@echo off

set ANT_HOME=C:\java\Apache\NetBeans\extide\ant\
set JAVA_HOME=C:\java\jdk\
set PATH=%PATH%;%ANT_HOME%;%JAVA_HOME%;"C:\java\Apache\NetBeans\extide\ant\bin\";

call ant -f C:\\projects\\negocio_delphi\\modulos\\cursores -Dnb.internal.action.name=rebuild clean jar     

call pause

call ant -f C:\\projects\\negocio\\modulos\\util -Dnb.internal.action.name=rebuild -DforceRedeploy=false -Dbrowser.context=C:\\projects\\negocio\\modulos\\util clean dist

call pause

call ant -f C:\\projects\\negocio_delphi\\modulos\\empresa -Dnb.internal.action.name=rebuild -DforceRedeploy=false -Dbrowser.context=C:\\projects\\negocio_delphi\\modulos\\empresa clean dist

call pause