#!/bin/bash
# build
#
#	Purpose:
#		
#	Description:
#		
#	History:
#		Wed Oct 18 11:34:55	 2017, Created by r<PERSON>yhaung
#
#Copyright (C) 2017 Potix Corporation. All Rights Reserved.
#

group="org.zkoss.theme"
install=false # -i Install the theme into Maven local repo
update=false  # -u Update git submodule first
source=false  # -s Generate source archives for publishing
allthemes=    # -t Build themes only, separeted by space (ex: a b c)
doclean=false # -c Do `mvn clean` first
pack=false    # -p Build theme-pack (Themes all-in-one jar)

while getopts 'iust:cp' flag; do
  case "${flag}" in
    i) install=true ;;
    u) update=true ;;
    s) source=true ;;
    t) allthemes=${OPTARG} ;;
    c) doclean=true ;;
    p) pack=true ;;
    *) echo "Unexpected option ${flag}" ;;
  esac
done

source ./yaml.sh
# parse_yaml build.projects.yml && echo
create_variables build.projects.yml

if [ "$allthemes" = "" ]
then
    # Themes to process (e.g. allthemes=sapphire silvertail)
    allthemes="${build_themes[@]}"
fi

# UpVer
zkver=$(head -1 version)
themever=$(tail -1 version)
datever=`date +%Y%m%d`
mvnver=$themever.FL.$datever
#sed -ie "s/zk.version>.*<\/zk.version/zk.version>$zkver.FL.$datever<\/zk.version/" pom.xml
#sed -i "1,/version>.*<\/version/s/version>.*<\/version/version>$mvnver<\/version/" pom.xml

buildVer=$(mvn help:evaluate --no-snapshot-updates -Dexpression=project.version | grep -e '^[^\[]')

if [ "$update" = true ]
then
    # Git submodule (ensure its contents are newest)
    git submodule foreach --recursive git reset --hard
    git submodule update --init --remote
fi
rm -rf src/archive/web
cp -R template/src/archive/web src/archive/

[ "$doclean" = true ] && mvn clean

# For each theme
for theme in $allthemes
do
    if [ -f "projects/${theme}.less" ]
    then
        name_key="themes_${theme}_name"
        display_key="themes_${theme}_display"

        if [ "$source" = true ]
        then
            echo "Generateing source archive of $theme..."
            ./generate-source.sh $theme
            echo "Building $theme..."
            mvn -Dtheme.name="${!name_key}" -Dtheme.display="${!display_key}" package 
        else
            echo "Building $theme..."
            mvn -Dtheme.name="${!name_key}" -Dtheme.display="${!display_key}" \
                -Dassembly.skipAssembly=true package
        fi

        if [ "$install" = true ]
        then
            echo "Installing $theme..."
            mvn -Dfile="target/${theme}-${buildVer}.jar" \
                -DgroupId=${group} -DartifactId=${theme} \
                -Dversion=${buildVer} -Dpackaging=jar install:install-file
        fi
    fi
done

# Theme pack
if [ "$pack" = true ]
then
    packName=theme-pack
    packDir=target/${packName}
    rm -rf $packDir && mkdir -p $packDir

    # Extract all themes
    cd $packDir
    find ../ -type f -name "*-${buildVer}.jar" -exec jar -xf {} \;

    # Find themes
    declare -a THEMES
    for d in web/*
    do
        [ -d "$d" ] || continue
        THEMES[${#THEMES[@]}+1]=$(basename "$d")
    done
    echo "Found themes: ${THEMES[@]}"

    if [ ${#THEMES[@]} -eq 0 ]
    then
        echo "ERROR: No theme found. Abort."
        exit 1
    fi

    # Edit ZK metainfo
    # config.xml
    sed -i "s/config-name>.*<\/config-name/config-name>${packName}<\/config-name/" metainfo/zk/config.xml

    function theme_listener(){
        echo "\t<listener>\\\n\t\t<listener-class>org.zkoss.theme.$1.ThemeWebAppInit</listener-class>\\\n\t</listener>"
    }

    sed -i '/<listener>/{:a;N;/<\/listener>/!ba};/<listener-class/d' metainfo/zk/config.xml
    for theme in ${THEMES[@]}
    do
        C=$(theme_listener $theme | sed 's/\//\\\//g')
        sed -i "/<\/config>/ s/.*/${C}\n&/" metainfo/zk/config.xml
        echo "Adding $theme listener..."
    done

    # lang-addon.xml
    sed -i -e "s/addon-name>.*<\/addon-name/addon-name>${packName}<\/addon-name/" metainfo/zk/lang-addon.xml

    # Repack
    jar -cf "../${packName}-${buildVer}.jar" .
    echo "Pack completed."

    theme=theme-pack
    cd ../../

    if [ "$source" = true ]
    then
        echo "Generateing source archive of ${theme}..."
        ./generate-source.sh

        echo "Building ${theme}..."
        # generate sources.jar
        mvn -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/sources.xml \
            -DfinalName=${theme}-$buildVer assembly:single

        # generate pom.xml + bundle.jar
        mvn -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/bundle.xml \
            -DfinalName=${theme}-$buildVer assembly:single

        # generate bin.zip, src.zip
        mvn -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/src.xml \
            -DfinalName=${theme}-src-$buildVer assembly:single
        mvn -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/bin.xml \
            -DfinalName=${theme}-bin-$buildVer assembly:single
        mv target/${theme}-src-$buildVer-src.zip target/${theme}-src-$buildVer.zip
        mv target/${theme}-bin-$buildVer-bin.zip target/${theme}-bin-$buildVer.zip
    fi

    if [ "$install" = true ]
    then
        echo "Installing ${theme}..."
        mvn -Dfile="target/${theme}-${buildVer}.jar" \
            -DgroupId=${group} -DartifactId=${theme} \
            -Dversion=${buildVer} -Dpackaging=jar install:install-file
    fi
fi
