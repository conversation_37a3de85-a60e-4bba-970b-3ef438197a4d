<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>___GROUP_ID___</groupId>
	<artifactId>___ARTIFACT_ID___</artifactId>
	<version>___VERSION___</version>
	<properties>
		<zk.version>*******-Eval</zk.version>
		<maven.build.timestamp.format>yyyy-MM-dd</maven.build.timestamp.format>
		<packname>-${project.version}</packname>
	</properties>
	<packaging>jar</packaging>
	<name>___ARTIFACT_ID___</name>
	<scm>
		<url>http://mavensync.zkoss.org/maven2</url>
	</scm>
	<description>___DISPLAY_NAME___</description>
	<licenses>
		<license>
			<name>GNU LESSER GENERAL PUBLIC LICENSE, Version 3</name>
			<url>http://www.gnu.org/licenses/gpl.html</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<developers>
		<developer>
			<id>zkteam</id>
			<name>ZK Team</name>
			<email><EMAIL></email>
			<url>http://www.zkoss.org</url>
			<organization>Potix</organization>
			<organizationUrl>http://www.zkoss.org</organizationUrl>
			<roles>
				<role>architect</role>
				<role>developer</role>
			</roles>
			<timezone>8</timezone>
			<properties>
				<picUrl>http://www.zkoss.org</picUrl>
			</properties>
		</developer>
	</developers>
	<repositories>
		<repository>
			<id>ZK CE</id>
			<name>ZK CE Repository</name>
			<url>http://mavensync.zkoss.org/maven2</url>
		</repository>
		<repository>
			<id>ZK PE EE Evaluation</id>
			<url>http://mavensync.zkoss.org/eval/</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>zkmaven</id>
			<name>ZK Maven Plugin Repository</name>
			<url>http://mavensync.zkoss.org/maven2/</url>
		</pluginRepository>
	</pluginRepositories>
	<dependencies>
		<dependency>
			<groupId>org.zkoss.zk</groupId>
			<artifactId>zul</artifactId>
			<version>${zk.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.zkoss.zk</groupId>
			<artifactId>zkmax</artifactId>
			<version>${zk.version}</version>
			<optional>true</optional>
		</dependency>
	</dependencies>
	<build>
		<sourceDirectory>${project.basedir}/src/</sourceDirectory>
		<resources>
			<resource>
				<directory>${project.basedir}/src/archive</directory>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.zkoss.maven</groupId>
				<artifactId>zkless-engine-maven-plugin</artifactId>
				<version>1.0.0</version>
				<executions>
					<execution>
						<id>compile-less</id>
						<goals>
							<goal>lessc</goal>
						</goals>
						<configuration>
							<sourceDirectory>${project.basedir}/src/archive</sourceDirectory>
							<outputDirectory>${project.basedir}/target/classes</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Compile java -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.5</source>
					<target>1.5</target>
				</configuration>
			</plugin>
			<!-- Build jar -->
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>2.3.7</version>
				<extensions>true</extensions>
				<configuration>
					<excludeDependencies>*;scope=provided|compile|runtime</excludeDependencies>
					<instructions>
						<_include>${project.basedir}/src/archive/META-INF/MANIFEST.MF</_include>
						<Bundle-Version>${project.version}</Bundle-Version>
						<Bundle-Name>${project.groupId}.${project.artifactId}</Bundle-Name>
						<Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
						<Export-Package>*</Export-Package>
						<Import-Package>*</Import-Package>
					</instructions>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.2</version>
				<executions>
					<execution>
						<id>bin</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<appendAssemblyId>false</appendAssemblyId>
							<descriptors>
								<descriptor>assembly/zip.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>bundle</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>assembly/bundle.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
