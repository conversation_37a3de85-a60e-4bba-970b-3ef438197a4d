/* FONT PATH
 * -------------------------- */

@font-face {
  font-family: 'ZK85Icons';
  src: url(${c:encodeURL('~./zul/less/font/ZK85Icons.eot?v=@{fa-version}')});
  src: url(${c:encodeURL('~./zul/less/font/ZK85Icons.eot?#iefix&v=@{fa-version}')}) format('embedded-opentype'),
    url(${c:encodeURL('~./zul/less/font/ZK85Icons.woff?v=@{fa-version}')}) format('woff'),
    url(${c:encodeURL('~./zul/less/font/ZK85Icons.ttf?v=@{fa-version}')}) format('truetype'),
    url(${c:encodeURL('~./zul/less/font/ZK85Icons.svg?v=@{fa-version}#ZK85Icons')}) format('svg');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'FontAwesome';
  src: url(${c:encodeURL('~./zul/less/font/fontawesome-webfont.eot?v=@{fa-version}')});
  src: url(${c:encodeURL('~./zul/less/font/fontawesome-webfont.eot?#iefix&v=@{fa-version}')}) format('embedded-opentype'),
    url(${c:encodeURL('~./zul/less/font/fontawesome-webfont.woff2?v=@{fa-version}')}) format('woff2'),
    url(${c:encodeURL('~./zul/less/font/fontawesome-webfont.woff?v=@{fa-version}')}) format('woff'),
    url(${c:encodeURL('~./zul/less/font/fontawesome-webfont.ttf?v=@{fa-version}')}) format('truetype'),
    url(${c:encodeURL('~./zul/less/font/fontawesome-webfont.svg?v=@{fa-version}#fontawesomeregular')}) format('svg');
//  src: url(${c:encodeURL('~./zul/less/font/FontAwesome.otf')}) format('opentype'); // used when developing fonts

  font-weight: normal;
  font-style: normal;
}
