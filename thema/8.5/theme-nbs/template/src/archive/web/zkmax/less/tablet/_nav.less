.z-navbar-vertical {
	max-width: 36%;
}

.z-navbar-horizontal {
	overflow-x: auto;
	position: static;
}

.z-nav-content, .z-navitem-content {
	padding: 4px 8px;
	height: 32px;
}

.z-nav-text, .z-navitem-text {
	font-size: 17px;
}

.z-nav-text-popup, .z-navitem-text-popup {
	padding: 0 12px;
}

.z-nav-info {
	font-size: 11px;
}

.z-nav-image, .z-navitem-image,
.z-nav i, .z-navitem i {
	font-size: 24px;
	.size(24px, 24px);
}

// sub menu
.z-navbar > ul ul {
	.z-nav-image, .z-navitem-image, i {
		font-size: 22px;
		.size(22px, 22px);
	}
	.z-nav-text, .z-navitem-text {
		font-size: 15px;
	}
}
.z-nav-popup {
	.z-nav-image, .z-navitem-image, i {
		font-size: 22px;
		.size(22px, 22px);
	}
	.z-nav-text, .z-navitem-text {
		font-size: 15px;
	}
}

