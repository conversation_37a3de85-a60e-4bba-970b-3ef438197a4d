.z-biglistbox {
	&-header-content {
		font-size: 15px;
	}
	&-body td {
		font-size: 15px;
	}
	&-outer {
		margin: 0 22px 22px 0;
	}
	&-verticalbar-tick {
		.size(14px, 100%);
		border: 0;
		.borderRadius(0);
		background: transparent;

		&:hover, &:active {
			background: 0;
			border: 0;
		}
		&:before {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			display: block;
			.size(14px, 22px);
			background: #fff;
			border: 1px solid @baseBorderColor;
			.borderRadius(2px);
		}
		&:hover:before {
			background-color: @biglistboxScrollBarHoverBackgroundColor;
			border: 1px solid @biglistboxScrollBarHoverBorderColor;
		}
		&:active:before {
			background-color: @biglistboxScrollBarActiveBackgroundColor;
			border: 1px solid @biglistboxScrollBarActiveBorderColor;
		}
		&:after {
			content: '\e910';
			position: absolute;
			bottom: 1px;
			display: block;
			left: -3px;
			font-size: 20px;
		}
	}
	// Wscroll Vertical
	&-wscroll-vertical {
		width: 22px;
		right: -22px;
		.boxShadow('inset 1px 1px 7px rgba(210, 210, 210, 0.75), inset -1px -1px 7px rgba(210, 210, 210, 0.75)');
		
		.z-biglistbox-wscroll-drag {
			.size(22px, 168px);
			.z-biglistbox-wscroll-home,
			.z-biglistbox-wscroll-up,
			.z-biglistbox-wscroll-down,
			.z-biglistbox-wscroll-end,
			.z-biglistbox-wscroll-body {
				font-size: 20px;
				line-height: 22px;
				text-align: center;
			}
			.z-biglistbox-wscroll-home,
			.z-biglistbox-wscroll-up,
			.z-biglistbox-wscroll-down,
			.z-biglistbox-wscroll-end {
				.size(100%, 28px);
			}
			.z-biglistbox-wscroll-body {
				.size(100%, 56px);
				top: 56px;
				&:before {
					left: 0;
					top: 20px;
				}
			}
			.z-biglistbox-wscroll-up {
				top: 28px;
			}
			.z-biglistbox-wscroll-down {
				bottom: 28px;
			}
		}
		.z-biglistbox-wscroll-pos {
			.size(22px, 168px);
			.borderRadius(2em);
		}
		.z-biglistbox-wscroll-endbar {
			.size(22px, 8px);
		}
	}
	// Wscroll Horizontal
	&-wscroll-horizontal {
		height: 22px;
		bottom: -22px;
		.boxShadow('inset 1px 1px 7px rgba(210, 210, 210, 0.75), inset -1px -1px 7px rgba(210, 210, 210, 0.75)');
		
		.z-biglistbox-wscroll-drag {
			.size(168px, 22px);
			.z-biglistbox-wscroll-home,
			.z-biglistbox-wscroll-up,
			.z-biglistbox-wscroll-down,
			.z-biglistbox-wscroll-end,
			.z-biglistbox-wscroll-body {
				font-size: 20px;
				line-height: 22px;
				text-align: center;
			}
			.z-biglistbox-wscroll-home,
			.z-biglistbox-wscroll-up,
			.z-biglistbox-wscroll-down,
			.z-biglistbox-wscroll-end {
				.size(28px, 100%);
			}
			.z-biglistbox-wscroll-body {
				.size(56px, 100%);
				left: 56px;
				&:before {
					left: 20px;
				}
			}
			.z-biglistbox-wscroll-up {
				left: 28px;
			}
			.z-biglistbox-wscroll-down {
				right: 28px;
			}
		}
		.z-biglistbox-wscroll-pos {
			.size(168px, 22px);
			.borderRadius(2em);
		}
		.z-biglistbox-wscroll-endbar {
			.size(8px, 22px);
			background: @biglistboxScrollBarEndBarBackgroundColor;
		}
	}
}

.z-biglistbox-wscroll {
	.clkStyle() {
		color: #FFF;
		background-color: @biglistboxScrollBarActiveBackgroundColor;
		border: 1px solid @biglistboxScrollBarActiveBorderColor;
	}
	&-home {
		&-clk & { .clkStyle(); }
	}
	&-up {
		&-clk & { .clkStyle(); }
	}
	&-down {
		&-clk & { .clkStyle(); }
	}
	&-end {
		&-clk & { .clkStyle(); }
	}
	&-body {
		&-clk & { .clkStyle(); }
	}
}
