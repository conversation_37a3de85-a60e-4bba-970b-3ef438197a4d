.z-calendar {
	&-text {
		font-size: 16px;
	}
	&-icon {
		font-size: 20px;
	}
	& th {
		font-size: 14px;
		.size(36px, 40px);
	}
	&-cell {
		font-size: 14px;
		.size(36px, 36px);
		&:hover {
			color: initial;
			background: initial;
		}
	}
	&-selected:hover {
		color: @calendarSelectedHoverColor;
		background: @calendarSelectedHoverBackgroundColor;
	}
	&-header:first-child {
		line-height: 38px;
	}
	&-decade,
	&-month,
	&-year {
		.z-calendar-cell {
			.size(63px, 48px);
		}
	}
	&-wk& {
		.z-calendar-decade, .z-calendar-month, .z-calendar-year {
			.z-calendar-cell {
				.size(72px, 48px);
			}
		}
	}
	&-today {
		margin: 8px 10px 0;
	}
}
.z-calendar-today .z-calendar-title {
	font-size: 15px;
}

//Calendar wheel
.z-datebox-popup .z-calendar {
	border: 0px;
	min-width: 100px;
}
.z-calendar-wheel {
	&-date {
		.boxOrientHorFlex();
		width: 100%;
		position: relative;
		background: @baseBackgroundColor;
		margin-right: 2px;
	}
	&-cave {
		position: relative;
	}
	&-body {
		.boxOrientHor();
		width: 100%;
	}
	&-line {
		.size(100%, 0);
		position: absolute;
		top: 50%;
		z-index: 1;
	}
	&-list {
		.applyCSS3(box-flex, 1);
		color: @textColorDefault;
		background: @baseBackgroundColor;
		height: 120px;
		margin: 0 1px 0 0;
		position: relative;
		overflow: hidden;
		
		ul {
			width: 100%;
			margin: 0;
			padding: 0;
			position: relative;
			list-style: none;
			z-index: 2;
		}
		
		li {
			font-size: 17px;
			display: block;
			height: 40px;
			margin: 0;
			padding: 0;
			line-height: 40px;
			opacity: 0.3;
			text-align: center;
			white-space: nowrap;
			list-style: none;
		}
	}
	&-footer {
		height: 50px;
		padding: 5px 0;
		clear: both;
	}
	&-button {
		.fontStyle(@baseContentFontFamily, 15px, normal, #000000);
		width: 45%;
		border: none;
		.borderRadius(@baseBorderRadius);
		padding: 8px 0;
		.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
	}
	&-left {
		float: left;
		background: @colorPrimary;
		color: @textColorDefault3;
		border: 1px solid transparent;
	}
	&-right {
		float: right;
		background: @baseBackgroundColor;
		color: @textColorDefault;
		border: 1px solid @colorGreyLight;
	}
}
li.z-calendar-wheel-list-selected {
	opacity: 1;
}
