.z-column-content, .z-row-content,
.z-group-content, .z-groupfoot-content, .z-footer-content {
	font-size: 15px;
	padding: 10px 12px;
}

.z-auxheader-content {
	font-size: 15px;
	padding: 8px 12px;
}

.z-grid-body .z-grid-emptybody td {
	font-size: 15px;
	padding: 10px 12px;
}

.z-columns-menupopup .z-column-content {
	padding-right: 38px;
}

.z-column-button {
	.size(38px, 38px);
	line-height: 38px;
	font-size: 22px;
	display: block;
}

// Detail
.z-detail {
	.size(22px, 22px);
	&-icon {
		font-size: 22px;
	}
	&-open &-icon {
		line-height: 22px;
	}
	&-content:before {
		margin: 0 0 10px 12px;
	}
}

// Grouping
.z-group-inner .z-group-content, .z-group-inner .z-cell {
	padding: 8px 12px;
}
.z-group-icon {
	.size(22px, 22px);
	font-size: 22px;
	line-height: 22px;
}
.z-groupfoot-content {
	padding: 8px 12px;
}
