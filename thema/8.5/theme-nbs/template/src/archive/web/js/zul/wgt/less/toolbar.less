@import "~./zul/less/_header.less";

.z-toolbar {
	display: block;
	border: 0;
	padding: 6px 16px;
	background: @baseBackgroundColor;
	position: relative;
}

// Toolbar in Tabbox
.z-toolbar.z-toolbar-tabs {
	background-color: @tabboxToolbarBackgroundColor;
	position: absolute;
	right: 0;
	top: 0;
	overflow: hidden;
	z-index: 1;
	min-height: 48px;
}

.z-caption .z-toolbar {
	background: none;
	border: 0;
}

// toolbar horizontal alignment
.z-toolbar-start {
	float: left;
	clear: none;
}
.z-toolbar-center {
	text-align: center;
	margin: 0 auto;
}
.z-toolbar-end {
	float: right;
	clear: none;
}
// Toolbar Panel Mold
.z-toolbar-panel {
	background-color: transparent;
}

.z-toolbar-panel .z-toolbar-horizontal,
.z-toolbar-panel .z-toolbar-vertical {
	border: 0;
	padding: 0;
}
.z-toolbar-panel .z-toolbar-horizontal {
	padding-left: 8px;
}
.z-toolbar-panel .z-toolbar-vertical {
	padding-bottom: 8px;
}

// Toolbarbutton
.z-toolbarbutton {
	display: inline-block;
	position: relative;
	cursor: pointer;

	&-content {
		.fontStyle(@baseTitleFontFamily, @fontSizeLarge, normal, @toolbarButtonColor);
		background-color: @toolbarButtonBackgroundColor;
		border: 2px solid transparent;
		.borderRadius(@inputBorderRadius);
		padding: @toolbarButtonPadding;
		line-height: @fontSizeLarge;
		display: inline-block;
		vertical-align: middle;
		position: relative;
		white-space: nowrap;
		> img, > i {
			margin-right: 4px;
			max-height: 20px;
			max-width: 20px;
			font-size: 20px;
			vertical-align: middle;
		}

		.z-toolbarbutton:hover & {
			color: @buttonHoverColor;
			border-color: @buttonHoverBorderColor;
			background-color: @buttonHoverBackgroundColor;
		}
		.z-toolbarbutton:active & {
			color: @buttonActiveColor;
			border-color: @buttonActiveBorderColor;
			background-color: @buttonActiveBackgroundColor;
		}
		.z-toolbarbutton:focus & {
			color: @buttonFocusColor;
			border-color: @buttonFocusBorderColor;
			background-color: @buttonFocusBackgroundColor;
		}
		.z-toolbarbutton[disabled] & {
			color: @buttonDisableColor !important;
			border-color: @buttonDisableBorderColor;
			background-color: @buttonDisableBackgroundColor;
			cursor: default !important;
		}
		.z-toolbarbutton-checked & {
			color: @toolbarButtonCheckedColor;
			background-color: @toolbarButtonCheckedBackgroundColor;
		}
	}
}

.z-toolbar-content > * {
	margin-left: 8px;
	&:first-child {
		margin-left: 0;
	}
}

.z-toolbar-vertical {
	.z-toolbar-content > * {
		margin-left: 0;
	}
}

