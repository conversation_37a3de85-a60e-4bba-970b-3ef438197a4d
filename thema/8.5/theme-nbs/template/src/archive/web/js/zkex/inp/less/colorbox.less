@import "~./zul/less/_header.less";

.z-colorbox {
	.displaySize(inline-block, 56px, 35px);
	border: 1px solid @baseBorderColor;
	.borderRadius(@baseBorderRadius);
	margin: 0 2px;
	padding: 4px;
	background: @inputBackgroundColor;
	vertical-align: middle;
	position: relative;
	overflow: hidden;
	cursor: pointer;
	
	&-current {
		.displaySize(inline-block, 100%, 100%);
		border: 1px solid @baseBorderColor;
		position: relative;
	}
	
	&-button {
		font-size: @baseFontSize;
		.displaySize(inline-block, 14px, 14px);
		line-height: normal;
		background: @inputBackgroundColor;
		position: absolute;
		bottom: 1px;
		right: 0;
		overflow: hidden;
	}
	
	&-icon {
		.iconFontStyle(@baseFontSize, @textColorDefault);
		.size(10px, 8px);
		position: relative;
		top: -3px;
		left: -2px;
	}
	
	&-disabled,
	&-disabled * {
		color: @disabledColor !important;
		.opacity(@disabledOpacity);
		cursor: default !important;
	}
}
// Popup shadow
.z-colorbox-shadow,
.z-menu-shadow {
	.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
}
.z-colorbox-popup,
.z-menu-popup {
	display: none;
	position: absolute;
	overflow: auto;
	z-index: @basePopupZIndex;
	border: 1px solid @popupBorderColor;
	.borderRadius(@baseBorderRadius);
	padding: 8px;
	background-color: @popupBackgroundColor;
}

// Colorpicker
.z-colorpicker {
	.size(310px, 460px);
	position: relative;
	overflow: hidden;
	padding: 8px;

	&-main {
		position: relative;
		height: 256px;
		top: 50px;
	}

	&-info {
		position: relative;
		margin-top: 16px;
		height: 122px;
		top: 50px;
	}

	&-gradient {
		.size(256px, 256px);
		border: 1px solid @baseBorderColor;
		position: absolute;
		left: 0;
		top: 0;
		cursor: crosshair;
	}
	
	&-overlay {
		.size(256px, 256px);
		.encodeURL(background-image, '~./zkex/img/colorbox/colorpicker_gradient.png');
	}
	
	&-bar {
		.size(12px, 256px);
		border: 1px solid @baseBorderColor;
		.encodeURL(background-image, '~./zkex/img/colorbox/colorpicker_hue.png');
		position: absolute;
		left: 7px;
		overflow: hidden;
		cursor: row-resize;
	}
	
	&-circle {
		.size(11px, 11px);
		margin: -5px 0 0 -5px;
		.encodeURL(background-image, '~./zkex/img/colorbox/colorpicker_select.gif');
		position: absolute;
		top: 0;
		left: 0;
		overflow: hidden;
	}
	
	&-hue {
		.size(27px, 256px);
		position: absolute;
		top: 0;
		right: 0;
	}
	
	&-arrows {
		.size(27px, 9px);
		margin: -4px 0 0 0;
		.encodeURL(background-image, '~./zkex/img/colorbox/colorpicker_arrows.gif');
		position: absolute;
		left: 0;
		overflow: hidden;
		cursor: row-resize;
	}
	
	&-color {
		border: 1px solid @baseBorderColor;
		background: transparent;
		position: absolute;
		.size(72px, 70px);
		.borderRadius(2px);
		top: 0;
		right: 0;
		padding: 2px;
	}
	
	&-newcolor {
		.size(66px, 32px);
		border: 1px solid @baseBorderColor;
		position: relative;
	}
	
	&-current {
		.size(66px, 32px);
		border: 1px solid @baseBorderColor;
		position: relative;
	}
	
	&-rgb, &-hsv {
		position: absolute;
		left: 0;
		.z-colorpicker-input {
			width: 48px;
			margin-left: 8px;
			margin-right: 0;
		}
	}

	&-rgb {top: 0;}
	&-hsv {top: 43px;}

	&-text,
	&-input {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
	}

	&-text {
		display: inline-block;
		width: 35px;
	}
	
	&-input {
		height: 35px;
		text-align: center;
	}
	
	&-hex {
		position: absolute;
		top: 86px;
		left: 0;
		
		.z-colorpicker-input {
			width: 160px;
			margin-left: 8px;
		}
	}
	
	&-button {
		position: absolute;
		top: 84px;
		right: 0;
		cursor: pointer;
		width: 72px;
	}

	// old theme compatible
	&-r, &-g, &-b,
	&-h, &-s, &-v {
		display: inline;
	}

	&-r-text, &-g-text, &-b-text,
	&-h-text, &-s-text, &-v-text {
		display: none;
	}
}
// Color Palette
.z-colorpalette {
	.size(340px, 300px);
	padding: 8px;

	&-head {
		position: relative;
		height: 50px;
	}

	&-newcolor {
		.size(35px, 35px);
		.borderRadius(2px);
		border: 1px solid @baseBorderColor;
		position: absolute;
		right: 100px;
	}
	
	&-input,
	&-button {
		position: absolute;
		top: 0;
		right: 0;
	}
	
	&-input {
		.size(96px, 35px);
		margin: 0;
	}
	
	&-color {
		.displaySize(inline-block, 16px, 16px);
		border: 1px solid @baseBorderColor;
		.borderRadius(2px);
		cursor: pointer;
		float: left;
		margin: 1px;

		&:hover {
			border: 1px solid #000000;
		}
	}
	
	&-selected {
		border: 1px solid #000000;
	}
}
.z-colorbox-paletteicon,
.z-menu-paletteicon,
.z-colorbox-pickericon,
.z-menu-pickericon {
	.size(44px, 32px);
	position: absolute;
	cursor: pointer;
	z-index: 10;
	color: @toolbarButtonColor;
	font-size: @fontSizeXLarge;
	.borderRadius(@inputBorderRadius);
	padding: @toolbarButtonPadding;
	&:hover {
		color: @buttonHoverColor;
		border-color: @buttonHoverBorderColor;
		background-color: @buttonHoverBackgroundColor;
	}
	&:focus {
		color: @buttonFocusColor;
		border-color: @buttonFocusBorderColor;
		background-color: @buttonFocusBackgroundColor;
	}
}
.z-colorbox-paletteicon,
.z-menu-paletteicon {
	left: 16px;
	top: 16px;
	&:before {
		.baseIconFont();
		content: '\e902'; // ZK85Icons: icon-color-grid
	}
}
.z-colorbox-pickericon,
.z-menu-pickericon {
	left: 68px;
	top: 16px;
	&:before {
		.baseIconFont();
		content: '\e900'; // ZK85Icons: icon-color-wheel
	}
}

.z-colorpalette-popup .z-colorbox-paletteicon,
.z-colorpalette-popup .z-menu-paletteicon {
	// copied form toolbar.less: z-toolbarbutton-checked
	color: @toolbarButtonCheckedColor;
	background-color: @toolbarButtonCheckedBackgroundColor;
}
.z-colorpicker-popup .z-colorbox-pickericon,
.z-colorpicker-popup .z-menu-pickericon {
	color: @toolbarButtonCheckedColor;
	background-color: @toolbarButtonCheckedBackgroundColor;
}
// Menu color block
.z-menu-image.z-colorbox-color {
	border-radius: 2px;
	border: 1px solid @baseBorderColor;
}
