@import "~./zul/less/_header.less";

.z-chosenbox {
	display: inline-block;
	border: 1px solid @inputBorderColor;
	.borderRadius(@baseBorderRadius);
	line-height: normal;
	background: @inputBackgroundColor;
	overflow: hidden;
	padding: 0 0 4px 0;
	min-height: @baseBarHeight;

	&-focus {
		border-color: @inputFocusBorderColor;
	}

	&-item {
		display: inline-block;
		border: 1px solid @chosenboxItemBorderColor;
		.borderRadius(@baseBorderRadius);
		margin: 4px 0 0 4px;
		padding: 4px 8px;
		background-color: @chosenboxItemBackgroundColor;
		vertical-align: middle;
		position: relative;
		white-space: nowrap;
		cursor: pointer;

		&-content {
			.fontStyle(@baseContentFontFamily, @chosenboxItemSize, normal, @chosenboxItemColor);
			display: inline-block;
			margin-right: @chosenboxIconSize + 6;
			float: left;
		}

		&-focus {
			background-color: @chosenboxItemFocusBackgroundColor;
		}
	}

	&-button {
		.iconFontStyle(@chosenboxIconSize, @chosenboxItemColor);
		.size(@chosenboxIconSize, @chosenboxIconSize);
		line-height: @chosenboxIconSize;
		text-align: center;
		position: absolute;
		right: 8px;
		top: 4px;
	}

	&-input {
		.fontStyle(@baseContentFontFamily, @inputTextSize, normal, @inputColor);
		.displaySize(inline-block, 30px, 30px);
		border: 0 !important;
		padding: 1px 4px 2px 8px;
		background: transparent !important;
		outline: 0;
		.boxShadow('none');
	}

	&-disabled {
		border-color: @chosenboxItemDisabledBorderColor;
		background: @disabledBackgroundColor;

		.z-chosenbox-item {
			cursor: default;
			border-color: @chosenboxItemDisabledBorderColor;
			background-color: @chosenboxItemDisabledBackgroundColor;
		}

		.z-chosenbox-item-content {
			color: @chosenboxItemDisabledColor;
			margin-right: 0;
		}

		.z-chosenbox-button {
			display: none;
		}
	}

	&-textcontent {
		font-family: @baseContentFontFamily;
		font-size: @fontSizeMedium;
		display: none;
		white-space: nowrap;
	}

	&-popup {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @comboPopupItemColor);
		display: block;
		border: 1px solid @comboPopupBorderColor;
		.borderRadius(@baseBorderRadius);
		padding: 4px 8px;
		background: @popupBackgroundColor;
		position: absolute;
		overflow: auto;

		&-hidden {
			display: none;
		}
	}

	&-shadow {
		.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
	}

	&-option {
		cursor: pointer;
		padding: 4px 8px;
		min-height: 20px;
		.borderRadius(@baseBorderRadius);

		&:hover, &-hover {
			background: @chosenboxPopupHoverBackgroundColor;
		}
	}

	&-empty {
		color: @disabledColor;
		padding: 4px 8px;
		min-height: 20px;

		&-creatable {
			color: @inputColor;
			padding-top: 2px;
			cursor: pointer;
		}
	}

	&-create {
		.iconFontStyle(@chosenboxCreateIconSize, @chosenboxCreateIconColor);
		.size(@chosenboxCreateIconSize, @chosenboxCreateIconSize);
		margin-right: 2px;
		&:before {
			content: '\f067'; // z-icon-plus
		}
	}
}
