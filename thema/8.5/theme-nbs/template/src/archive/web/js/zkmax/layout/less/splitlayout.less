@import "~./zul/less/_header.less";

.z-splitlayout {
	white-space: nowrap;
	overflow: hidden;
	&-cave-top, &-cave-bottom {
		position: relative;
		width: 100%;
		overflow: hidden;
		display: block;
	}
	&-cave-left,
	&-cave-right {
		height: 100%;
		display: inline-block;
		position: relative;
		white-space: normal;
		vertical-align: top;
		overflow: hidden;
	}
	&-splitter {
		border: 1px solid @splitterBorderColor;
		cursor: default;
		background-color: @splitterBackgroundColor;

		&:hover {
			background-color: @splitterHoverBackgroundColor;
			.z-splitlayout-splitter-button {
				color: extract(@splitterButtonTextColors, 2);
			}
		}

		&-horizontal {
			display: inline-block;
			width: @splitterSize;
			height: 100%;
			max-width: @splitterSize;
			padding: 0;
			margin: 0;
			border-width: 0 1px;
			overflow: hidden;
		}
		&-horizontal > &-button {
			width: @splitterSize;
			height: 30px;
			border-width: 1px 0;
		}
		&-horizontal &-icon {
			position: absolute;
			top: 8px;
			left: -3px
		}
		&-horizontal &-icon.z-icon-ellipsis-v {
			top: -21px;
			left: 2px;
			visibility: hidden;
		}
		&-horizontal &-icon.z-icon-ellipsis-v ~ &-icon.z-icon-ellipsis-v {
			top: 39px
		}
		&-vertical {
			width: 100%;
			height: @splitterSize;
			max-height: @splitterSize;
			padding: 0;
			border-width: 1px 0;
			overflow: hidden;
		}
		&-vertical > &-button {
			width: 30px;
			height: @splitterSize;
			border-width: 0 1px
		}
		&-vertical &-icon {
			line-height: normal;
			position: absolute;
			top: -4px;
			left: 9px
		}
		&-vertical &-icon.z-icon-ellipsis-h {
			top: -2px;
			left: -16px;
			visibility: hidden;
		}
		&-vertical &-icon.z-icon-ellipsis-h ~ &-icon.z-icon-ellipsis-h {
			left: 36px;
		}
		&-button {
			.iconFontStyle(@splitterButtonTextSize, extract(@splitterButtonTextColors, 1));
			display: inline-block;
			position: relative;
			vertical-align: top;
			cursor: pointer;
		}
		&-button-disabled {
			border: 0;
		}
		&-horizontal&-draggable {
			cursor: col-resize;
		}
		&-vertical&-draggable {
			cursor: row-resize;
		}
		&-nosplitter {
			cursor: default
		}
	}
}
