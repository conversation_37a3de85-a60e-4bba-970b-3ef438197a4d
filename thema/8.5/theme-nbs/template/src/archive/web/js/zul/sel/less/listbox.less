@import "~./zul/less/_header.less";

//reset table
.resetTable() {
	table {
		border-spacing: 0;
		th, td {
			background-clip: padding-box;
			padding: 0;
		}
		th {
			text-align: inherit;
		}
	}
}

// checkbox style
.checkable() {
	.displaySize(inline-block, 20px, 20px);
	.iconFontStyle(@checkedIconSize, @checkedColor);
	border: 1px solid @checkedBorderColor;
	background: @checkedBackgroundColor;
	vertical-align: text-top;
	margin-right: 8px;
	.borderRadius(@baseBorderRadius);
}

//listbox
.z-listbox {
	background: @meshBackgroundColor;
	border: 1px solid @baseBorderColor;
	overflow: hidden;
	zoom: 1;
	//listbox header
	&-header {
		width: 100%;
		background: @meshTitleBackgroundColor;
		position: relative;
		overflow: hidden;
		.resetTable();
	}
	&-header-border {
		border-bottom: 1px solid @meshTitleBorderColor;
		margin-top: -1px;
		position: relative;
	}
	//listbox body
	&-body {
		position: relative;
		overflow: hidden;
		.resetTable();
		//ZK-3263: scroll issue (chrome scroll anchor bug)
		overflow-anchor: none;
	}
	&-body &-emptybody td {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @disabledColor);
		text-align: left;
		height: 1px;
	}
	&-body &-emptybody &-emptybody-content {
		background: @meshBackgroundColor;
		padding: 8px 8px;
	}
	//listbox footer
	&-footer {
		background: @meshFootBackgroundColor;
		border-top: 1px solid @baseBorderColor;
		overflow: hidden;
		.resetTable();

		.z-listfooter {
			background: @meshFootBackgroundColor;
		}
	}

	.z-listcell {
		background: @meshBackgroundColor;
	}

	&-odd > .z-listcell {
		background: @meshStripeBackgroundColor;
	}
}
//listhead
.z-listhead {
	th:first-child {
		border-left: none;
		// B50-3306729: the first header should have border-left when the first column is covered with other header
		&.z-listhead-border {
			border-left: 1px solid @meshTitleBorderColor;
		}
	}
	&-bar {
		border-left: 1px solid @meshTitleBorderColor;
		border-bottom: 1px solid @meshTitleBorderColor;
	}
}
.z-listheader {
	border-left: 1px solid @meshTitleBorderColor;
	border-bottom: 1px solid @meshTitleBorderColor;
	padding: 0;
	background: @meshTitleBackgroundColor;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	&-sort {
		.z-listheader-content {
			cursor: pointer;
		}
		.z-listheader-sorticon {
			.iconFontStyle(@fontSizeLarge, @meshTitleColor);
			position: absolute;
			top: -3px;
			left: 50%;
		}
		:active {
			background: @meshTitleActiveBackgroundColor;
		}
	}
	&-hover {
		background: @meshTitleHoverBackgroundColor;
		.z-listheader-button {
			display: block;
		}
	}
	&-visited {
		background: @meshTitleBackgroundColor;
		.z-listheader-button {
			background: @meshTitleActiveBackgroundColor;
		}
	}
	&-checkable {
		.checkable();
		
		.z-listheader-icon {
			display: none;
			cursor: default;
		}
		&.z-listheader-checked .z-listheader-icon {
			display: block;
			line-height: @checkedIconSize;
		}
	}
	&-button {
		.iconFontStyle(@fontSizeLarge, @meshTitleColor);
		display: none;
		.size(34px, 48px);
		line-height: 48px;
		text-align: center;
		position: absolute;
		top: 0;
		right: 0;
		text-decoration: none;
		cursor: pointer;
		.boxShadow('inset 1px 0 @{meshTitleBorderColor}');
		z-index: 15;
	}

	&-sizing,
	&-sizing .z-listheader-button,
	&-sizing.z-listheader-sort .z-listheader-content {
		cursor: col-resize;
	}
}
.z-listhead-menupopup .z-listheader-content {
	padding-right: 24px;
	text-overflow: ellipsis;
}

// ZK-2151: use strict selector to prevent nest problem
//list item and cell
.z-listitem {
	&:first-child {
		.z-listcell {
			border-top-width: 0;
		}
	}
	
	//list cell
	.z-listcell {
		border-top: 1px solid @meshContentBorderColor;
		overflow: hidden;
		cursor: pointer;
	}
	&:hover {
		> .z-listcell {
			background: @hoverBackgroundColor;
			> .z-listcell-content {
				color: @hoverColor;
			}
		}
	}
	//check mark
	&-checkable {
		.checkable();
		
		&.z-listitem-radio {
			.borderRadius(50%);
		}
		.z-listitem-icon {
			display: none;
			cursor: default;
		}
	}
	//selected list cell
	&.z-listitem-selected {
		> .z-listcell {
			background: @selectedBackgroundColor;
			position: relative;

			> .z-listcell-content {
				color: @selectedColor;
			}
		}
		&:hover {
			> .z-listcell {
				background: @selectedHoverBackgroundColor;
				position: relative;
			}
			.z-listcell-content {
				color: @selectedHoverColor;
			}
		}
	}
	&.z-listitem-selected.z-listitem-focus {
		> .z-listcell {
			background: @selectedFocusBackgroundColor;
			position: relative;

			> .z-listcell-content {
				color: @selectedFocusColor;
			}
		}
		&:hover {
			> .z-listcell {
				background: @selectedHoverBackgroundColor;
				position: relative;
			}
			.z-listcell-content {
				color: @selectedHoverColor;
			}
		}
	}
	&-focus {
		> .z-listcell {
			background: @meshContentFocusBackgroundColor;
			position: relative;
		}
		.z-listcell-content {
			color: @hoverColor;
		}
	}
	//selected check mark
	&-selected {
		> .z-listcell > .z-listcell-content 
			> .z-listitem-checkable .z-listitem-icon {
			display: block;
			line-height: @checkedIconSize;
			cursor: pointer;
			
			&.z-icon-check {} //for checkbox, use font-awesome
			&.z-icon-radio { //for radio
				.size(10px, 10px);
				.borderRadius(50%);
				background: @checkedColor;
				position: relative;
				top: 50%;
				left: 50%;
				.transform('translate(-50%, -50%)');
			}
		}
	}
	//disabled
	&.z-listitem-disabled {
		* {
			color: @disabledColor !important;
			cursor: default !important;
		}
		&:hover > .z-listcell {
			position: relative;
		}
		a, a:visited, a:hover {
			text-decoration: none;
		}
	}
	a, a:visited, a:hover {
		text-decoration: none;
	}
}

body:not(.gecko) {
	.z-listitem {
		&:hover {
			> .z-listitem {
				position: relative;
			}
		}
	}
}

//Group
.z-listgroup {
	&-inner {
		border-top: 1px solid @meshContentBorderColor;
		border-bottom: 1px solid @meshGroupBorderColor;
		background: @meshGroupBackgroundColor;
		position: relative;
		overflow: hidden;
		
		.z-listcell-content,
		.z-listgroup-content {
			padding: 8px 8px;
		}
	}
	&&-open &-inner {
		background: @meshGroupOpenBackgroundColor;
		border-bottom: @meshGroupOpenBorder;
		& .z-listgroup-icon,
		& .z-listcell-content {
			color: @meshGroupOpenColor;
		}
	}
	//check mark
	&-checkable {
		.checkable();
		
		.z-listgroup-icon {
			display: none;
			cursor: default;
		}
	}
	//selected check mark
	&-selected &-checkable {
		.z-listgroup-icon {
			.iconFontStyle(@fontSizeMedium, @checkedColor);
			display: block;
			padding-right: 2px;
			line-height: @baseLineHeight;
			cursor: pointer;
			
			&:hover {
				color: @checkedColor;
			}
		}
	}
	&-icon {
		.iconFontStyle(@fontSizeLarge, @meshGroupColor);
		.displaySize(inline-block, @baseIconWidth, @baseIconHeight);
		line-height: @baseIconHeight;
		text-align: center;
		vertical-align: text-top;
		position: relative;
		cursor: pointer;
		margin-right: 8px;
	}
}

//content
.z-listheader-content,
.z-listcell-content,
.z-listgroup-content,
.z-listgroupfoot-content,
.z-listfooter-content {
	.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
	line-height: 1.3em;
	overflow: hidden;
	padding: 8px 8px;
}
.z-listheader-content {
	color: @meshTitleColor;
	position: relative;
}
.z-listfooter-content {
	color: @textColorLight;
}
.z-listgroup .z-listcell-content {
	color: @meshGroupColor;
}
.z-listgroupfoot {
	&-inner {
		border-top: 1px solid @meshContentBorderColor;
	}
	& .z-listcell-content {
		color: @meshGroupFooterColor;
		padding: 8px 8px;
		.z-listgroup-open& {
			color: @meshGroupFooterOpenColor;
		}
	}
}

//paging
.z-listbox-paging {
	&-top {
		border-bottom: 1px solid @pagingBorderColor;
		overflow: hidden;
		width: 100%;
	}
	&-bottom {
		border-top: 1px solid @pagingBorderColor;
		overflow: hidden;
		width: 100%;
	}
}
.z-listbox-autopaging .z-listcell-content {
	height: @meshAutoPagingRowHeight;
	overflow: hidden;
}
//column menu
.z-listhead-menugrouping .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-group.png');
}
.z-listhead-menuungrouping .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-ungroup.png');
}
.z-listhead-menuascending .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-arrowup.png');
}
.z-listhead-menudescending .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-arrowdown.png');
}
//select mold
.z-select {
	font-family: @baseContentFontFamily;
	font-size: @fontSizeMedium;
}

.ie {
	// Prevent sub-pixel rounding that causes scrollbar appear
	.z-listbox-body table {
		overflow: hidden;
	}
}

// ZK-2151: use strict selector to prevent nest problem

