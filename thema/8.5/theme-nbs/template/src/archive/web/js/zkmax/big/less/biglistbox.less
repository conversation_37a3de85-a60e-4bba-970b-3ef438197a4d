@import "~./zul/less/_header.less";

.resetTable() {
	table {
		border-spacing: 0;
		th, td {
			background-clip: padding-box;
		}
	}
}
.z-biglistbox {
	border: 1px solid @baseBorderColor;
	background: @meshBackgroundColor;
	position: relative;
	overflow: hidden;
	zoom: 1;
	
	&-outer {
		border: 1px solid @baseBorderColor;
		border-top: none;
		border-left: none;
		margin: 0 18px 18px 0;
		background: @meshBackgroundColor;
		position: relative;
	}
	&-faker th {
		font-size: 0;
		.size(45px, 0);
		border: 0;
		margin: 0;
		padding: 0;
		line-height: 0;
		overflow: hidden;
	}
	//head
	&-head-outer {
		overflow: hidden;
	}
	&-head {
		width: 100%;
		border: 0;
		overflow: hidden;
		float: left;
		.resetTable();
	}
	&-header {
		border-left: 1px solid @meshTitleBorderColor;
		border-bottom: 1px solid @meshTitleBorderColor;
		padding: 0;
		text-align: left;
		position: relative;
		overflow: hidden;
		cursor: default;
		white-space: nowrap;
		background: @meshTitleBackgroundColor;

		&:hover {
			background: @meshTitleHoverBackgroundColor;
		}
		&:active {
			background: @meshTitleActiveBackgroundColor;
		}
		&-content {
			.fontStyle(@baseTitleFontFamily, @fontSizeMedium, normal, @meshTitleColor);
			padding: 12px 16px;
			line-height: 1.3em;
			position: relative;
			white-space: nowrap;
		}
	}
	&-header-leftmost {
		border-left: none;
	}
	//body
	&-body-outer {
		overflow: hidden;
	}
	&-body {
		width: 100%;
		border: 0;
		background: @meshBackgroundColor;
		position: relative;
		overflow: hidden;
		float: left;
		.resetTable();
		
		//body content
		td {
			.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
			padding: 12px 16px;
			line-height: 1.3em;
			overflow: hidden;
			cursor: pointer;
			white-space: nowrap;
		}
	}
	&-row {
		background: @meshBackgroundColor;
		border-top: 1px solid @meshContentBorderColor;
		&:hover td {
			color: @hoverColor;
			background-color: @hoverBackgroundColor;
			position: relative;
		}
		&.z-biglistbox-selected {
			td {
				color: @selectedColor;
				border-color: @selectedBorderColor;
				background-color: @selectedBackgroundColor;
				position: relative;
			}
			&:hover td {
				color: @selectedHoverColor;
				border-color: @selectedHoverBorderColor;
				background: @selectedHoverBackgroundColor;
				position: relative;
			}
		}
	}
	//odd rows
	&-odd {
		background: @meshStripeBackgroundColor;
	}
	//sort
	&-sort {
		cursor: pointer;
	}
	&-sorticon {
		color: @meshTitleColor;
		position: absolute;
		top: -7px;
		left: 50%;
	}
	&-hover { //sortable header hover status
		position: relative;
	}
	//frozen
	&-head-shim,
	&-body-shim {
		.size(3px, 1px);
		overflow: hidden;
		float: left;
	}
	&-verticalbar-frozen {
		.size(3px, 100%);
		background: @biglistboxFrozenBackgroundColor;
		position: absolute;
		top: -3px;
		&-ghost {
			.size(1px, 100%);
			background: @biglistboxFrozenGhostBackgroundColor;
		}
	}
	&-verticalbar-tick {
		.size(10px, 18px);
		position: absolute;
		font-size: 16px;
		color: @biglistboxScrollBarTextColor;
		background-color: @baseBackgroundColor;
		border: 1px solid @biglistboxScrollBarBorderColor;
		.borderRadius(2px);
		bottom: 2px;
		overflow: hidden;
		cursor: col-resize;
		z-index: 20;
		font-family: 'ZK85Icons';
		&:before {
			content: '\e910';
			position: absolute;
			left: -4px;
		}
		&:hover {
			background-color: @biglistboxScrollBarHoverBackgroundColor;
            border: 1px solid @biglistboxScrollBarHoverBorderColor;
		}
		&:active {
			color: @textColorDefault3;
			background-color: @biglistboxScrollBarActiveBackgroundColor;
            border: 1px solid @biglistboxScrollBarActiveBorderColor;
		}
	}
	//WScroll bar
	&-wscroll {
		// vertical
		&-vertical {
			.size(18px, 100%);
			position: absolute;
			top: 0;
			right: -19px;
			z-index: 10;
			
			.z-biglistbox-wscroll-drag {
				font-family: 'ZK85Icons';
				.size(18px, 116px);
				color: @biglistboxScrollBarTextColor;
				background-color: @baseBackgroundColor;
				position: absolute;
				overflow: hidden;
				cursor: pointer;
				z-index: 15;
				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up,
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end,
				.z-biglistbox-wscroll-body {
					font-size: 16px;
					position: absolute;
					border: 1px solid @biglistboxScrollBarBorderColor;
					&:hover {
						background-color: @biglistboxScrollBarHoverBackgroundColor;
						border: 1px solid @biglistboxScrollBarHoverBorderColor;
					}
					&:active {
						color: @textColorDefault3;
						background-color: @biglistboxScrollBarActiveBackgroundColor;
						border: 1px solid @biglistboxScrollBarActiveBorderColor;
					}
				}
				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up,
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end {
					.size(18px, 18px);
				}
				.z-biglistbox-wscroll-body {
					.size(18px, 45px);
					top: 36px;
					border-top: 0;
					border-bottom: 0;
					&:before {
						content: '\e90d';
						position: absolute;
						top: 14px;
					}
					&:hover, &:active {
						top: 35px;
					}
				}
				.z-biglistbox-wscroll-home {
					top: 0;
					.topBorderRadius(2px);
					&:before {
						content: '\e90e';
					}
				}
				.z-biglistbox-wscroll-up {
					top: 18px;
					border-top: 0;
					&:before {
						content: '\e904';
					}
					&:hover, &:active {
						top: 17px;
					}
				}
				.z-biglistbox-wscroll-down {
					bottom: 18px;
					border-bottom: 0;
					&:before {
						content: '\e90a';
					}
				}
				.z-biglistbox-wscroll-end {
					bottom: 0;
					.bottomBorderRadius(2px);
					&:before {
						content: '\e903';
					}
				}
			}
			.z-biglistbox-wscroll-pos {
				visibility: visible;
				.size(18px, 115px);
				.borderRadius(2px);
				background: contrast(@baseBackgroundColor);
				.opacity(0.25);
				position: absolute;
				left: 0;
				top: 0;
				z-index: 10;
			}
			.z-biglistbox-wscroll-endbar {
				.size(18px, 8px);
				border: 1px solid @biglistboxScrollBarBorderColor;
				.borderRadius(2px);
				background-color: @biglistboxScrollBarEndBarBackgroundColor;
				overflow: hidden;
				position: absolute;
				right: 0;
				z-index: 20;
			}
		}
		// horizontal
		&-horizontal {
			.size(100%, 18px);
			position: absolute;
			left: 0;
			bottom: -19px;
			z-index: 10;
			
			.z-biglistbox-wscroll-drag {
				font-family: 'ZK85Icons';
				.size(116px, 18px);
				color: @biglistboxScrollBarTextColor;
				background-color: @baseBackgroundColor;
				position: absolute;
				overflow: hidden;
				cursor: pointer;
				z-index: 15;
				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up,
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end,
				.z-biglistbox-wscroll-body {
					font-size: 16px;
					position: absolute;
					top: 0;
					border: 1px solid @biglistboxScrollBarBorderColor;
					&:hover {
						background-color: @biglistboxScrollBarHoverBackgroundColor;
						border: 1px solid @biglistboxScrollBarHoverBorderColor;
					}
					&:active {
						color: @textColorDefault3;
						background-color: @biglistboxScrollBarActiveBackgroundColor;
						border: 1px solid @biglistboxScrollBarActiveBorderColor;
					}
				}
				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up,
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end {
					.size(18px, 18px);
				}
				.z-biglistbox-wscroll-body {
					.size(45px, 18px);
					left: 36px;
					border-left: 0;
					border-right: 0;
					&:before {
						content: '\e90f';
						position: absolute;
						left: 14px;
					}
					&:hover, &:active {
						left: 35px;
					}
				}
				.z-biglistbox-wscroll-home {
					left: 0px;
					&:before {
						content: '\f048';
					}
				}
				.z-biglistbox-wscroll-up {
					left: 18px;
					border-left: 0;
					&:before {
						content: '\e906';
					}
					&:hover, &:active {
						left: 17px;
					}
				}
				.z-biglistbox-wscroll-down {
					right: 18px;
					border-right: 0;
					&:before {
						content: '\e90c';
					}
				}
				.z-biglistbox-wscroll-end {
					right: 0;
					&:before {
						content: '\f051';
					}
				}
			}
			.z-biglistbox-wscroll-pos {
				visibility: visible;
				.size(115px, 18px);
				.borderRadius(2px);
				background: contrast(@baseBackgroundColor);
				.opacity(0.25);
				position: absolute;
				top: 0;
				left: 0;
				z-index: 10;
			}
			.z-biglistbox-wscroll-endbar {
				.size(7px, 18px);
				border: 1px solid @biglistboxScrollBarBorderColor;
				.borderRadius(2px);
				background-color: @biglistboxScrollBarEndBarBackgroundColor;
				position: absolute;
				right: -12px;
				overflow: hidden;
				z-index: 20;
			}
		}
		&-vertical,
		&-horizontal {
			// Disabled button style
			.disabledStyle() {
				color: @disabledColor;
				background: @disabledBackgroundColor;
				border: 1px solid @biglistboxScrollBarBorderColor;
				&:hover {
					color: @disabledColor;
					background: @disabledBackgroundColor;
					border: 1px solid @biglistboxScrollBarBorderColor;
				}
				&:active {
					color: @disabledColor;
					background: @disabledBackgroundColor;
					border: 1px solid @biglistboxScrollBarBorderColor;
				}
			}
			.z-biglistbox-wscroll-head {
				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up {
					.disabledStyle();
				}
			}
			.z-biglistbox-wscroll-tail {
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end {
					.disabledStyle();
				}
			}
		}
	}
}
