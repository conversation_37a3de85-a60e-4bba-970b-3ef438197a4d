@import "~./zul/less/_header.less";

//reset table
.resetTable() {
	border-spacing: 0;
	th, td {
		padding: 0;
		background-clip: padding-box;
	}
	th {
		text-align: inherit;
	}
}

.z-hbox,
.z-vbox {
	.resetTable();
	
	&-separator {
		margin: 0;
		padding: 0;
	}
}
.z-hbox-separator {
	width: 0.3em;
}
.z-vbox-separator {
	height: 0.3em;
	td {
		line-height: 0;
	}
}

// Splitter
tr.z-splitter-outer > td {
	height: @splitterSize;
	max-height: @splitterSize;
}
td.z-splitter-outer {
	width: @splitterSize;
	max-width: @splitterSize;
	padding: 0;
}
.z-splitter {
	border: 1px solid @splitterBorderColor;
	background-color: @splitterBackgroundColor;

	&:hover {
		background-color: @splitterHoverBackgroundColor;
		.z-splitter-button {
			color: extract(@splitterButtonTextColors, 2);
		}
	}

	&-horizontal {
		width: @splitterSize;
		border-width: 0 1px;
		overflow: hidden;
		cursor: col-resize;
		
		> .z-splitter-button {
			.size(@splitterSize, 30px);
			border-width: 1px 0;
		}
		.z-splitter-icon {
			font-size: @splitterButtonTextSize;
			position: absolute;
			top: 8px;
			left: -3px;
			
			&.z-icon-ellipsis-v {
				top: -21px;
				left: 2px;
				cursor: col-resize;
				visibility: hidden;
			}
			&.z-icon-ellipsis-v ~ .z-splitter-icon.z-icon-ellipsis-v {
				top: 39px;
			}
		}
	}
	&-vertical {
		height: @splitterSize;
		border-width: 1px 0;
		overflow: hidden;
		cursor: row-resize;
		
		> .z-splitter-button {
			.size(30px, @splitterSize);
			border-width: 0 1px;
		}
		.z-splitter-icon {
			font-size: @splitterButtonTextSize;
			line-height: normal;
			position: absolute;
			top: -4px;
			left: 9px;
			
			&.z-icon-ellipsis-h {
				top: -2px;
				left: -16px;
				cursor: row-resize;
				visibility: hidden;
			}
			&.z-icon-ellipsis-h ~ .z-splitter-icon.z-icon-ellipsis-h {
				left: 36px;
			}
		}
	}
	&-button {
		color: extract(@splitterButtonTextColors, 1);
		display: inline-block;
		position: relative;
		vertical-align: top; //vertical-align: make it looks same in diff browsers
		cursor: pointer;
		
		&-disabled {
			border: 0;
			
			.z-splitter-vertical & {
				cursor: row-resize;
			}
			.z-splitter-horizontal & {
				cursor: col-resize;
			}
		}
	}
	&-nosplitter {
		cursor: default;
	}
	&-ghost {
		background: @splitterDragBackgroundColor !important;
	}
}
