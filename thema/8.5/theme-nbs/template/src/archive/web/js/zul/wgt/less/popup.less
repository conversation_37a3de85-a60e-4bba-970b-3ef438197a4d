@import "~./zul/less/_header.less";

.z-popup {
	border: 1px solid @popupBorderColor;
	.borderRadius(@baseBorderRadius);
	background-color: @popupBackgroundColor;
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
	.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');

	&-content {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
		height: 100%;
		padding: 8px 8px;
		line-height: @baseLineHeight;
	}
}

// notification
.z-notification {
	position: absolute;
	top: 0;
	left: 0;
}

.z-notification-icon {
	position: absolute;
	z-index: 1;

	&.z-icon-times-circle, 
	&.z-icon-exclamation-circle,
	&.z-icon-info-circle {
		.size(32px, 32px);
		top: 50%;
		font-size: 32px;
		line-height: 32px;
		margin-top: -16px;
		left: 16px;
	}
}

.z-notification-pointer + .z-notification-icon {
	left: 8px;
}

.z-notification-left + .z-notification-icon {
	left: 20px;
}

.z-notification-up + .z-notification-icon {
	margin-top: -12px;
}

.z-notification-down + .z-notification-icon {
	margin-top: -22px;
}

.z-notification-content {
	font-family: @baseContentFontFamily;
	font-size: @fontSizeMedium;
	font-weight: normal;
	width: 288px;
	min-height: 80px;
	padding: 24px 16px 24px 56px;
	position: relative;
	overflow: hidden;
	.borderRadius(@baseBorderRadius);
	.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
}

.z-notification-pointer ~ .z-notification-content {
	.displaySize(table-cell, 260px, 60px);
	min-height: 60px; // fix for IE9
	padding: 8px 34px 8px 48px;
	vertical-align: middle;
}

.z-notification-pointer {
	.displaySize(none, 0, 0);
	border: 10px solid transparent;
	position: absolute;
	z-index: 100;
}

// notification arrow: base style
.z-notification-left,
.z-notification-right,
.z-notification-up,
.z-notification-down {
	border: 10px solid transparent;
}

// notification arrow: info
.z-notification-info {
	.z-notification-icon,
	.z-notification-close,
	.z-notification-content {
		color: @notificationInfoColor;
		background-color: @notificationInfoBackgroundColor;
	}
	.z-notification-left {
		border-right-color: @notificationInfoBackgroundColor;
	}
	.z-notification-right {
		border-left-color: @notificationInfoBackgroundColor;
	}
	.z-notification-up {
		border-bottom-color: @notificationInfoBackgroundColor;
	}
	.z-notification-down {
		border-top-color: @notificationInfoBackgroundColor;
	}
}

// notification arrow: warning
.z-notification-warning {
	.z-notification-icon,
	.z-notification-close,
	.z-notification-content {
		color: @notificationWarningColor;
		background-color: @notificationWarningBackgroundColor;
	}
	.z-notification-left {
		border-right-color: @notificationWarningBackgroundColor;
	}
	.z-notification-right {
		border-left-color: @notificationWarningBackgroundColor;
	}
	.z-notification-up {
		border-bottom-color: @notificationWarningBackgroundColor;
	}
	.z-notification-down {
		border-top-color: @notificationWarningBackgroundColor;
	}
}

// notification arrow: error
.z-notification-error {
	.z-notification-icon,
	.z-notification-close,
	.z-notification-content {
		color: @notificationErrorColor;
		background-color: @notificationErrorBackgroundColor;
	}
	.z-notification-left {
		border-right-color: @notificationErrorBackgroundColor;
	}
	.z-notification-right {
		border-left-color: @notificationErrorBackgroundColor;
	}
	.z-notification-up {
		border-bottom-color: @notificationErrorBackgroundColor;
	}
	.z-notification-down {
		border-top-color: @notificationErrorBackgroundColor;
	}
}

.z-notification-close {
	font-size: 18px;
	.size(20px, 20px);
	padding: 0 2px;
	line-height: 18px;
	position: absolute;
	top: 8px;
	right: 8px;
	cursor: pointer;
}

.z-notification-right ~ .z-notification-close {
	right: 16px;
}
.z-notification-up ~ .z-notification-close {
	top: 16px;
}
