@import "~./zul/less/_header.less";

//reset table
.resetTable() {
	table {
		border-spacing: 0;
		th, td {
			background-clip: padding-box;
			padding: 0;
		}
		th {
			text-align: inherit;
		}
	}
}

//tree
.z-tree {
	background: @meshBackgroundColor;
//	border: 1px solid @baseBorderColor;
	overflow: hidden;
	zoom: 1;
	//tree header div
	&-header {
		width: 100%;
		background: @meshTitleBackgroundColor;
		position: relative;
		overflow: hidden;
		.resetTable();
	}
	&-header-border {
		border-bottom: 1px solid @meshTitleBorderColor;
		margin-top: -1px;
		position: relative;
	}
	//tree body div
	&-body {
		position: relative;
		overflow: hidden;
		.resetTable();
	}
	&-body &-emptybody td {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @disabledColor);
		text-align: left;
		height: 1px;
		padding: 12px 16px;
	}
	//tree footer div
	&-footer {
		background: @meshFootBackgroundColor;
		border-top: 1px solid @baseBorderColor;
		overflow: hidden;
		.resetTable();

		.z-treefooter {
			background: @meshFootBackgroundColor;
		}
	}
}
//tree open icon and indent space
.z-tree {
	&-icon,
	&-line {
		.displaySize(inline-block, @baseIconWidth, @baseIconHeight);
		line-height: @baseIconHeight;
		vertical-align: middle;
	}
	&-icon {
		.iconFontStyle(@fontSizeLarge, @textColorDefault);
		text-align: center;
		cursor: pointer;
	}
}
//treecols
.z-treecols {
	th:first-child {
		border-left: none;
		// B50-3306729: the first header should have border-left when the first column is covered with other header
		&.z-treecols-border {
			border-left: 1px solid @meshTitleBorderColor;
		}
	}
	&-bar {
		border-left: 1px solid @meshTitleBorderColor;
		border-bottom: 1px solid @meshTitleBorderColor;
	}
}
.z-treecol {
	background: @meshTitleBackgroundColor;
	border-left: 1px solid @meshTitleBorderColor;
	border-bottom: 1px solid @meshTitleBorderColor;
	padding: 0;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	&-sort {
		cursor: pointer;

		.z-treecol-sorticon {
			color: @meshTitleColor;
			position: absolute;
			top: -7px;
			left: 50%;
		}
	}
	&-sizing,
	&-sizing .z-treecol-content {
		cursor: col-resize;
	}
}
// ZK-2151: use strict selector to prevent nest problem
//tree row and cell
.z-treerow {
	&:first-child {
		.z-treecell {
			border-top-width: 0;
		}
	}
	//tree cell
	.z-treecell {
		border-top: 1px solid @meshContentBorderColor;
		overflow: hidden;
		cursor: pointer;
		background: @meshBackgroundColor;
	}

	&:hover {
		> .z-treecell {
			background: @hoverBackgroundColor;
			> .z-treecell-content {
				color: @hoverColor;
			}
		}
	}
	//check mark
	&-checkable {
		.displaySize(inline-block, 20px, 20px);
    	.iconFontStyle(@checkedIconSize, @checkedColor);
    	border: 1px solid @checkedBorderColor;
    	background: @checkedBackgroundColor;
    	vertical-align: text-top;
    	margin-right: 8px;
    	.borderRadius(@baseBorderRadius);

		&.z-treerow-radio {
			.borderRadius(50%);
		}
		.z-treerow-icon {
			display: none;
			cursor: default;
		}
	}
	//selected tree cell
	&.z-treerow-selected {
		> .z-treecell {
			background: @selectedBackgroundColor;
			position: relative;

			> .z-treecell-content {
				color: @selectedColor;
			}
		}
		
		&:hover {
			> .z-treecell {
				background: @selectedHoverBackgroundColor;
				> .z-treecell-content {
					color: @selectedHoverColor;
				}
			}
		}
	}
	&.z-treerow-selected.z-treerow-focus {
		> .z-treecell {
			background: @selectedFocusBackgroundColor;
			position: relative;

			> .z-treecell-content {
				color: @selectedFocusColor;
			}
		}
		&:hover {
			> .z-treecell {
				background: @selectedHoverBackgroundColor;
				> .z-treecell-content {
					color: @selectedHoverColor;
				}
			}
		}
	}
	&-focus {
		> .z-treecell {
			background: @meshContentFocusBackgroundColor;
			position: relative;
			> .z-treecell-content {
				color: @hoverColor;
			}
		}
	}
	//selected check mark
	&-selected {
		> .z-treecell > .z-treecell-content 
			> .z-treerow-checkable .z-treerow-icon {
			color: @checkedColor;
			display: block;
			padding-left: 1px;
			line-height: @baseLineHeight;
			
			&.z-icon-check {} //for checkbox, use font-awesome
			&.z-icon-radio { //for radio
				.size(10px, 10px);
				.borderRadius(50%);
				background: @checkedColor;
				position: relative;
				top: 50%;
				left: 50%;
				.transform('translate(-50%, -50%)');
			}
		}
	}
	//disabled
	&.z-treerow-disabled {
		* {
			color: @disabledColor !important;
			cursor: default !important;
		}
		&:hover > .z-treecell {
			position: relative;
		}
	}
}

body:not(.gecko) {
	.z-treerow {
		&:hover {
			> .z-treecell {
				position: relative;
			}
		}
	}
}

//content
.z-treecol-content,
.z-treecell-content,
.z-treefooter-content {
	.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
	padding: 12px 16px;
	line-height: 1.3em;
	overflow: hidden;
}
.z-treecol-content {
	color: @meshTitleColor;
	position: relative;
}
.z-treecell-content {
	line-height: @baseLineHeight;
}
.z-treefooter-content {
	color: @textColorLight;
}
.z-treecell-text {
	vertical-align: middle;
	margin-left: 8px;
}
//paging
.z-tree-paging {
	&-top {
		border-bottom: 1px solid @pagingBorderColor;
		overflow: hidden;
		width: 100%;
	}
	&-bottom {
		border-top: 1px solid @pagingBorderColor;
		overflow: hidden;
		width: 100%;
	}
}
.z-tree-autopaging .z-treecell-content {
	height: @meshAutoPagingRowHeight;
	overflow: hidden;
}
// ZK-2151: use strict selector to prevent nest problem
