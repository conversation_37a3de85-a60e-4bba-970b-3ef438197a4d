@import "~./zul/less/_header.less";

.z-frozen {
	.encodeThemeURL(background-image, '~./zul/img/common/bar-bg.png');
	overflow: hidden;

	&-body {
		overflow: hidden;
		float: left;
	}

	&-inner {
		overflow-x: scroll;
		overflow-y: hidden;
		float: right;

		div {
			height: 100%;
		}
	}
	//for new frozen
	&-col {
		border-left: none !important;
		border-right: 1px solid @meshTitleBorderColor;
	}
}

.ie9, .ie10 {
	.z-frozen {
		&-inner {
			padding-top: 1px;
			margin-top: -1px;
		}
	}
}
