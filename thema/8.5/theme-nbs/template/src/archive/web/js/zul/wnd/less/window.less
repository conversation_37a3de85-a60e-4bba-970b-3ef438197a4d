@import "~./zul/less/_header.less";

.defaultOverflowZoom() {
	overflow: hidden;
	zoom: 1;
}

.z-window {
	// ZK-2115: the style should apply [overflow: hidden]
	.defaultOverflowZoom();
	border: 1px solid @containerBorderColor;
	.borderRadius(@containerBorderRadius);
	padding: @containerPadding;
	background-color: @containerBackground;

	&-shadow {
		.boxShadow('0 3px 6px rgba(0, 0, 0, 0.24)');
	}

	&-header {
		.fontStyle(@baseTitleFontFamily, @containerHeaderTextSize, normal, @containerHeaderColor);
		line-height: @baseButtonWidth;
		padding-bottom: @containerPadding;
		.defaultOverflowZoom();
		cursor: default;

		&-move {
			cursor: move;
			padding-left: 4px;
		}
	}

	&-content {
		padding: @containerPadding;
		background: @baseBackgroundColor;
		color: @containerBodyColor;
		.defaultOverflowZoom();
	}

	&-icon {
		font-size: @containerButtonSize;
		color: extract(@containerButtonColors, 1);
		.displaySize(block, @baseButtonWidth + 4, @baseButtonHeight);
		margin: auto 1px;
		padding: 2px;
		line-height: @baseButtonHeight;
		text-align: center;
		overflow: hidden;
		cursor: pointer;
		float: right;

		&:hover {
			color: extract(@containerButtonColors, 2);
		}
	}


	&-resize-faker {
		border: 1px dashed #1854C2;
		background: #D7E6F7;
		.opacity(0.5);
		position: absolute;
		left: 0;
		top: 0;
		overflow: hidden;
		z-index: 60000;
	}

	&-move-ghost {
		border: 1px solid @containerBorderColor;
		.borderRadius(@containerBorderRadius);
		padding: 0;
		background: @colorPrimaryLighter;
		.opacity(0.65);
		position: absolute;
		overflow: hidden;
		cursor: move !important;

		.z-window-header-move {
			padding: 16px;			
		}

		dl {
			font-size: 0;
			display: block;
			border-top: 1px solid @containerBorderColor;
			margin: 0;
			padding: 0;
			line-height: 0;
			overflow: hidden;
		}
	}

	&-embedded {
		.z-window-shadow {
			.boxShadow('none');
		}
	}

	&-noborder {
		border: 0;

		> .z-window-content {
			border: 0;
		}
	}
}

.z-messagebox {
	margin-left: 16px;
	/* workaround for overflow messages */
	-ms-word-break: break-all;
	word-break: break-all;
	word-break: break-word; // Non standard for webkit
	.applyCSS3('hyphens', auto);

	&-window {
		padding: 0;
	}

	&-window .z-window-header, &-window.z-window-modal .z-window-content, 
	&-window.z-window-highlighted .z-window-content {
		padding: 16px;
	}

	&-window .z-separator {
		height: 32px !important;
	}

	.z-label {
		font-family: @baseContentFontFamily;
		font-size: @baseFontSize;
		color: @baseTextColor;
	}

	&-buttons {
		text-align: right;
		& > * {
			margin-left: 8px;
		}
	}

	&-button {
		width: 100%;
		min-width: 48px;
	}

	&-icon {
		font-size: 30px;
		.displaySize(inline-block, @baseBarWidth, @baseBarHeight);
		border: 0;
		background-repeat: no-repeat;
		text-align: center;
		vertical-align: top;
		cursor: pointer;
	}

	&-question {
		.encodeThemeURL(background-image, '~./zul/img/msgbox/question-btn.png');
	}

	&-exclamation {
		.encodeThemeURL(background-image, '~./zul/img/msgbox/warning-btn.png');
	}

	&-information {
		.encodeThemeURL(background-image, '~./zul/img/msgbox/info-btn.png');
	}

	&-error {
		.encodeThemeURL(background-image, '~./zul/img/msgbox/stop-btn.png');
	}
}
