@import "~./zul/less/_header.less";

.z-groupbox {
	padding-top: 8px;

	> .z-groupbox-header {
		background: @baseBackgroundColor;
		.fontStyle(@baseTitleFontFamily, @fontSizeLarge, normal, @groupboxHeaderColor);
		.size(100%, @baseIconHeight);
		border: 1px solid @baseBorderColor;
		border-bottom: 0;
		padding-left: 8px;
		line-height: @baseLineHeight;
		zoom: 1;

		.z-groupbox-title {
			height: auto;
			min-height: 12px;
			white-space: nowrap;
		}

		.z-groupbox-title-content {
			display: inline-block;
			line-height: @baseHeightGroupBox;
			padding: 0 4px;
		}

		.z-caption, .z-groupbox-title {
			display: inline;
			width: auto;
			padding: 0 4px;
			line-height: @baseHeightGroupBox;
			background: @baseBackgroundColor;
			position: relative;
			cursor: pointer;

			&-readonly {
				cursor: default;
			}
		}

		.z-caption-content, .z-label {
			float: none;			
			.z-caption-label {
				white-space: pre !important;
			}
		}

	}
	
	> .z-groupbox-readonly .z-groupbox-title {
		cursor: default;
	}
	
	&-content {
		.fontStyle(@baseTitleFontFamily, @fontSizeMedium, normal, @baseTextColor);
		background: @baseBackgroundColor;
		display: block;
		height: inherit; // Firefox bug: content cant be collapsed when scrolling
		border: 1px solid @baseBorderColor;
		border-top: 0;
		padding: 8px 16px 16px;
		overflow: hidden;
		zoom: 1;
	}

	&-notitle {
		padding: 0;

		.z-groupbox-content {
			border-top: 1px solid @baseBorderColor;
			padding: 16px;
		}
	}

	&-collapsed .z-groupbox-header {
		border-left: 1px solid transparent;
		border-right: 1px solid transparent;
	}

	&-3d {
		padding: 0;

		> .z-groupbox-header {
			height: auto;
			min-height: @baseBarHeight;
			border-bottom: 1px solid @baseBorderColor;
			padding: 0;

			.z-caption, .z-groupbox-title {
				display: inline-block;
				width: 100%;
				margin: 0;
				padding: 8px 12px;
				line-height: @baseHeightGroupBox;
				background: none;
				top: 0;
			}

			.z-caption-content, .z-groupbox-title-content {
				line-height: @baseIconHeight;
				font-size: @fontSizeLarge;
				color: @groupboxHeaderColor;
				padding: 0;
				> .z-caption-image {
					margin-left: 0;
					margin-right: 4px;
					height: @sizeHeightCaptionImageGroupBox;
					width: @sizeWidthCaptionImageGroupBox;
				}
			}
		}

		> .z-groupbox-content {
			padding: 16px;
		}
	}
}
