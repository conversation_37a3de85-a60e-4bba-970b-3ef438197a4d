## ZK85 Theme Builder

### Prerequisites
1. `mvn` Maven 3
2. A shell for executing script like `bash`
3. `git` Git (only if you want to update submodule)
4. [zkThemeTemplate source](https://github.com/zkoss/zkThemeTemplate) = /template

### How to add a new palette
1. Put `new_palette.less` into `projects/` folder.
2. Add new project in `build.projects.yml`.

        build_themes:
          - new_palette

        themes:
          new_palette:
            name: new_palette
            display: New Palette

3. `./build.sh -t new_palette` to build.

### How to build all palettes at once
1. Edit `build_themes:` of `build.projects.yml`, leave what you want to build
2. Execute `./build.sh` (or `./build.sh -u` for updating template first)
3. DO NOT build parallelly (eg: multiple instances). Build one at a time.
4. The jar files will be placed in `target` folder.

### How to install into local Maven repo
1. Execute `./build.sh -i`
2. Use in your project pom.xml

		<dependency>
			<groupId>org.zkoss.theme</groupId>
			<artifactId>new_palette</artifactId>
			<version>8.5.0.FL.20171011</version>
		</dependency>

### Build parameters

    $ ./build -ust 'a b c'

* i: Install the theme into Maven local repo
* u: Update git submodule first
* s: Generate source archives for publishing
* t: Build themes only, separeted by space (ex: a b c)
* c: Do `mvn clean` first
* p: Build theme-pack (Themes all-in-one jar, affected by -t)

### Notice for whom gets source from archive file
The archive doesn't include any git information, so updating template by `./build.sh -u` will fail. Instead you can fetch the newest template source from GitHub.

Or you can try adding it manually:
1. Make this project Git controlled `git init`
2. Delete the template folder
3. `git submodule add https://github.com/zkoss/zkThemeTemplate.git template`

Another way is cloning the repo (that you can keep your changes and merge from origin, but you still can't do `./build.sh -u`):
1. Delete the template folder
2. `git clone https://github.com/zkoss/zkThemeTemplate.git template`
