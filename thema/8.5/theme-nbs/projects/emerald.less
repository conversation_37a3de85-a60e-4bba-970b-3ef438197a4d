// Emerald
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #8AC73F;
@colorPrimary:                 #8AC73F;
@colorPrimaryDark:             #6EA22F; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #A1D265; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #364225; // List hover Bg
@colorAccent:                  #13ACFF;
@colorAccent3:                 #D6FAFF; // Tooltip Bg
@colorBackground1:             #3C3E39; // Window Bg, Panel Bg
@colorBackground3:             #2F2F2F; // Container Bg
@colorGreyDark:                #767676;
@colorGreyLight:               #4E4E4E; // Btn disabled Bg
@colorGreyLighter:             #404040; // Field disabled Bg
@inputBackgroundColor:         #262626;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #454545;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';