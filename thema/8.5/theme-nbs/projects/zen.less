// Zen
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #FFB529;
@colorPrimary:                 #FFB529;
@colorPrimaryDark:             #EC9A00; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #FFC353; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #8096A6; // List hover Bg
@colorAccent:                  #33A6FF;
@colorAccent3:                 #D9F3FF; // Tooltip Bg
@colorBackground1:             #3C5568; // Window Bg, Panel Bg
@colorBackground3:             #223C50; // Container Bg
@colorGreyDark:                #6D7E8B;
@colorGreyLight:               #42596A; // Btn disabled Bg
@colorGreyLighter:             #334B5D; // Field disabled Bg
@inputBackgroundColor:         #182E3F;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #3C5568;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';
