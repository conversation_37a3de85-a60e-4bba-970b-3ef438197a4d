// Winter Spring
@textColorActive:              #A5BFCD;
@colorPrimary:                 #A5BFCD;
@colorPrimaryDark:             #759DB2; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #B7CBD7; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #D6E9F5; // List hover Bg
@colorAccent:                  #A5BFCD;
@colorAccent3:                 #7D8291; // Tooltip Bg
@colorBackground1:             #7D8291; // Window Bg, Panel Bg
@containerHeaderColor:         #FFFFFF;
@containerButtonColors:        #FFFFFF, rgba(255,255,255,0.9); // 1: normal, 2: hover
@inputFocusBorderColor:        #A5BFCD;

@primary2:                     #0CD17D;
@primaryDark2:                 #09A764;
@primaryLight2:                #3CE49C;

@checkedColor:                 @primary2; // @colorPrimary;
@checkboxHoverBorderColor:     @primary2; // @colorPrimary;

@chosenboxItemBorderColor:     @primaryDark2; // @colorPrimaryDark;
@chosenboxItemBackgroundColor: @primary2; // @colorPrimary;
@chosenboxItemFocusBackgroundColor: @primaryDark2; // @colorPrimaryDark;
@chosenboxCreateIconColor:     @primary2; // @colorPrimary;

@buttonBackgroundColor:        @primary2; // @colorPrimary;
@buttonHoverBackgroundColor:   @primaryLight2; // @colorPrimaryLight;
@buttonFocusBackgroundColor:   @primary2; // @colorPrimary;
@buttonActiveBackgroundColor:  @primaryDark2; // @colorPrimaryDark;
@buttonSeparatorBorderColor:   @primaryDark2; // @colorPrimaryDark;
@toolbarButtonCheckedBackgroundColor: @primary2; // @colorPrimary;

@sliderAreaBackgroundColor:    @primary2; // @colorPrimary;

@progressmeterBackgroundColor: @primary2; // @colorPrimary;

@calendarSelectedBackgroundColor: @primary2; // @colorPrimary;
@calendarSelectedHoverBackgroundColor: @primary2; // @colorPrimary;

@menuItemActiveBackground:      @primary2; // @colorPrimary;
@menuPopupItemActiveBackground: @primary2; // @colorPrimary;