// Amber
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #FFA706;
@colorPrimary:                 #FFA706;
@colorPrimaryDark:             #D08700; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #FFB837; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #58431B; // List hover Bg
@colorAccent:                  #13ACFF;
@colorAccent3:                 #B8CBD7; // Tooltip Bg
@colorBackground1:             #454545; // Window Bg, Panel Bg
@colorBackground3:             #2F2F2F; // Container Bg
@colorGreyDark:                #767676;
@colorGreyLight:               #4E4E4E; // Btn disabled Bg
@colorGreyLighter:             #404040; // Field disabled Bg
@inputBackgroundColor:         #262626;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #454545;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';