// Violet
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #9F1ABF;
@colorPrimary:                 #9F1ABF;
@colorPrimaryDark:             #7F1498; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #C030E2; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #4C3456; // List hover Bg
@colorAccent:                  #F9B000;
@colorAccent3:                 #ADDFF8; // Tooltip Bg
@colorBackground1:             #4F4C50; // Window Bg, Panel Bg
@colorBackground3:             #2F2F2F; // Container Bg
@colorGreyDark:                #767676;
@colorGreyLight:               #4E4E4E; // Btn disabled Bg
@colorGreyLighter:             #404040; // Field disabled Bg
@inputBackgroundColor:         #262626;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #454545;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';