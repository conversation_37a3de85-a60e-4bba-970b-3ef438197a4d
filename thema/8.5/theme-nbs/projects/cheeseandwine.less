// Cheese and <PERSON>
@textColorActive:              #7E468D;
@colorPrimary:                 #7E468D;
@colorPrimaryDark:             #643870; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #9E5FAF; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #F5E7FB; // List hover Bg
@colorAccent:                  #7E468D;
@colorAccent3:                 #0A99A1; // Tooltip Bg
@colorBackground1:             #54335D; // Window Bg, Panel Bg
@containerHeaderColor:         #FFFFFF;
@containerButtonColors:        #FFFFFF, rgba(255,255,255,0.9); // 1: normal, 2: hover
@inputFocusBorderColor:        #7E468D;

@primary2:                     #F19110;
@primaryDark2:                 #C2740B;
@primaryLight2:                #F3A63F;

@checkedColor:                 @primary2; // @colorPrimary;
@checkboxHoverBorderColor:     @primary2; // @colorPrimary;

@chosenboxItemBorderColor:     @primaryDark2; // @colorPrimaryDark;
@chosenboxItemBackgroundColor: @primary2; // @colorPrimary;
@chosenboxItemFocusBackgroundColor: @primaryDark2; // @colorPrimaryDark;
@chosenboxCreateIconColor:     @primary2; // @colorPrimary;

@buttonBackgroundColor:        @primary2; // @colorPrimary;
@buttonHoverBackgroundColor:   @primaryLight2; // @colorPrimaryLight;
@buttonFocusBackgroundColor:   @primary2; // @colorPrimary;
@buttonActiveBackgroundColor:  @primaryDark2; // @colorPrimaryDark;
@buttonSeparatorBorderColor:   @primaryDark2; // @colorPrimaryDark;
@toolbarButtonCheckedBackgroundColor: @primary2; // @colorPrimary;

@sliderAreaBackgroundColor:    @primary2; // @colorPrimary;

@progressmeterBackgroundColor: @primary2; // @colorPrimary;

@calendarSelectedBackgroundColor: @primary2; // @colorPrimary;
@calendarSelectedHoverBackgroundColor: @primary2; // @colorPrimary;

@menuItemActiveBackground:      @primary2; // @colorPrimary;
@menuPopupItemActiveBackground: @primary2; // @colorPrimary;

@borderlayoutCollapsedIconColors: rgba(255,255,255,0.68), @textColorLight; // 1: normal, 2: hover
@splitterButtonTextColors:     rgba(255,255,255,0.24), @textColorLighter; // 1: normal, 2: hover

@containerBorderColor:         #767676;
@splitterBorderColor:          #4e4e4e;