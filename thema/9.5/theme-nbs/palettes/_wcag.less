// WCAG complaint: Blue
@textColorDefault:             rgba(0, 0, 0, 0.95);
@textColorLight:               rgba(0, 0, 0, 0.75);
@textColorLighter:             rgba(0, 0, 0, 0.57);
@textColorDefault3:            #FFFFFF;
@textColorActive:              @colorPrimary;

@colorPrimary:                 #3251C9;
@colorPrimaryDark:             #22378A;
@colorPrimaryLight:            #96AAFA;
@colorPrimaryLighter:          #CDD6FA; // List hover Bg
@colorAccent:                  #E67626;
@colorAccent2:                 #F53142;
@colorAccent3:                 #261429; // Tooltip Bg
@colorBackground1:             #F0F2F5; // Window Bg, Panel Bg
@colorBackground3:             #FFFFFF; // Container Bg
@colorGreyDark:                #A8A8A8;
@colorGreyLight:               #D9D9D9; // Btn disabled Bg
@colorGreyLighter:             #F2F2F2; // Field disabled Bg

@selectedBackgroundColor:      #6689ED;
@errorboxColor:                #C90D2C;
@baseBorderColorDark:          #6C6C6C;
@baseBorderColor:              #949494;
@baseBorderColorLight:         #C1C1C1;
@containerBorderColor:         @baseBorderColorDark;
@inputHoverBorderColor:        @baseBorderColorDark;
@biglistboxScrollBarHoverBorderColor: @baseBorderColorDark;
@portalchildrenFrameBorderColor:      @baseBorderColorDark;
@goldenLayoutBorderColor:      @baseBorderColor;
@organigramLine:               1px solid @baseBorderColor;
@meshContentBorderColor:       @baseBorderColorLight;

@buttonBorderWidth:            0;
@buttonHoverColor:             @textColorDefault;
@meshTitleHoverColor:          @textColorDefault;

@progressmeterBackgroundImage: '';
@loadingAnimationDefer:        '~./zul/img/misc/progress-48-noanim.png';
@loadingAnimationLoad:         '~./zul/img/misc/progress-72-noanim.png';

// Global focus style
#footer() {
	.append-style() {
		*:focus {
			box-shadow: inset 0 0 0 2px @colorAccent, inset 0 0 0 3px @colorBackground3;
		}
		// workaround
		.z-combobutton {
			&:focus > &-content {
				box-shadow: inherit;
			}
		}
		input[role="spinbutton"]:focus + span,
		[role="combobox"] > input:focus + [role="button"] {
			box-shadow: inset 0 -2px 0 0 @colorAccent, inset 0 2px 0 0 @colorAccent, inset -2px 0 0 0 @colorAccent,
						inset 0 -3px 0 0 @colorBackground3, inset 0 3px 0 0 @colorBackground3, inset -3px 0 0 0 @colorBackground3;
		}
	}
}