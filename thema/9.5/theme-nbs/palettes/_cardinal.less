// <PERSON>
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #CE1E3E;
@colorPrimary:                 #CE1E3E;
@colorPrimaryDark:             #A41831; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #E33F5D; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #59192A; // List hover Bg
@colorAccent:                  #B993C4;
@colorAccent3:                 #FFF2F4; // Tooltip Bg
@colorBackground1:             #40223E; // Window Bg, Panel Bg
@colorBackground3:             #261425; // Container Bg
@colorGreyDark:                #816D7F;
@colorGreyLight:               #463745; // Btn disabled Bg
@colorGreyLighter:             #372636; // Field disabled Bg
@inputBackgroundColor:         #080308;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #40223E;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';
@notificationInfoBackgroundColor:        #34373B;
@notificationWarningBackgroundColor:     #34373B;
@notificationErrorBackgroundColor:       #34373B;