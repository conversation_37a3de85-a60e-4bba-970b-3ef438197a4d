<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>org.zkoss.themepack</groupId>
	<artifactId>theme-pack-builder</artifactId>
	<version>9.5.0.2</version>
	<properties>
		<zk.version>9.0.0-Eval</zk.version>
		<finalName>${project.artifactId}-${project.version}</finalName>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<zktheme.resources>${project.basedir}/src/archive</zktheme.resources>
		<zktheme.web.resources>${zktheme.resources}/web</zktheme.web.resources>
		<zktheme.theme.outputDirectory>${project.build.outputDirectory}/web/${theme.name}</zktheme.theme.outputDirectory>
		<theme.name>iceblue</theme.name>
		<theme.display>Iceblue</theme.display>
		<theme.profile>default</theme.profile>
		<theme.palette>iceblue</theme.palette>
	</properties>
	<packaging>jar</packaging>
	<name>ZK Theme Pack Builder</name>
	<url>https://www.zkoss.org/product/zkthemepack</url>
	<description>ZK Theme Pack Builder</description>
	<licenses>
		<license>
			<name>ZK Commercial License</name>
			<url>https://www.zkoss.org/license#commercial</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<developers>
		<developer>
			<id>zkteam</id>
			<name>ZK Team</name>
			<email><EMAIL></email>
			<url>https://www.zkoss.org</url>
			<organization>Potix</organization>
			<organizationUrl>https://www.zkoss.org</organizationUrl>
			<roles>
				<role>architect</role>
				<role>developer</role>
			</roles>
			<timezone>8</timezone>
			<properties>
				<picUrl>https://www.zkoss.org</picUrl>
			</properties>
		</developer>
	</developers>
	<repositories>
		<repository>
			<id>ZK CE</id>
			<name>ZK CE Repository</name>
			<url>https://mavensync.zkoss.org/maven2</url>
		</repository>
		<repository>
			<id>ZK EE Evaluation</id>
			<url>https://mavensync.zkoss.org/eval/</url>
		</repository>
	</repositories>
	<dependencies>
		<dependency>
			<groupId>org.zkoss.zk</groupId>
			<artifactId>zul</artifactId>
			<version>${zk.version}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.zkoss.zk</groupId>
			<artifactId>zkmax</artifactId>
			<version>${zk.version}</version>
			<optional>true</optional>
		</dependency>
	</dependencies>
	<build>
		<finalName>${finalName}</finalName>
		<sourceDirectory>${project.basedir}/src/</sourceDirectory>
		<resources>
			<resource>
				<directory>${zktheme.resources}</directory>
				<excludes>
					<exclude>web/**</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>${project.basedir}/src/archive-filtered</directory>
				<filtering>true</filtering>
			</resource>
			<resource>
				<directory>${zktheme.web.resources}</directory>
				<excludes>
					<exclude>**/*.less</exclude>
				</excludes>
				<targetPath>${zktheme.theme.outputDirectory}</targetPath>
			</resource>
		</resources>
		<plugins>
			<!-- Filter java sources -->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>templating-maven-plugin</artifactId>
				<version>1.0.0</version>
				<executions>
					<execution>
						<id>filter-src</id>
						<goals>
							<goal>filter-sources</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/generated-sources/java-templates/org/zkoss/theme/${theme.name}</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Copy palette -->
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>copy-less</id>
						<phase>generate-sources</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${basedir}/src/archive/web/zul/less/colors</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/palettes</directory>
									<includes>
										<include>_${theme.palette}.less</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- replace -->
			<plugin>
				<groupId>com.google.code.maven-replacer-plugin</groupId>
				<artifactId>replacer</artifactId>
				<version>1.5.3</version>
				<executions>
					<execution>
						<phase>generate-sources</phase>
						<goals>
							<goal>replace</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<file>${basedir}/src/archive/web/zul/less/_zkvariables.less</file>
					<replacements>
						<replacement>
							<token>(@themeProfile:\s+?").+(";)</token>
							<value>$1${theme.profile}$2</value>
						</replacement>
						<replacement>
							<token>(@themePalette:\s+?").+(";)</token>
							<value>$1${theme.palette}$2</value>
						</replacement>
					</replacements>
				</configuration>
			 </plugin>
			<!-- Build less -->
			<plugin>
				<inherited>true</inherited>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.6.0</version>
				<executions>
					<execution>
						<id>compile-less</id>
						<phase>process-resources</phase>
						<goals>
							<goal>exec</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<executable>npx</executable>
					<arguments>
						<argument>zklessc</argument>
						<argument>--source</argument>
						<argument>${zktheme.web.resources}</argument>
						<argument>--output</argument>
						<argument>${zktheme.theme.outputDirectory}</argument>
						<argument>--compress</argument>
					</arguments>
				</configuration>
			</plugin>
			<!-- Compile java -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>8</source>
					<target>8</target>
					<includes>
						<include>Version.java</include>
						<include>ThemeWebAppInit.java</include>
					</includes>
				</configuration>
			</plugin>
			<!-- Build jar -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<archive>
						<addMavenDescriptor>false</addMavenDescriptor>
					</archive>
					<includes>
						<include>**/${theme.name}/*</include>
						<include>**/${theme.name}/**</include>
						<include>**/metainfo/**</include>
					</includes>
					<finalName>${theme.name}-${project.version}</finalName>
				</configuration>
			</plugin>
			<!-- assembly -->
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.2</version>
				<executions>
					<execution>
						<id>sources</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>assembly/sources.xml</descriptor>
							</descriptors>
							<finalName>${theme.name}-${project.version}</finalName>
						</configuration>
					</execution>
					<execution>
						<id>src</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<appendAssemblyId>false</appendAssemblyId>
							<descriptors>
								<descriptor>assembly/src.xml</descriptor>
							</descriptors>
							<finalName>${theme.name}-src-${project.version}</finalName>
						</configuration>
					</execution>
					<execution>
						<id>bin</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<appendAssemblyId>false</appendAssemblyId>
							<descriptors>
								<descriptor>assembly/bin.xml</descriptor>
							</descriptors>
							<finalName>${theme.name}-bin-${project.version}</finalName>
						</configuration>
					</execution>
					<execution>
						<id>bundle</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>assembly/bundle.xml</descriptor>
							</descriptors>
							<finalName>${theme.name}-${project.version}</finalName>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
