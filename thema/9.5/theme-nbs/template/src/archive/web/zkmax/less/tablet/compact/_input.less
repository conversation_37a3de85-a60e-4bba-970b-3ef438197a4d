.z-textbox,
.z-decimalbox,
.z-intbox,
.z-longbox,
.z-doublebox {
	height: 32px;
}

.z-combobox,
.z-bandbox,
.z-datebox,
.z-timebox,
.z-spinner,
.z-doublespinner,
.z-timepicker {
	height: 32px;

	&-input {
		line-height: 14px;
		height: 32px;
		padding: 4px 5px;
		padding-right: 34px + 8px;
	}
	&-button {
		line-height: 14px;
		height: 32px;
		font-size: 20px;
		min-width: 34px;
	}
}

.z-spinner-button>a:first-child,
.z-doublespinner-button>a:first-child {
	line-height: 32px;
}

.z-spinner,
.z-doublespinner {
	&-input {
		padding-right: 65px + 8px;
	}
	&-button>a {
		line-height: 32px;
	}
}

.z-timebox,
.z-spinner,
.z-doublespinner {
	&-button>a>i {
		transform: none;
	}
}

.z-timepicker-button {
	line-height: 32px;
	padding: 3px 0;
}

.z-errorbox {
	cursor: move;

	&-icon {
		top: -3px;
	}
	&-content {
		font-size: 15px;
		word-wrap: break-word; 
	}
	> .z-errorbox-icon {
		font-size: 22px;
		top: 6px;
	}
	&-up + .z-errorbox-icon {
		top: 14px;
	}
	&-close {
		font-size: 22px;
	}
}
