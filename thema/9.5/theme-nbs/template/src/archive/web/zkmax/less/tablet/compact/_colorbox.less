//Colorbox
.z-colorbox {
	height: 34px;
}
.z-menu-popup .z-colorpalette {
	padding: 4px;
}
.z-colorpalette {
	.size(586px, 460px);
	padding: 0;
	&-newcolor {
		.size(50px, 34px);
		right: 116px;
	}
	&-input {
		.size(96px, 34px);
	}
	&-color {
		.size(32px, 32px);
		margin: 0px;
	}
	&-body {
		max-height: 416px;
		overflow: auto;
		padding-top: 2px;
	}
}
.z-colorpicker {
	padding: 4px;
	&-main {
		top: 34px;
	}
}

.z-colorbox-popup {
	padding: 4px;
}

.z-menu-image.z-colorbox-color {
	.size(24px, 24px);
	min-width: 24px;
	min-height: 24px;
}

.z-colorbox-paletteicon,
.z-menu-paletteicon,
.z-colorbox-pickericon,
.z-menu-pickericon {
	font-size: 24px;
	width: 32px;
	height: 32px;
	line-height: 32px;
	padding: 0;
	text-align: center;
	left: 4px;
	top: 5px;
}

.z-colorbox-paletteicon, .z-menu-paletteicon {
	left: 40px;
	top: 5px;
}

.z-palette-button .z-colorbox-paletteicon,
.z-picker-button .z-colorbox-pickericon {
	// copied form toolbar.less: z-toolbarbutton-checked
	color: @toolbarButtonCheckedColor;
	background-color: @toolbarButtonCheckedBackgroundColor;
}
