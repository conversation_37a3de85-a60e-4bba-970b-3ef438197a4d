@import "~./zul/less/_header.less";

//reset table
.resetTable() {
	table {
		border-spacing: 0;
		th, td {
			background-clip: padding-box;
			padding: 0;
		}
		th {
			text-align: inherit;
		}
	}
}

//tree
.z-tree {
	background: @meshBackgroundColor;
//	border: 1px solid @baseBorderColor;
	overflow: hidden;
	zoom: 1;
	//tree header div
	&-header {
		width: 100%;
		background: @meshTitleBackgroundColor;
		position: relative;
		overflow: hidden;
		.resetTable();
	}
	//tree body div
	&-body {
		position: relative;
		overflow: hidden;
		.resetTable();
	}
	&-body &-emptybody td {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @disabledColor);
		text-align: left;
		height: 1px;
		padding: 12px 16px;
	}
	//tree footer div
	&-footer {
		background: @meshFootBackgroundColor;
		border-top: 1px solid @baseBorderColor;
		overflow: hidden;
		white-space: nowrap;
		.resetTable();

		.z-treefooter {
			overflow: hidden;
			background: @meshFootBackgroundColor;
		}

		.z-treefoot-bar {
			background: @meshFootBackgroundColor;
		}
	}
	// ZK-3138: ROD loading indicator
	&-loading {
		background: transparent no-repeat center;
		.encodeThemeURL(background-image, @loadingAnimationLoad);
	}
}
//tree open icon and indent space
.z-tree {
	&-icon,
	&-line {
		.displaySize(inline-block, @baseIconWidth, @baseIconHeight);
		line-height: @baseIconHeight;
		vertical-align: middle;
	}
	&-icon {
		.iconFontStyle(@fontSizeLarge, @textColorDefault);
		text-align: center;
		cursor: pointer;
	}
}
//treecols
.z-treecols {
	th:first-child {
		border-left: none;
		// B50-3306729: the first header should have border-left when the first column is covered with other header
		&.z-treecols-border {
			border-left: 1px solid @meshTitleBorderColor;
		}
	}
	&-bar {
		background: @meshTitleBackgroundColor;
		border-left: 1px solid @meshTitleBorderColor;
		border-bottom: 1px solid @meshTitleBorderColor;
	}
}
.z-treecol {
	background: @meshTitleBackgroundColor;
	border-left: 1px solid @meshTitleBorderColor;
	border-bottom: 1px solid @meshTitleBorderColor;
	padding: 0;
	position: relative;
	overflow: hidden;
	white-space: nowrap;
	&-sort {
		cursor: pointer;

		.z-treecol-sorticon {
			color: @meshTitleColor;
			position: absolute;
			top: -7px;
			left: 50%;
			.transform('translateX(-50%)');

			:active {
				background: rgba(0, 0, 0, 0);
			}
		}
	}
	&-sizing,
	&-sizing .z-treecol-content {
		cursor: col-resize;
	}
}
// ZK-2151: use strict selector to prevent nest problem
//tree row and cell
.z-treerow {
	&:first-child {
		.z-treecell {
			border-top-width: 0;
		}
	}
	//tree cell
	.z-treecell {
		border-top: 1px solid @meshContentBorderColor;
		overflow: hidden;
		cursor: pointer;
		background: @meshBackgroundColor;
		position: relative;
		z-index: 0;
	}

	&:hover {
		> .z-treecell {
			background: @hoverBackgroundColor;
			> .z-treecell-content {
				color: @hoverColor;
			}
		}
	}
	//check mark
	&-checkable {
		.displaySize(inline-block, @checkboxSize, @checkboxSize);
    	.iconFontStyle(@checkedIconSize, @checkedColor);
    	border: 1px solid @checkedBorderColor;
    	background: @checkedBackgroundColor;
    	vertical-align: text-top;
    	margin-right: 8px;
    	.borderRadius(@baseBorderRadius);

		&.z-treerow-radio {
			.borderRadius(50%);
		}
		.z-treerow-icon {
			display: none;
			cursor: default;
		}
	}
	//selected tree cell
	&.z-treerow-selected {
		> .z-treecell {
			background: @selectedBackgroundColor;
			position: relative;

			> .z-treecell-content {
				color: @selectedColor;
			}
		}
		
		&:hover {
			> .z-treecell {
				background: @selectedHoverBackgroundColor;
				> .z-treecell-content {
					color: @selectedHoverColor;
				}
			}
		}
	}
	&.z-treerow-selected.z-treerow-focus {
		> .z-treecell {
			background: @selectedFocusBackgroundColor;
			position: relative;

			> .z-treecell-content {
				color: @selectedFocusColor;
			}
		}
		&:hover {
			> .z-treecell {
				background: @selectedHoverBackgroundColor;
				> .z-treecell-content {
					color: @selectedHoverColor;
				}
			}
		}
	}
	&-focus {
		> .z-treecell {
			background: @meshContentFocusBackgroundColor;
			position: relative;
			> .z-treecell-content {
				color: @hoverColor;
			}
		}
	}
	//selected check mark
	&-selected {
		> .z-treecell > .z-treecell-content 
			> .z-treerow-checkable .z-treerow-icon {
			color: @checkedColor;
			display: block;
			padding-left: 1px;
			line-height: @baseLineHeight;
			
			&.z-icon-check {} //for checkbox, use font-awesome
			&.z-icon-radio { //for radio
				.size(10px, 10px);
				.borderRadius(50%);
				background: @checkedColor;
				position: relative;
				top: 50%;
				left: 50%;
				.transform('translate(-50%, -50%)');
			}
		}
	}
	//disabled
	&.z-treerow-disabled {
		* {
			color: @disabledColor !important;
			cursor: default !important;
		}
		&:hover > .z-treecell {
			position: relative;
		}
	}
}
// hidden col
.z-treecell-hidden-col {
	white-space: nowrap;
	overflow: hidden;
}

body:not(.gecko) {
	.z-treerow {
		&:hover {
			> .z-treecell {
				position: relative;
			}
		}
	}
}

//content
.z-treecol-content,
.z-treecell-content,
.z-treefooter-content {
	.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
	padding: @meshBodyPadding;
	line-height: @meshContentLineHeight;
	overflow: hidden;
}
.z-treecol-content {
	color: @meshTitleColor;
	position: relative;
}
.z-treecell-content {
	line-height: @treecellContentLineHeight;
}
.z-treefooter-content {
	color: @textColorLight;
}
.z-treecell-text {
	vertical-align: middle;
	margin-left: 8px;
}
//paging
.z-tree-paging {
	&-top {
		border-bottom: 1px solid @pagingBorderColor;
		overflow: hidden;
		width: 100%;
	}
	&-bottom {
		border-top: 1px solid @pagingBorderColor;
		overflow: hidden;
		width: 100%;
	}
}
.z-tree-autopaging .z-treecell-content {
	height: @meshAutoPagingRowHeight;
	overflow: hidden;
}

.ie9 {
	.z-tree-body {
		margin-top: auto; // ZK-3985: Listbox size change when click the listitem https://stackoverflow.com/a/6425130
	}

	.z-treerow {
		.z-treecell {
			position: static;
			z-index: auto;
		}
	}
}
// ZK-2151: use strict selector to prevent nest problem
