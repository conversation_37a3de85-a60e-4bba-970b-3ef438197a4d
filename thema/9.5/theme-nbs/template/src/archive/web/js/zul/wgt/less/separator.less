@import "~./zul/less/_header.less";

.z-separator {
	opacity: 0.69;
	text-align: center;
	position: relative;
	&-horizontal,
	&-horizontal-bar {
		font-size: 0;
		height: 7px;
		line-height: 0;
		overflow: hidden;
	}
	&-horizontal-bar:after {
		content: "";
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		border-top: 1px solid @colorGreyDark;
		.transform('translate(0, -50%)');
	}
	
	&-vertical,
	&-vertical-bar {
		display: inline-block;
		width: 10px;
		overflow: hidden;
	}
	&-vertical-bar:after {
		content: "";
		position: absolute;
		left: 50%;
		top: 0;
		bottom: 0;
		border-left: 1px solid @colorGreyDark;
		.transform('translate(-50%)');
	}
}