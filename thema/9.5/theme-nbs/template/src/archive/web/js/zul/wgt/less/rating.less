@import "~./zul/less/_header.less";

.z-rating {
    display: inline-block;
    margin: 0;
    padding: 0;
    font-size: @ratingIconFontSize;
    line-height: normal;

    > .z-rating-icon {
        text-align: center;
        color: @ratingIcon;
        padding: @ratingIconPadding;
        .size(@ratingIconSize, @ratingIconSize);
        text-decoration: none;
    }

    &-vertical > a {
        display: block;
    }

    > .z-rating-selected {
        color: @ratingIconSelected;
    }

    > .z-rating-hover {
        color: @ratingIconHover;
        text-shadow: 0 0 1px @ratingIconHoverTextShadow;
    }

    > .z-rating-disabled {
        opacity: 0.5;
        cursor: default;
    }

    > .z-rating-readonly {
        cursor: default;
    }

}