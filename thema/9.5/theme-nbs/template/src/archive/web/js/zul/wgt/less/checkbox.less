@import "~./zul/less/_header.less";

.z-checkbox {
	&-default > &-mold {
		display: none;
	}
	&-tristate > &-mold {
		display: none;
	}
	&-switch {
		display: inline-block;
	}
	&-switch > input[type="checkbox"] {
		display: none;
	}
	&-switch > &-mold {
		margin: @checkboxSwitchMargin;
		position: relative;
		.size(@checkboxSwitchWidth, @checkboxSwitchHeight);
		transition: .4s;
		border-radius: @checkboxSwitchHeight / 2;
		display: inline-block;
		vertical-align: middle;

		&:before {
			content: "";
			position: absolute;
			.size(@checkboxSwitchSize, @checkboxSwitchSize);
			left: @checkboxSwitchOffsetLeft;
			bottom: @checkboxSwitchOffsetBottom;
			transition: .4s;
			border-radius: 50%;
			background-color: white;
			.boxShadow('0 2px 4px 0 rgba(0,0,0,0.16)');
		}

		&:focus {
			.boxShadow(0 0 0px 2px @focusBorderColor);
			transition: unset;
		}
	}
	&-switch > &-content {
		display: inline-block;
	}
	&-switch-off > &-mold {
		background-color: @disabledColor;
	}
	&-switch-on > &-mold {
		background-color: @checkedColor;

		&:before {
			.transform("translateX(@{checkboxSwitchStep})");
		}
	}
	&-switch-disabled > &-mold {
		opacity: .5;
		cursor: default;
	}
	&-toggle {
		display: inline-block;
	}
	&-toggle > input[type="checkbox"] {
		display: none;
	}
	&-toggle > &-mold {
		margin: 0 4px 4px;
		.size(@checkboxToggleSize, @checkboxToggleSize);
		border-radius: 4px;
		transition: .1s;
		display: inline-block;
		vertical-align: middle;
	}
	&-toggle > &-content {
		display: inline-block;
	}
	&-toggle-off > &-mold {
		background-color: @disabledColor;
		.boxShadow('0 4px 1px rgba(0, 0, 0, 0.39)');

		&:focus {
			.boxShadow('0 4px 1px rgba(0, 0, 0, 0.39), 0 1px 0px 2px @{focusBorderColor}');
			transition: unset;
		}
	}
	&-toggle-on > &-mold {
		background-color: @checkedColor;
		.boxShadow('0 0 5px rgba(0, 0, 0, 0.48), 0 0 6px 2px rgba(0, 0, 0, 0.35) inset');
		.transform('translateY(4px)');

		&:focus {
			.boxShadow('0 0 5px rgba(0, 0, 0, 0.48) inset, 0 0 6px 2px rgba(0, 0, 0, 0.35) inset, 0 1px 0px 2px @{focusBorderColor}');
			transition: unset;
		}
	}
	&-toggle-disabled > &-mold {
		opacity: .5;
		cursor: default;
	}
}