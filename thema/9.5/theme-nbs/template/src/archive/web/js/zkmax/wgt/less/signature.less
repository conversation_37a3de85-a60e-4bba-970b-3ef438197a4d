@import "~./zul/less/_header.less";

.z-signature {
	position: relative;
	border: 1px solid @signatureBorderColor;
	border-radius: @signatureBorderRadius;

	&-toolbar {
		position: absolute;
		bottom: @signatureToolbarBottom;
		right: @signatureToolbarRight;

		&-hide {
			display: none;
		}
	}

	&-tool-button {
		.fontStyle(@baseTitleFontFamily, @inputTextSize, normal, @buttonColor);
		min-height: @baseButtonHeight;
		border: @buttonBorderWidth solid @buttonBorderColor;
		.borderRadius(@inputBorderRadius);
		padding: @buttonPadding;
		line-height: normal;
		background-color: @buttonBackgroundColor;
		.boxShadow('0 2px 4px 0 rgba(0,0,0,0.16)');
		cursor: pointer;
		white-space: nowrap;
		margin-right: @signatureToolbarButtonSpacing;

		&:hover {
			color: @buttonHoverColor;
			border-color: @buttonHoverBorderColor;
			background-color: @buttonHoverBackgroundColor;
		}
		&:focus {
			color: @buttonFocusColor;
			border-color: @buttonFocusBorderColor;
			background-color: @buttonFocusBackgroundColor;
		}
		&:active {
			color: @buttonActiveColor;
			border-color: @buttonActiveBorderColor;
			background-color: @buttonActiveBackgroundColor;
		}
		&-icon {
			.baseIconFont();
			font-size: @signatureToolbarButtonIconFontSize;
			vertical-align: bottom;
		}
		&-undo:before {
			content: "\f112";
		}
		&-save:before {
			content: "\f0c7";
		}
		&-clear:before {
			content: "\f00d";
		}
		&-label:not(:empty) {
			margin-left: 4px;
		}
	}

	&-canvas {
		width: 100%;
		height: 100%;
		border-radius: @signatureBorderRadius;
	}
}
