/**
 * @{zprefix} v2.6.0 - A lightweight WYSIWYG editor
 * Default stylesheet for @{zprefix} editor
 * ------------------------
 * @link http://alex-d.github.io/@{zprefix}
 * @license MIT
 * <AUTHOR> (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

@import "~./zul/less/_header.less";
@zprefix: z-tbeditor;

#@{zprefix}-icons {
  overflow: hidden;
  visibility: hidden;
  height: 0;
  width: 0; }
  #@{zprefix}-icons svg {
    height: 0;
    width: 0; }

.@{zprefix}-box *,
.@{zprefix}-box *::before,
.@{zprefix}-box *::after {
  box-sizing: border-box; }

.@{zprefix}-box svg {
  width: 17px;
  height: 100%;
  fill: #222; }

.@{zprefix}-box,
.@{zprefix}-editor {
  display: block;
  position: relative;
  border: 1px solid #DDD;
  width: 100%;
  min-height: 300px;
  margin: 0px auto; }

.@{zprefix}-box .@{zprefix}-editor {
  margin: 0 auto; }

.@{zprefix}-editor p {
    margin: 0 0 1em;
}

.@{zprefix}-box.@{zprefix}-fullscreen {
  background: #FEFEFE;
  border: none !important; }

.@{zprefix}-editor,
.@{zprefix}-textarea {
  position: relative;
  box-sizing: border-box;
  padding: 20px;
  min-height: 300px;
  width: 100%;
  border-style: none;
  resize: none;
  outline: none;
  overflow: auto; }

.@{zprefix}-box-blur .@{zprefix}-editor *, .@{zprefix}-box-blur .@{zprefix}-editor::before {
  color: transparent !important;
  text-shadow: 0 0 7px #333; }
  @media screen and (min-width: 0 0 ) {
    .@{zprefix}-box-blur .@{zprefix}-editor *, .@{zprefix}-box-blur .@{zprefix}-editor::before {
      color: rgba(200, 200, 200, 0.6) !important; } }
  @supports (-ms-accelerator: true) {
    .@{zprefix}-box-blur .@{zprefix}-editor *, .@{zprefix}-box-blur .@{zprefix}-editor::before {
      color: rgba(200, 200, 200, 0.6) !important; } }

.@{zprefix}-box-blur .@{zprefix}-editor img,
.@{zprefix}-box-blur .@{zprefix}-editor hr {
  opacity: 0.2; }

.@{zprefix}-textarea {
  position: relative;
  display: block;
  overflow: auto;
  border: none;
  white-space: normal;
  font-size: 14px;
  font-family: "Inconsolata", "Consolas", "Courier", "Courier New", sans-serif;
  line-height: 18px; }

.@{zprefix}-box.@{zprefix}-editor-visible .@{zprefix}-textarea {
  height: 1px !important;
  width: 25%;
  min-height: 0 !important;
  padding: 0 !important;
  background: none;
  opacity: 0 !important; }

.@{zprefix}-box.@{zprefix}-editor-hidden .@{zprefix}-textarea {
  display: block; }

.@{zprefix}-box.@{zprefix}-editor-hidden .@{zprefix}-editor {
  display: none; }

.@{zprefix}-box.@{zprefix}-disabled .@{zprefix}-textarea {
  opacity: 0.8;
  background: none; }

.@{zprefix}-editor[contenteditable=true]:empty:not(:focus)::before {
  content: attr(placeholder);
  color: #999;
  pointer-events: none; }

.@{zprefix}-button-pane {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: row wrap;
      flex-flow: row wrap;
  width: 100%;
  min-height: 36px;
  background: #ecf0f1;
  border-bottom: 1px solid #d7e0e2;
  margin: 0;
  padding: 0 5px;
  list-style-type: none;
  line-height: 10px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden; }
  .@{zprefix}-button-pane::after {
    content: " ";
    display: block;
    position: absolute;
    top: 36px;
    left: 0;
    right: 0;
    width: 100%;
    height: 1px;
    background: #d7e0e2; }
  .@{zprefix}-button-pane .@{zprefix}-button-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-flow: row wrap;
        flex-flow: row wrap; }
    .@{zprefix}-button-pane .@{zprefix}-button-group .@{zprefix}-fullscreen-button svg {
      color: transparent; }
    .@{zprefix}-button-pane .@{zprefix}-button-group:not(:empty) + .@{zprefix}-button-group::before {
      content: " ";
      display: block;
      width: 1px;
      background: #d7e0e2;
      margin: 0 5px;
      height: 35px; }
  .@{zprefix}-button-pane button {
    display: block;
    position: relative;
    width: 35px;
    height: 35px;
    padding: 1px 6px !important;
    margin-bottom: 1px;
    overflow: hidden;
    border: none;
    cursor: pointer;
    background: none;
    -webkit-transition: background-color 150ms, opacity 150ms;
            transition: background-color 150ms, opacity 150ms; }
    .@{zprefix}-button-pane button.@{zprefix}-textual-button {
      width: auto;
      line-height: 35px; }
  .@{zprefix}-button-pane.@{zprefix}-disable button:not(.@{zprefix}-not-disable):not(.@{zprefix}-active),
  .@{zprefix}-disabled .@{zprefix}-button-pane button:not(.@{zprefix}-not-disable):not(.@{zprefix}-viewHTML-button) {
    opacity: 0.2;
    cursor: default; }
  .@{zprefix}-button-pane.@{zprefix}-disable .@{zprefix}-button-group::before,
  .@{zprefix}-disabled .@{zprefix}-button-pane .@{zprefix}-button-group::before {
    background: #e3e9eb; }
  .@{zprefix}-button-pane button:not(.@{zprefix}-disable):hover,
  .@{zprefix}-button-pane button:not(.@{zprefix}-disable):focus,
  .@{zprefix}-button-pane button.@{zprefix}-active {
    background-color: #FFF;
    outline: none; }
  .@{zprefix}-button-pane .@{zprefix}-open-dropdown::after {
    display: block;
    content: " ";
    position: absolute;
    top: 25px;
    right: 3px;
    height: 0;
    width: 0;
    border: 3px solid transparent;
    border-top-color: #555; }
  .@{zprefix}-button-pane .@{zprefix}-open-dropdown.@{zprefix}-textual-button {
    padding-left: 10px !important;
    padding-right: 18px !important; }
    .@{zprefix}-button-pane .@{zprefix}-open-dropdown.@{zprefix}-textual-button::after {
      top: 17px;
      right: 7px; }
  .@{zprefix}-button-pane .@{zprefix}-right {
    margin-left: auto; }
    .@{zprefix}-button-pane .@{zprefix}-right::before {
      display: none !important; }

.@{zprefix}-dropdown {
  width: 200px;
  border: 1px solid #ecf0f1;
  padding: 5px 0;
  border-top: none;
  background: #FFF;
  margin-left: -1px;
  box-shadow: rgba(0, 0, 0, 0.1) 0 2px 3px; }
  .@{zprefix}-dropdown button {
    display: block;
    width: 100%;
    height: 35px;
    line-height: 35px;
    text-decoration: none;
    background: #FFF;
    padding: 0 10px;
    color: #333 !important;
    border: none;
    cursor: pointer;
    text-align: left;
    font-size: 15px;
    -webkit-transition: all 150ms;
            transition: all 150ms; }
    .@{zprefix}-dropdown button:hover, .@{zprefix}-dropdown button:focus {
      background: #ecf0f1; }
    .@{zprefix}-dropdown button svg {
      float: left;
      margin-right: 14px; }

/* Modal box */
.@{zprefix}-modal {
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  max-width: 520px;
  width: 100%;
  height: 350px;
  z-index: 11;
  overflow: hidden;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden; }

.@{zprefix}-modal-box {
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  max-width: 500px;
  width: calc(100% - 20px);
  padding-bottom: 45px;
  z-index: 1;
  background-color: #FFF;
  text-align: center;
  font-size: 14px;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 3px;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden; }
  .@{zprefix}-modal-box .@{zprefix}-modal-title {
    font-size: 24px;
    font-weight: bold;
    margin: 0 0 20px;
    padding: 15px 0 13px;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #333;
    background: #fbfcfc; }
  .@{zprefix}-modal-box .@{zprefix}-progress {
    width: 100%;
    height: 3px;
    position: absolute;
    top: 58px; }
    .@{zprefix}-modal-box .@{zprefix}-progress .@{zprefix}-progress-bar {
      background: #2BC06A;
      height: 100%;
      -webkit-transition: width 150ms linear;
              transition: width 150ms linear; }
  .@{zprefix}-modal-box label {
    display: block;
    position: relative;
    margin: 15px 12px;
    height: 29px;
    line-height: 29px;
    overflow: hidden; }
    .@{zprefix}-modal-box label .@{zprefix}-input-infos {
      display: block;
      text-align: left;
      height: 25px;
      line-height: 25px;
      -webkit-transition: all 150ms;
              transition: all 150ms; }
      .@{zprefix}-modal-box label .@{zprefix}-input-infos span {
        display: block;
        color: #69878f;
        background-color: #fbfcfc;
        border: 1px solid #DEDEDE;
        padding: 0 7px;
        width: 150px; }
      .@{zprefix}-modal-box label .@{zprefix}-input-infos span.@{zprefix}-msg-error {
        color: #e74c3c; }
    .@{zprefix}-modal-box label.@{zprefix}-input-error input,
    .@{zprefix}-modal-box label.@{zprefix}-input-error textarea {
      border: 1px solid #e74c3c; }
    .@{zprefix}-modal-box label.@{zprefix}-input-error .@{zprefix}-input-infos {
      margin-top: -27px; }
    .@{zprefix}-modal-box label input {
      position: absolute;
      top: 0;
      right: 0;
      height: 27px;
      line-height: 27px;
      border: 1px solid #DEDEDE;
      background: #fff;
      font-size: 14px;
      max-width: 330px;
      width: 70%;
      padding: 0 7px;
      -webkit-transition: all 150ms;
              transition: all 150ms; }
      .@{zprefix}-modal-box label input:hover, .@{zprefix}-modal-box label input:focus {
        outline: none;
        border: 1px solid #95a5a6; }
      .@{zprefix}-modal-box label input:focus {
        background: #fbfcfc; }
  .@{zprefix}-modal-box .error {
    margin-top: 25px;
    display: block;
    color: red; }
  .@{zprefix}-modal-box .@{zprefix}-modal-button {
    position: absolute;
    bottom: 10px;
    right: 0;
    text-decoration: none;
    color: #FFF;
    display: block;
    width: 100px;
    height: 35px;
    line-height: 33px;
    margin: 0 10px;
    background-color: #333;
    border: none;
    cursor: pointer;
    font-family: "Trebuchet MS", Helvetica, Verdana, sans-serif;
    font-size: 16px;
    -webkit-transition: all 150ms;
            transition: all 150ms; }
    .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit {
      right: 110px;
      background: #2bc06a; }
      .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit:hover, .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit:focus {
        background: #40d47e;
        outline: none; }
      .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit:active {
        background: #25a25a; }
    .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset {
      color: #555;
      background: #e6e6e6; }
      .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset:hover, .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset:focus {
        background: #fbfbfb;
        outline: none; }
      .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset:active {
        background: #d5d5d5; }

.@{zprefix}-overlay {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.5);
  width: 100%;
  left: 0;
  display: none;
  z-index: 10; }

/**
 * Fullscreen
 */
body.@{zprefix}-body-fullscreen {
  overflow: hidden; }

.@{zprefix}-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  z-index: 99999; }
  .@{zprefix}-fullscreen.@{zprefix}-box,
  .@{zprefix}-fullscreen .@{zprefix}-editor {
    border: none; }
  .@{zprefix}-fullscreen .@{zprefix}-editor,
  .@{zprefix}-fullscreen .@{zprefix}-textarea {
    height: calc(100% - 37px) !important;
    overflow: auto; }
  .@{zprefix}-fullscreen .@{zprefix}-overlay {
    height: 100% !important; }
  .@{zprefix}-fullscreen .@{zprefix}-button-group .@{zprefix}-fullscreen-button svg {
    color: #222;
    fill: transparent; }

.@{zprefix}-editor {
  /*
     * lset for resetCss option
     */ }
  .@{zprefix}-editor object,
  .@{zprefix}-editor embed,
  .@{zprefix}-editor video,
  .@{zprefix}-editor img {
    max-width: 100%; }
  .@{zprefix}-editor video,
  .@{zprefix}-editor img {
    height: auto; }
  .@{zprefix}-editor img {
    cursor: move; }
  .@{zprefix}-editor.@{zprefix}-reset-css {
    background: #FEFEFE !important;
    font-family: "Trebuchet MS", Helvetica, Verdana, sans-serif !important;
    font-size: 14px !important;
    line-height: 1.45em !important;
    white-space: normal !important;
    color: #333; }
    .@{zprefix}-editor.@{zprefix}-reset-css a {
      color: #15c !important;
      text-decoration: underline !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css div,
    .@{zprefix}-editor.@{zprefix}-reset-css p,
    .@{zprefix}-editor.@{zprefix}-reset-css ul,
    .@{zprefix}-editor.@{zprefix}-reset-css ol,
    .@{zprefix}-editor.@{zprefix}-reset-css blockquote {
      box-shadow: none !important;
      background: none !important;
      margin: 0 !important;
      margin-bottom: 15px !important;
      line-height: 1.4em !important;
      font-family: "Trebuchet MS", Helvetica, Verdana, sans-serif !important;
      font-size: 14px !important;
      border: none; }
    .@{zprefix}-editor.@{zprefix}-reset-css iframe,
    .@{zprefix}-editor.@{zprefix}-reset-css object,
    .@{zprefix}-editor.@{zprefix}-reset-css hr {
      margin-bottom: 15px !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css blockquote {
      margin-left: 32px !important;
      font-style: italic !important;
      color: #555; }
    .@{zprefix}-editor.@{zprefix}-reset-css ul,
    .@{zprefix}-editor.@{zprefix}-reset-css ol {
      padding-left: 20px !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css ul ul,
    .@{zprefix}-editor.@{zprefix}-reset-css ol ol,
    .@{zprefix}-editor.@{zprefix}-reset-css ul ol,
    .@{zprefix}-editor.@{zprefix}-reset-css ol ul {
      border: none;
      margin: 2px !important;
      padding: 0 !important;
      padding-left: 24px !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css hr {
      display: block;
      height: 1px;
      border: none;
      border-top: 1px solid #CCC; }
    .@{zprefix}-editor.@{zprefix}-reset-css h1,
    .@{zprefix}-editor.@{zprefix}-reset-css h2,
    .@{zprefix}-editor.@{zprefix}-reset-css h3,
    .@{zprefix}-editor.@{zprefix}-reset-css h4 {
      color: #111;
      background: none;
      margin: 0 !important;
      padding: 0 !important;
      font-weight: bold; }
    .@{zprefix}-editor.@{zprefix}-reset-css h1 {
      font-size: 32px !important;
      line-height: 38px !important;
      margin-bottom: 20px !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css h2 {
      font-size: 26px !important;
      line-height: 34px !important;
      margin-bottom: 15px !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css h3 {
      font-size: 22px !important;
      line-height: 28px !important;
      margin-bottom: 7px !important; }
    .@{zprefix}-editor.@{zprefix}-reset-css h4 {
      font-size: 16px !important;
      line-height: 22px !important;
      margin-bottom: 7px !important; }

/*
 * Dark theme
 */
.@{zprefix}-dark .@{zprefix}-textarea {
  background: #111;
  color: #ddd; }

.@{zprefix}-dark .@{zprefix}-box {
  border: 1px solid #343434; }
  .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-fullscreen {
    background: #111; }
  .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-box-blur .@{zprefix}-editor *, .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-box-blur .@{zprefix}-editor::before {
    text-shadow: 0 0 7px #ccc; }
    @media screen and (min-width: 0 0 ) {
      .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-box-blur .@{zprefix}-editor *, .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-box-blur .@{zprefix}-editor::before {
        color: rgba(20, 20, 20, 0.6) !important; } }
    @supports (-ms-accelerator: true) {
      .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-box-blur .@{zprefix}-editor *, .@{zprefix}-dark .@{zprefix}-box.@{zprefix}-box-blur .@{zprefix}-editor::before {
        color: rgba(20, 20, 20, 0.6) !important; } }
  .@{zprefix}-dark .@{zprefix}-box svg {
    fill: #ecf0f1;
    color: #ecf0f1; }

.@{zprefix}-dark .@{zprefix}-button-pane {
  background-color: #222;
  border-bottom-color: #343434; }
  .@{zprefix}-dark .@{zprefix}-button-pane::after {
    background: #343434; }
  .@{zprefix}-dark .@{zprefix}-button-pane .@{zprefix}-button-group:not(:empty)::before {
    background-color: #343434; }
  .@{zprefix}-dark .@{zprefix}-button-pane .@{zprefix}-button-group:not(:empty) .@{zprefix}-fullscreen-button svg {
    color: transparent; }
  .@{zprefix}-dark .@{zprefix}-button-pane.@{zprefix}-disable .@{zprefix}-button-group::before {
    background-color: #2a2a2a; }
  .@{zprefix}-dark .@{zprefix}-button-pane button:not(.@{zprefix}-disable):hover,
  .@{zprefix}-dark .@{zprefix}-button-pane button:not(.@{zprefix}-disable):focus,
  .@{zprefix}-dark .@{zprefix}-button-pane button.@{zprefix}-active {
    background-color: #333; }
  .@{zprefix}-dark .@{zprefix}-button-pane .@{zprefix}-open-dropdown::after {
    border-top-color: #fff; }

.@{zprefix}-dark .@{zprefix}-fullscreen .@{zprefix}-button-group .@{zprefix}-fullscreen-button svg {
  color: #ecf0f1;
  fill: transparent; }

.@{zprefix}-dark .@{zprefix}-dropdown {
  border-color: #222;
  background: #333;
  box-shadow: rgba(0, 0, 0, 0.3) 0 2px 3px; }
  .@{zprefix}-dark .@{zprefix}-dropdown button {
    background: #333;
    color: #fff !important; }
    .@{zprefix}-dark .@{zprefix}-dropdown button:hover, .@{zprefix}-dark .@{zprefix}-dropdown button:focus {
      background: #222; }

.@{zprefix}-dark .@{zprefix}-modal-box {
  background-color: #222; }
  .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-title {
    border-bottom: 1px solid #555;
    color: #fff;
    background: #3c3c3c; }
  .@{zprefix}-dark .@{zprefix}-modal-box label {
    display: block;
    position: relative;
    margin: 15px 12px;
    height: 27px;
    line-height: 27px;
    overflow: hidden; }
    .@{zprefix}-dark .@{zprefix}-modal-box label .@{zprefix}-input-infos span {
      color: #eee;
      background-color: #2f2f2f;
      border-color: #222; }
    .@{zprefix}-dark .@{zprefix}-modal-box label .@{zprefix}-input-infos span.@{zprefix}-msg-error {
      color: #e74c3c; }
    .@{zprefix}-dark .@{zprefix}-modal-box label.@{zprefix}-input-error input,
    .@{zprefix}-dark .@{zprefix}-modal-box label.@{zprefix}-input-error textarea {
      border-color: #e74c3c; }
    .@{zprefix}-dark .@{zprefix}-modal-box label input {
      border-color: #222;
      color: #eee;
      background: #333; }
      .@{zprefix}-dark .@{zprefix}-modal-box label input:hover, .@{zprefix}-dark .@{zprefix}-modal-box label input:focus {
        border-color: #626262; }
      .@{zprefix}-dark .@{zprefix}-modal-box label input:focus {
        background-color: #2f2f2f; }
  .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit {
    background: #1b7943; }
    .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit:hover, .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit:focus {
      background: #25a25a; }
    .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-submit:active {
      background: #176437; }
  .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset {
    background: #333;
    color: #ccc; }
    .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset:hover, .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset:focus {
      background: #444; }
    .@{zprefix}-dark .@{zprefix}-modal-box .@{zprefix}-modal-button.@{zprefix}-modal-reset:active {
      background: #111; }

.@{zprefix}-dark .@{zprefix}-overlay {
  background-color: rgba(15, 15, 15, 0.6); }
