@import "~./zul/less/_header.less";

.z-caption {
	background-color: transparent;
	font-size: @fontSizeLarge;
	.size(100%, auto);
	min-height: @baseButtonHeight;
	line-height: @baseLineHeight;

	&-content,
	.z-label {
		display: inline-block;
		line-height: @baseButtonHeight;
	}

	&-content {
		padding: 4px 0;
		& > * {
			margin-left: 16px;

			&:first-child {
				margin-left: 0;
			}
		}
	}

	&-label {
		margin: 0 2px;		
	}

	.z-label {
		// ZK-2209: fix style
		padding: 0;
	}

	&-image {
		vertical-align: middle;
		height: 20px;
		width: 20px;
	}

	input {
		font-size: @fontSizeSmall;
	}

	.z-toolbar {
		.z-a,
		.z-a:visited,
		.z-a:hover {
			color: #FFFFFF;
			border: 0;
			background: none;
		}
	}

	.z-button {
		.boxShadow('none');
		padding: 6px 12px;
		line-height: @baseLineHeight - 2;
		font-size: @fontSizeLarge;
	}

	.z-a,
	.z-a:visited {
		font-size: @fontSizeSmall; 
		font-weight: normal; 
		color: @baseTextColor; 
		background: none;
		text-decoration: none;
	}

	.z-a:hover {
		text-decoration: underline;
	}
}
