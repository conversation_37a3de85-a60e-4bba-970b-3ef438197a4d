<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>bundle</id>
	<formats>
		<format>jar</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<files>
		<file>
			<source>pom.xml</source>
			<outputDirectory>/</outputDirectory>
		</file>
		<file>
			<source>${project.build.directory}/${artifactId}-${version}.jar</source>
			<outputDirectory>/</outputDirectory>
		</file>
	</files>
</assembly>
