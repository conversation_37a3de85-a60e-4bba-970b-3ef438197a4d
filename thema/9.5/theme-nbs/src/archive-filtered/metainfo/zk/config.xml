<?xml version="1.0" encoding="UTF-8"?>

<config>
	<config-name>${theme.name}</config-name><!-- used to resolve dependency -->
	<depends>zul</depends>
	<version>
		<version-class>org.zkoss.theme.${theme.name}.Version</version-class>
		<version-uid>${project.version}</version-uid>
	</version>

	<listener>
		<listener-class>org.zkoss.theme.${theme.name}.ThemeWebAppInit</listener-class>
	</listener>
</config>
