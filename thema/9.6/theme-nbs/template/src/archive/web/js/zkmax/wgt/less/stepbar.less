@import "~./zul/less/_header.less";

.z-stepbar {
	background: @colorBackground3;
	overflow: hidden;
	padding: @stepbarPadding;
	display: flex;
	display: -ms-flexbox;
	font-family: @baseTitleFontFamily;

	&:not(&-linear) .z-step {
		cursor: pointer;
	}
}

.z-step {	
	display: flex;
	display: -ms-flexbox;
	flex-direction: row;
	-ms-flex-direction: row;
	align-items: center;
	flex: 1;
	z-index: 0;
	padding: @stepbarPadding;
	text-align: center;

	&:first-child,
	&:first-child:before {
		flex: 0 1 auto;
		white-space: nowrap;
	}

	&:before {
		content: '';
		height: @stepbarSeparatorSize;
		background-color: @stepInactiveColor;
		flex: 1;
		margin: 0 @stepbarPadding 0 -@stepbarPadding;
	}

	&bar-linear &-active:before,
	&bar-linear &-complete:before {
		background-color: @stepActiveColor;
	}

	&-icon {
		display: inline-block;
		width: @stepSize;
		height: @stepSize;
		margin-right: @stepbarPadding;
		line-height: @stepSize;
		text-align: center;
		font-size: @stepSize;
		vertical-align: top;
		color: @stepInactiveColor;
	}

	&-icon-empty {
		border-radius: 50%;
		border: @stepIconEmptyBorderWidth solid @stepInactiveColor;
		background-color: white;
	}

	&-title {
		background-color: @colorBackground3;
		line-height: @stepSize;
		font-size: @stepFontSize;
		vertical-align: top;
	}

	&-active &-icon,
	&-complete &-icon {
		color: @stepActiveColor;
	}

	&-active &-icon-empty,
	&-complete &-icon-empty {
		border-color: @stepActiveColor;
	}

	&bar:not(&bar-linear) &-complete &-icon:hover {
		color: @stepHoverColor;
	}

	&bar:not(&bar-linear) &-complete &-icon:active {
		color: @stepClickColor;
	}

	&bar:not(&bar-linear) & .z-icon-check:hover {
		color: @textColorDefault3;
		background-color: @stepHoverColor;
		border-color: @stepHoverColor;
	}

	&bar:not(&bar-linear) & .z-icon-check:active {
		color: @textColorDefault3;
		background-color: @stepClickColor;
		border-color: @stepClickColor;
	}

	.z-icon-check {
		color: @textColorDefault3;
		background-color: @stepActiveColor;
		border-color: @stepActiveColor;
		border-radius: 50%;
		font-size: @stepIconCompleteSize;
	}

	&-error &-icon {
		color: @textColorDefault3;
		background-color: @stepErrorColor;
		border-radius: 50%;
		font-size: @stepIconErrorSize;
	}
}

.z-stepbar-wrapped-label {
	.z-step {
		position: relative;
		flex: 2 2 @stepSize;
		align-items: stretch;
	}

	.z-step:first-child, .z-step:last-child {
		flex-grow: 1;
		flex-shrink: 1;
	}

	.z-step:before {
		margin-left: -@stepbarPadding;
		margin-right: 0;
	}

	.z-step:after {
		content: '';
		height: @stepbarSeparatorSize;
		background-color: @stepInactiveColor;
		flex: 1;
		margin-left: 0;
		margin-right: -@stepbarPadding;
	}
	
	.z-step:before,
	.z-step:after {
		margin-top: (@stepSize - @stepbarSeparatorSize) / 2;
	}

	.z-step-content {
		position: relative;
		display: flex;
		flex-direction: column;
		-ms-flex-direction: column;
		align-items: center;
		text-align: center;
		vertical-align: top;
	}

	.z-step-content:before,
	.z-step-content:after {
		position: absolute;
		height: @stepbarSeparatorSize;
		background-color: @stepInactiveColor;
		content: '';
		top: (@stepSize - @stepbarSeparatorSize) / 2;
	}

	.z-step-content:before {		
		left: 0;
		right: calc(50% + (@stepSize / 2));
	}

	.z-step-content:after {
		left: calc(50% + (@stepSize / 2));
		right: 0;
	}

	.z-step:first-child:before,
	.z-step:last-child:after,
	.z-step:first-child .z-step-content:before,
	.z-step:last-child .z-step-content:after {
		display: none;
	}

	&.z-stepbar-linear .z-step-complete:after,
	&.z-stepbar-linear .z-step-complete .z-step-content:after,
	&.z-stepbar-linear .z-step-active .z-step-content:before,
	&.z-stepbar-linear .z-step-complete .z-step-content:before {
		background-color: @stepActiveColor;
	}


	.z-step-icon {
		margin-right: 0;
	}

	.z-step-title {
		display: block;
		margin-top: @stepbarPadding;
	}
}