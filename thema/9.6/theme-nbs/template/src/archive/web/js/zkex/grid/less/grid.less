@import "~./zul/less/_header.less";

.z-row {
	.z-detail-outer {
		min-width: 40px;
		line-height: normal;
		background-color: @meshBackgroundColor;
		vertical-align: top;
		border-top: 1px solid @meshContentBorderColor;
		padding: @meshRowDetailOuterPadding;
	}
}

.z-detail {
	font-size: @baseFontSize;
	.size(@baseIconWidth, @baseIconHeight);
	padding: 0;
	line-height: @baseLineHeight;
	text-align: center;
	overflow: hidden;
	white-space: nowrap;
	cursor: pointer;
	margin-left: -3px;

	&-icon {
		.iconFontStyle(@fontSizeLarge, @textColorDefault);
		position: relative;
		left: 1px;
	}
	&-open .z-detail-icon {
		line-height: @baseLineHeight + 2;
		left: 0;
	}
	&-content {
		padding: @gridDetailContentPadding;
	}
	&-content:before {
		content: '';
		display: block;
		height: 1px;
		border-top: 1px solid @meshContentBorderColor;
		margin: @gridDetailContentBeforeMargin;
	}
}

