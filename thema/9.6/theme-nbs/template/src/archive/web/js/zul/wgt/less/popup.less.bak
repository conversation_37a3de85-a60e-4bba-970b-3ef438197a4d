@import "~./zul/less/_header.less";

.z-popup {
	position: absolute;
	top: 0;
	left: 0;

	&-content {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
		height: 100%;
		padding: @popupPadding;
		line-height: @baseLineHeight;
		border: 1px solid @popupBorderColor;
		.borderRadius(@baseBorderRadius);
		background-color: @popupBackgroundColor;
		.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
	}
}

// notification
.z-notification {
	position: absolute;
	top: 0;
	left: 0;
}

.z-notification-icon {
	position: absolute;
	z-index: 1;

	&.z-icon-times-circle, 
	&.z-icon-exclamation-circle,
	&.z-icon-info-circle {
		.size(32px, 32px);
		top: 50%;
		font-size: 32px;
		line-height: 32px;
		margin-top: -16px;
		left: 16px;
	}
}

.z-notification-pointer + .z-notification-icon {
	left: 12px;
}

.z-notification-left + .z-notification-icon {
	left: 24px;
}

.z-notification-up + .z-notification-icon {
	margin-top: -12px;
}

.z-notification-down + .z-notification-icon {
	margin-top: -22px;
}

.z-notification-content {
	font-family: @baseContentFontFamily;
	font-size: @fontSizeMedium;
	font-weight: normal;
	width: 288px;
	min-height: 80px;
	padding: @notificationContentPadding;
	position: relative;
	overflow: hidden;
	.borderRadius(@baseBorderRadius);
	.boxShadow('0 0 16px rgba(0,0,0,0.16)');

	&::before {
		content: "";
		display: block;
		width: 6px;
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
	}
}

.z-notification-pointer ~ .z-notification-content {
	.displaySize(table-cell, 260px, @notificationPointerContentHeight);
	min-height: @notificationPointerContentHeight; // fix for IE9
	padding: @notificationPointerContentPadding;
	vertical-align: middle;
}

.z-notification-pointer {
	.displaySize(none, 0, 0);
	border: 10px solid transparent;
	position: absolute;
	z-index: 100;
}

// notification arrow: base style
.z-notification-left,
.z-notification-right,
.z-notification-up,
.z-notification-down {
	border: 10px solid transparent;
}

// notification arrow: info
.z-notification-info {
	.z-notification-icon {
		color: @notificationInfoColor;
	}
	.z-notification-close > .z-notification-icon {
		color: @notificationInfoTextColor;
	}
	.z-notification-content {
		color: @notificationInfoTextColor;
		background-color: @notificationInfoBackgroundColor;
		&::before {
			background-color: @notificationInfoColor;
		}
	}
	.z-notification-left {
		border-right-color: @notificationInfoColor;
	}
	.z-notification-right {
		border-left-color: @notificationInfoBackgroundColor;
	}
	.z-notification-up {
		border-bottom-color: @notificationInfoBackgroundColor;
	}
	.z-notification-down {
		border-top-color: @notificationInfoBackgroundColor;
	}
}

// notification arrow: warning
.z-notification-warning {
	.z-notification-icon {
		color: @notificationWarningColor;
	}
	.z-notification-close > .z-notification-icon {
		color: @notificationWarningTextColor;
	}
	.z-notification-content {
		color: @notificationWarningTextColor;
		background-color: @notificationWarningBackgroundColor;
		&::before {
			background-color: @notificationWarningColor;
		}
	}
	.z-notification-left {
		border-right-color: @notificationWarningColor;
	}
	.z-notification-right {
		border-left-color: @notificationWarningBackgroundColor;
	}
	.z-notification-up {
		border-bottom-color: @notificationWarningBackgroundColor;
	}
	.z-notification-down {
		border-top-color: @notificationWarningBackgroundColor;
	}
}

// notification arrow: error
.z-notification-error {
	.z-notification-icon {
		color: @notificationErrorColor;
	}
	.z-notification-close > .z-notification-icon {
		color: @notificationErrorTextColor;
	}
	.z-notification-content {
		color: @notificationErrorTextColor;
		background-color: @notificationErrorBackgroundColor;
		&::before {
			background-color: @notificationErrorColor;
		}
	}
	.z-notification-left {
		border-right-color: @notificationErrorColor;
	}
	.z-notification-right {
		border-left-color: @notificationErrorBackgroundColor;
	}
	.z-notification-up {
		border-bottom-color: @notificationErrorBackgroundColor;
	}
	.z-notification-down {
		border-top-color: @notificationErrorBackgroundColor;
	}
}

.z-notification-close {
	font-size: @notificationCloseFontSize;
	.size(@notificationCloseIconSize, @notificationCloseIconSize);
	padding: 0 2px;
	line-height: @notificationCloseFontSize;
	position: absolute;
	top: 8px;
	right: 8px;
	cursor: pointer;
}

.z-notification-right ~ .z-notification-close {
	right: 16px;
}
.z-notification-up ~ .z-notification-close {
	top: 16px;
}

// Toast
.z-toast {
	position: relative;
	will-change: opacity;

	&-content {
		font-family: @baseContentFontFamily;
		font-size: @fontSizeMedium;
		font-weight: normal;
		width: 320px;
		min-height: 46px;
		padding: 16px 42px 16px 56px;
		position: relative;
		overflow: hidden;
		.borderRadius(@baseBorderRadius);
		.boxShadow('0 0 16px rgba(0,0,0,0.16)');

		&::before {
			content: "";
			display: block;
			width: 6px;
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
		}
	}
	&-close {
		font-size: 18px;
		.size(20px, 20px);
		padding: 0 2px;
		position: absolute;
		top: 50%;
		right: 12px;
		cursor: pointer;
		margin-top: -10px;
	}
	&-icon {
		.z-notification-icon();
	}
	&-error {
		.z-toast-icon {
			color: @notificationErrorColor;
		}
		.z-toast-close > .z-toast-icon,
		.z-toast-content {
			color: @notificationErrorTextColor;
		}
		.z-toast-content {
			background-color: @notificationErrorBackgroundColor;
			&::before {
				background-color: @notificationErrorColor;
			}
		}
	}
	&-info {
		.z-toast-icon {
			color: @notificationInfoColor;
		}
		.z-toast-close > .z-toast-icon,
		.z-toast-content {
			color: @notificationInfoTextColor;
		}
		.z-toast-content {
			background-color: @notificationInfoBackgroundColor;
			&::before {
				background-color: @notificationInfoColor;
			}
		}
	}
	&-warning {
		.z-toast-icon {
			color: @notificationWarningColor;
		}
		.z-toast-close > .z-toast-icon,
		.z-toast-content {
			color: @notificationWarningTextColor;
		}
		.z-toast-content {
			background-color: @notificationWarningBackgroundColor;
			&::before {
				background-color: @notificationWarningColor;
			}
		}
	}

	.top() {
		-ms-flex-direction: column-reverse;
		flex-direction: column-reverse;
		top: 0;
	}

	.middle() {
		top: 50%;
		transform: translateY(-50%);
		-ms-transform: translateY(-50%);
	}

	.bottom() {
		bottom: 0;
	}

	.left() {
		left: 0;
	}

	.right() {
		right: 0;
	}

	.center() {
		left: 50%;
		transform: translateX(-50%);
		-ms-transform: translateX(-50%);
	}

	&-position {
		display: -ms-flexbox;
		display: flex;
		position: fixed;
		-ms-flex-direction: column;
		flex-direction: column;
		z-index: 1800;
	}

	&-position > & {
		margin-top: 10px;
	}

	&-position-top-left { .top(); .left(); }
	&-position-top-center { .top(); .center(); }
	&-position-top-right { .top(); .right(); }
	&-position-middle-left { .middle(); .left(); }
	&-position-middle-center {
		.middle(); .center();
		transform: translate(-50%, -50%);
		-ms-transform: translate(-50%, -50%);
	}
	&-position-middle-right { .middle(); .right(); }
	&-position-bottom-left { .bottom(); .left(); }
	&-position-bottom-center { .bottom(); .center(); }
	&-position-bottom-right { .bottom(); .right(); }
}
