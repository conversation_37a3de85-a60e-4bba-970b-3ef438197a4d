@import "~./zul/less/_header.less";

.z-toolbar {
	display: block;
	border: 0;
	padding: @toolbarPadding;
	background: @baseBackgroundColor;
	position: relative;

	// toolbar horizontal alignment
	&-start {
		float: left;
		clear: none;
	}

	&-end {
		float: right;
		clear: none;
	}

	&-center {
		text-align: center;
		margin: 0 auto;
	}

	&-overflowpopup {
		white-space: nowrap;
		font-size: 0;

		&-button {
			font-size: @toolbarOverflowpopupButtonSize;
		}

		&-off > .z-toolbar-overflowpopup-button {
			display: none;
		}

		&-on {
			> .z-toolbar-overflowpopup-button {
				cursor: pointer;
				position: absolute;
				top: 50%;
				.transform('translateY(-50%)');
				right: @toolbarHorizontalPadding;

				&:hover {
					color: @colorPrimaryLighter;
				}
			}
			> .z-toolbar {
				&-end {
					position: relative;
					right: @toolbarOverflowpopupButtonSize + @toolbarHorizontalPadding;
				}
				&-center {
					padding-right: @toolbarOverflowpopupButtonSize;
				}
			}
		}
	}

	&-popup {
		border: 1px solid @popupBorderColor;
		.borderRadius(@baseBorderRadius);
		background-color: @popupBackgroundColor;
		.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
		display: block;
		position: absolute;
		padding: 0 4px;
		overflow: auto;
		max-height: 350px;

		> * {
			display: table !important;
			margin: 4px 0 4px 8px;
		}

		&-open {
			visibility: visible;
		}

		&-close {
			visibility: hidden;
		}
	}
}

// Toolbar in Tabbox
.z-toolbar.z-toolbar-tabs {
	background-color: @tabboxToolbarBackgroundColor;
	position: absolute;
	right: 0;
	top: 0;
	overflow: hidden;
	z-index: 1;
	min-height: @toolbarInTabboxMinHeight;
}

.z-caption .z-toolbar {
	background: none;
	border: 0;
}

.z-toolbar-content > * {
	margin-left: @toolbarButtonsSpacing;
	&:first-child {
		margin-left: 0;
	}
}

.z-toolbar-vertical {
	.z-toolbar-content > * {
		margin-left: 0;
		display: block;
	}
}

// Toolbar Panel Mold
.z-toolbar-panel {
	background-color: transparent;
}

.z-toolbar-panel .z-toolbar-horizontal,
.z-toolbar-panel .z-toolbar-vertical {
	border: 0;
	padding: 0;
}
.z-toolbar-panel .z-toolbar-horizontal {
	padding-left: @toolbarButtonsSpacing;
}
.z-toolbar-panel .z-toolbar-vertical {
	padding-bottom: @toolbarButtonsSpacing;
}

// Toolbarbutton
.z-toolbarbutton {
	display: inline-block;
	vertical-align: middle;
	position: relative;
	cursor: pointer;
	.borderRadius(@inputBorderRadius);
	overflow: hidden;
	.fontStyle(@baseTitleFontFamily, @toolbarButtonFontSize, normal, @toolbarButtonColor);
	border: @buttonBorderWidth solid transparent;
	background-color: @toolbarButtonBackgroundColor;
	padding: @toolbarButtonPadding;

	// A hack to make the content vertical centering, see ZK-4532
	&:before {
		content: '';
		height: 100%;
		display: inline-block;
		vertical-align: middle;
	}
	&-content {
		display: inline-block;
		text-align: center;
		position: relative;
		white-space: nowrap;
		width: 100%;
		> img {
			vertical-align: text-bottom;
		}
	}
	&:hover {
		color: @buttonHoverColor;
		border-color: @buttonHoverBorderColor;
		background-color: @buttonHoverBackgroundColor;
	}
	&:focus {
		color: @buttonFocusColor;
		border-color: @buttonFocusBorderColor;
		background-color: @buttonFocusBackgroundColor;
	}
	&:active {
		color: @buttonActiveColor;
		border-color: @buttonActiveBorderColor;
		background-color: @buttonActiveBackgroundColor;
	}
	&[disabled] {
		color: @buttonDisableColor !important;
		border-color: @buttonDisableBorderColor;
		background-color: @buttonDisableBackgroundColor;
		cursor: default !important;
	}
	&-checked {
		color: @toolbarButtonCheckedColor;
		background-color: @toolbarButtonCheckedBackgroundColor;
	}
}

