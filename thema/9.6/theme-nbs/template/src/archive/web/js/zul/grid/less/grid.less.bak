@import "~./zul/less/_header.less";

//reset table
.resetTable() {
	table {
		border-spacing: 0;
		th, td {
			background-clip: padding-box;
			padding: 0;
		}
		th {
			text-align: inherit;
		}
	}
}

//grid
.z-grid {
	background: @meshBackgroundColor;
	border: 1px solid @baseBorderColor;
	overflow: hidden;
	zoom: 1;
	//grid header
	&-header {
		width: 100%;
		background: @meshTitleBackgroundColor;
		position: relative;
		overflow: hidden;
		.resetTable();
	}
	//grid body
	&-body {
		margin-top: auto;
		position: relative;
		overflow: hidden;
		.resetTable();
		//ZK-3263: scroll issue (chrome scroll anchor bug)
		overflow-anchor: none;
	}
	&-body &-emptybody td {
		.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @disabledColor);
		text-align: left;
		height: 1px;
	}
	&-body &-emptybody &-emptybody-content {
		background: @meshBackgroundColor;
		padding: @meshEmptyBodyPadding;
	}
	//grid footer
	&-footer {
		background: @meshFootBackgroundColor;
		border-top: 1px solid @baseBorderColor;
		overflow: hidden;
		white-space: nowrap;
		.resetTable();
		
		.z-footer {
			overflow: hidden;
			background: @meshFootBackgroundColor;
		}

		.z-foot-bar {
			background: @meshFootBackgroundColor;
		}
	}
	.z-row-inner, .z-cell {
		background: @meshBackgroundColor;
	}
	&-odd > .z-row-inner,
	&-odd > .z-cell {
		background: @meshStripeBackgroundColor;
	}
	// ZK-3138: ROD loading indicator
	&-loading {
		background: transparent no-repeat center;
		.encodeThemeURL(background-image, @loadingAnimationLoad);
	}
}
//column
.z-columns {
	th:first-child {
		border-left: none;
		// B50-3306729: the first header should have border-left when the first column is covered with other header
		&.z-columns-border {
			border-left: 1px solid @meshTitleBorderColor;
		}
	}
	&-bar {
		background: @meshTitleBackgroundColor;
		border-left: 1px solid @meshTitleBorderColor;
		border-bottom: 1px solid @meshTitleBorderColor;
	}
}
//content
.z-column-content,
.z-row-content,
.z-group-content,
.z-groupfoot-content,
.z-footer-content {
	.fontStyle(@baseContentFontFamily, @fontSizeMedium, normal, @baseTextColor);
	line-height: @meshContentLineHeight;
	overflow: hidden;
	padding: @meshBodyPadding;
}
.z-column-content {
	color: @meshTitleColor;
}
.z-footer-content {
	color: @textColorLight;
}
.z-group-content {
	color: @meshGroupColor;
}
.z-groupfoot-content {
	color: @meshGroupFooterColor;
	padding: @meshBodyPadding;
	.z-group-open & {
		color: @meshGroupFooterOpenColor;
	}
}
.z-columns-menupopup .z-column-content {
	padding-right: 34px;
	text-overflow: ellipsis;
}

.z-column {
	background: @meshTitleBackgroundColor;
	border-left: 1px solid @meshTitleBorderColor;
	border-bottom: 1px solid @meshTitleBorderColor;
	padding: 0;
	position: relative;
	overflow: hidden;
	white-space: nowrap;

	&-button {
		.iconFontStyle(@fontSizeLarge, @meshTitleColor);
		display: none;
		.size(@meshColumnSortButtonWidth, @meshColumnSortButtonHeight);
		line-height: @meshColumnSortButtonHeight;
		text-align: center;
		position: absolute;
		top: 0;
		right: 0;
		z-index: 15;
		cursor: pointer;
		text-decoration: none;
		.boxShadow('inset 1px 0 @{meshTitleBorderColor}');
	}
	&-hover {
		background: @meshTitleHoverBackgroundColor;
		.z-column-button {
			display: block;
		}
		.z-column-content {
			color: @meshTitleHoverColor;
		}
	}
	&-visited {
		background: @meshTitleBackgroundColor;
		.z-column-button {
			background: @meshTitleActiveBackgroundColor;
		}
		.z-column-content {
			color: @meshTitleActiveColor;
		}
	}
	&-sort {
		.z-column-content {
			cursor: pointer;
			&:active {
				background: @meshTitleActiveBackgroundColor;
				color: @meshTitleColor;
			}
		}
		.z-column-sorticon {
			.iconFontStyle(@fontSizeLarge, @meshTitleColor);
			position: absolute;
			top: @meshColumnSortIconTop;
			left: 50%;
			.transform('translateX(-50%)');

			:active {
				background: rgba(0, 0, 0, 0);
			}
		}
	}
	&-sizing,
	&-sizing .z-column-button,
	&-sizing.z-column-sort .z-column-content {
		cursor: col-resize;
	}
}
.z-grid-autopaging {
	.z-row-content,
	.z-groupfoot-content {
		height: @meshAutoPagingRowHeight;
		padding: @meshAutoPagingRowPadding;
		line-height: @meshAutoPagingRowLineHeight;
		overflow: hidden;
	}
	.z-group-content {
		height: @meshAutoPagingRowHeight;
		padding: @meshAutoPagingRowPadding;
		line-height: @meshAutoPagingRowLineHeight;
		overflow: hidden;
	}
}
//Group
.z-group {
	&-inner {
		border-top: 1px solid @meshContentBorderColor;
		border-bottom: 1px solid @meshGroupBorderColor;
		background: @meshGroupBackgroundColor;
		position: relative;
		overflow: hidden;
		
		.z-group-content,
		.z-cell {
			padding: @meshBodyPadding;
		}
	}
	&-icon {
		.iconFontStyle(@fontSizeMedium, @meshGroupColor);
		.displaySize(inline-block, @baseIconWidth, @baseIconHeight);
		line-height: @baseIconHeight;
		text-align: center;
		position: relative;
		cursor: pointer;
		margin-right: 8px;
	}
	&&-open &-inner {
		background: @meshGroupOpenBackgroundColor;
		border-bottom: @meshGroupOpenBorder;
		& .z-group-icon,
		& .z-group-content {
			color: @meshGroupOpenColor;
		}
	}
}
.z-groupfoot-inner {
	border-top: 1px solid @meshContentBorderColor;
	background: @meshGroupFooterBackgroundColor;
	overflow: hidden;
}
//row and cell
//Cell Content
.z-grid-body .z-cell {
	font-family: @baseContentFontFamily;
	font-size: @fontSizeMedium;
	color: @baseTextColor;
	padding: @meshBodyPadding;
	line-height: @baseButtonHeight;
	overflow: hidden;
}
.z-row {
	.z-row-inner,
	.z-cell {
		border-top: 1px solid @meshContentBorderColor;
		overflow: hidden;
		position: relative;
		z-index: 0;
	}
	&:hover {
		> .z-row-inner,
		> .z-cell {
			background: @hoverBackgroundColor;
		}
		> .z-row-inner > .z-row-content { //for nest issue
			color: @hoverColor;
		}
	}
}
//paging
.z-grid-paging {
	&-top {
		width: 100%;
		border-bottom: 1px solid @pagingBorderColor;
		overflow: hidden;
	}
	&-bottom {
		width: 100%;
		border-top: 1px solid @pagingBorderColor;
		overflow: hidden;
	}
}
//column menu
.z-columns-menugrouping .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-group.png');
}
.z-columns-menuungrouping .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-ungroup.png');
}
.z-columns-menuascending .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-arrowup.png');
}
.z-columns-menudescending .z-menuitem-image {
	.encodeThemeURL(background-image, '~./zul/img/grid/menu-arrowdown.png');
}
// Hidden column
.z-cell-hidden-column, .z-row-hidden-column {
	white-space: nowrap;
	overflow: hidden;
}

.ie {
	// Prevent sub-pixel rounding that causes scrollbar appear
	.z-grid-body table {
		overflow: hidden;
	}
}

.ie9 {
	.z-row {
		.z-row-inner,
		.z-cell {
			position: static;
			z-index: auto;
		}
	}
}