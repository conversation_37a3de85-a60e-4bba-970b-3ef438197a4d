@import "~./zul/less/_header.less";

.z-searchbox {
	display: inline-block;
	position: relative;
	height: @searchboxHeight;
	width: 180px;
	border: 1px solid @searchboxBorderColor;
	border-radius: @searchboxBorderRadius;
	color: @searchboxColor;
	background: @searchboxBackgroundColor;
	cursor: pointer;

	&:hover {
		border-color: @searchboxHoverBorderColor;
	}

	&-focus, &-open {
		border-color: @searchboxFocusBorderColor;
	}

	&:active {
		border-color: @searchboxActiveBorderColor;
		background: @searchboxActiveBackgroundColor;
		color: @searchboxActiveColor;
	}
	&:active &-placeholder {
		color: @searchboxActiveColor;
	}
	&:active &-icon {
		color: @searchboxActiveIconColor;
	}

	&[disabled] {
		color: @searchboxDisableColor;
		background: @searchboxDisableBackgroundColor;
		cursor: default;
		border-color: @searchboxDisableBorderColor;
		&-icon {
			color: @searchboxDisableIconColor;
		}
	}
	&[disabled] &-icon {
		color: @searchboxDisableIconColor;
	}

	&:before {
		content: '';
		display: inline-block;
		vertical-align: middle;
		height: 100%;
	}

	&-label {
		user-select: none;
		display: inline-block;
		width: 100%;
		height: 100%;
		line-height: calc(@searchboxHeight - 2px);
		padding: @searchboxPadding;
		padding-right: (@searchboxIconSize * 2 + @searchboxIconRight);
		vertical-align: middle;
		font-family: @baseContentFontFamily;
		font-size: @searchboxFontSize;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	&-placeholder {
		user-select: none;
		display: none;
		width: 100%;
		height: 100%;
		line-height: calc(@searchboxHeight - 2px);
		padding: @searchboxPadding;
		padding-right: (@searchboxIconSize + @searchboxIconRight);
		vertical-align: middle;
		font-family: @baseContentFontFamily;
		font-size: @searchboxFontSize;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		color: @searchboxPlaceholderColor;
	}

	&-icon {
		color: @searchboxIconColor;
		font-size: @searchboxIconSize;
		position: absolute;
		right: @searchboxIconRight;
		top: 50%;
		-ms-transform: translateY(-50%);
		transform: translateY(-50%);
	}

	&-clear {
		right: @searchboxIconSize + @searchboxIconRight; // space for down arrow
	}

	&-popup {
		position: absolute;
		z-index: 1000;
		border: 1px solid @searchboxPopupBorderColor;
		border-radius: @searchboxPopupBorderRadius;
		background: @searchboxPopupBackgroundColor;
		padding: 4px;
		font-family: @baseContentFontFamily;
		font-size: @searchboxFontSize;
		color: @searchboxColor;
	}

	&-shadow {
		box-shadow: @searchboxPopupShadow;
	}

	& &-popup {
		display: none;
	}

	&-search {
		height: @searchboxHeight;
		width: 100%;
		padding: @inputPadding;
		border: 1px solid @inputBorderColor;
		border-radius: @inputBorderRadius;
		background: @inputBackgroundColor;
		margin-bottom: 4px;
		&::placeholder {
			color: @searchboxPlaceholderColor;
			opacity: 1;
		}
		&::-webkit-input-placeholder {
			color: @searchboxPlaceholderColor;
			opacity: 1;
		}
		&::-moz-placeholder {
			color: @searchboxPlaceholderColor;
			opacity: 1;
		}
		&:-ms-input-placeholder {
			color: @searchboxPlaceholderColor;
			opacity: 1;
		}
		&:hover {
			border-color: @inputFocusBorderColor;
		}
		&:focus {
			border-color: @inputFocusBorderColor;
		}
	}

	&-cave {
		margin: 0;
		padding: 0;
		max-height: 350px;
		overflow: auto;
	}

	&-item {
		height: @inputHeight;
		white-space: nowrap;
		list-style: none;
		display: block;
		padding: @searchboxPopupItemPadding;
		cursor: pointer;
		border-radius: @searchboxPopupItemBorderRadius;
		vertical-align: middle;
		-moz-user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		user-select: none;

		&:before {
			content: '';
			display: inline-block;
			vertical-align: middle;
			height: 100%;
		}
	}

	&-item&-selected {
		color: @searchboxPopupItemSelectedColor;
	}

	&-item:hover {
		background: @searchboxPopupItemHoverBackgroundColor;
		color: @searchboxPopupItemHoverColor;
	}

	&-item&-active {
		background: @searchboxPopupItemActiveBackgroundColor;
		color: @searchboxPopupItemActiveColor;
	}

	&-item&-selected&-active,
	&-item&-selected:hover {
		color: @searchboxPopupItemSelectedColor;
	}

	&-item-check {
		display: inline-block;
		vertical-align: middle;
		border: 1px solid @searchboxPopupItemCheckBorderColor;
		background: @searchboxPopupItemCheckBackgroundColor;
		border-radius: @searchboxPopupItemCheckBorderRadius;
		width: @searchboxPopupItemCheckSize;
		height: @searchboxPopupItemCheckSize;
		margin-right: @searchboxPopupItemCheckMarginRight;
	}

	&-item&-selected > &-item-check {
		color: @searchboxPopupItemCheckCheckedColor;
		background: @searchboxPopupItemCheckCheckedBackgroundColor;
		.baseIconFont();
		font-size: @searchboxPopupItemCheckSize;
		&::before {
			content: "\f00c"; // fa-check
		}
	}
}