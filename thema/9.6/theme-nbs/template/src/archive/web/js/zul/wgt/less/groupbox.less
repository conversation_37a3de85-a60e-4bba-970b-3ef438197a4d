@import "~./zul/less/_header.less";

.z-groupbox {
	padding-top: 8px; //NBS

	> .z-groupbox-header {
		background: @baseBackgroundColor;
		.fontStyle(@baseTitleFontFamily, @fontSizeLarge, normal, @groupboxHeaderColor); //NBS
		.size(100%, @baseIconHeight);
		border: 1px solid @baseBorderColor;
		border-bottom: 0;
		padding-left: 8px; //NBS
		line-height: @baseLineHeight; //NBS
		zoom: 1;

		.z-groupbox-title {
			height: auto;
			min-height: 12px; //NBS
			white-space: nowrap;
		}

		.z-groupbox-title-content {
			display: inline-block;
			line-height: @baseHeightGroupBox; //NBS
			padding: 0 4px;
		}

		.z-caption, .z-groupbox-title {
			display: inline;
			width: auto;
			padding: 0 4px;
			line-height: @baseHeightGroupBox; //NBS
			background: @baseBackgroundColor;
			position: relative;
			cursor: pointer;

			&-readonly {
				cursor: default;
			}
		}

		.z-caption-content, .z-label {
			float: none;
		}

	}
	
	> .z-groupbox-readonly .z-groupbox-title {
		cursor: default;
	}
	
	&-content {
		.fontStyle(@baseTitleFontFamily, @fontSizeMedium, normal, @baseTextColor);
		background: @baseBackgroundColor;
		display: block;
		height: inherit; // Firefox bug: content cant be collapsed when scrolling
		border: 1px solid @baseBorderColor;
		border-top: 0;
		padding: @groupboxContentPadding;
		overflow: hidden;
		zoom: 1;
	}

	&-notitle {
		padding: 0;

		.z-groupbox-content {
			border-top: 1px solid @baseBorderColor;
			padding: @groupboxNotitleContentPadding;
		}
	}

	&-collapsed .z-groupbox-header {
		border-left: 1px solid transparent;
		border-right: 1px solid transparent;
	}

	&-3d {
		padding: 0;

		> .z-groupbox-header {
			height: auto;
			min-height: @baseBarHeight;
			border-bottom: 1px solid @baseBorderColor;
			padding: 0;

			.z-caption, .z-groupbox-title {
				display: inline-block;
				width: 100%;
				margin: 0;
				padding: 8px 12px; //NBS
				line-height: @baseHeightGroupBox; //NBS
				background: none;
				top: 0;
			}

			.z-caption-content, .z-groupbox-title-content {
				line-height: @baseIconHeight;
				font-size: @groupboxHeaderFontSize;
				color: @groupboxHeaderColor;
				padding: 0;
				> .z-caption-image {
					margin-left: 0;
					margin-right: 4px;
					height: @sizeHeightCaptionImageGroupBox;
					width: @sizeWidthCaptionImageGroupBox;
				}
			}
		}

		> .z-groupbox-content {
			padding: @groupbox3DContentPadding;
		}
	}
}
