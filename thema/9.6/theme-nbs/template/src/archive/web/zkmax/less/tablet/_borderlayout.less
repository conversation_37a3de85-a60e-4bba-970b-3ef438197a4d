.z-borderlayout-icon {
	font-size: 22px;
	line-height: normal;
	top: 5px;
}
.z-north-header,
.z-south-header,
.z-west-header,
.z-center-header,
.z-east-header {
	font-size: @baseFontSize;
	line-height: 20px;
}
.z-east-splitter,
.z-west-splitter,
.z-north-splitter,
.z-south-splitter {
	overflow: visible;
}
.z-north-splitter-button,
.z-south-splitter-button {
	.size(32px, 16px);
	border-width: 1px;
	.verGradient(@baseGradientStart, @baseGradientEnd);
	top: -4px;
}
.z-west-splitter-button,
.z-east-splitter-button {
	.size(16px, 32px);
	border-width: 1px;
	.horGradient(@baseGradientStart, @baseGradientEnd);
	left: -4px;
}
.z-north-splitter-button-disabled,
.z-south-splitter-button-disabled,
.z-west-splitter-button-disabled,
.z-east-splitter-button-disabled {
	display: none;
}
.z-north-icon,
.z-south-icon,
.z-west-icon,
.z-east-icon {
	font-size: 16px;
	opacity: 0.7;
}
.z-west-icon,
.z-east-icon {
	top: 50%;
	margin-top: -6px;
	left: 4px;
}
.z-east-icon {
	left: 5px;
}
.z-north-icon, .z-south-icon {
	left: 11px;
	top: 50%;
	margin-top: -6px;
}
.z-north-icon {
	margin-top: -7px;
}
.z-north-icon.z-icon-ellipsis-horizontal,
.z-south-icon.z-icon-ellipsis-horizontal,
.z-west-icon.z-icon-ellipsis-vertical,
.z-east-icon.z-icon-ellipsis-vertical {
	display: none;
}
