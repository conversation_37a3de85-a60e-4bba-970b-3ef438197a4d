.z-tabbox-scroll {
	.z-tabs {
		margin: 0 24px;
		
		.z-tabbox-vertical &,
		.z-tabbox-vertical-right & {
			margin: 32px 0;
		}
	}
	.z-tabbox-right,
	.z-tabbox-left {
		width: 32px;
	}
	.z-tabbox-up,
	.z-tabbox-down {
		.z-tabbox-vertical &,
		.z-tabbox-vertical-right & {
			height: 32px;
			padding: 4px 0;
		}
	}
}

.z-tab {
	font-size: 17px;
	padding-right: 2px;
	
	.z-caption-content{
		padding: 4px 0px;
	}
	&-icon {
        font-size: 24px;
		margin-top: -8px;
		left: 3px;
		
		.z-tabbox-accordion & {
			margin-top: -9px;
		}
	}
	&-text {
		.z-tabbox-accordion & {
			padding: 4px 5px 4px 8px;
		}
	}
	&-button {
		font-size: @fontSizeLarge;
		opacity: 0.5;
		
		.z-tabbox-vertical & {
			left: 0;
		}
		.z-tabbox-vertical-right & {
			right: 2px;
		}
		.z-tabbox-accordion & {
			opacity: 0.5;
		}
	}
	&-selected .z-tab-button {
		opacity: 0.7;
		
		.z-tabbox-vertical &,
		.z-tabbox-vertical-right &,
		.z-tabbox-accordion {
			opacity: 0.7;
		}
	}
}

.z-tab-image {
	.size(24px, 24px);
}

.z-tabpanel {
	padding: 0px 0px;
}


.z-toolbar.z-toolbar-tabs {
	padding: 0 4px;
}
.z-tab-button+.z-tab-text {
	margin-right: 16px;
}
.z-tabbox-vertical .z-tab-button+.z-tab-text {
	margin-left: 16px;
}

// Accordion
.z-tabbox-accordion .z-tabpanel>.z-tabpanel-content {
	padding: 0px 0px;
}
