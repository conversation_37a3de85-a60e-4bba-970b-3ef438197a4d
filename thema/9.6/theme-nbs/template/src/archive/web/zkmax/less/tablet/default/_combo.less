.z-combobox,
.z-bandbox,
.z-datebox,
.z-timebox,
.z-spinner,
.z-doublespinner, 
.z-timepicker {
	font-size: 15px;
}

// mobile-specific
.z-timebox-button {
	width: 40px;
	font-size: 22px;
	padding: 5px 8px 0;
	&:hover {
		border-color: @comboButtonHoverBorderColor;
		background: @comboButtonHoverBackgroundColor;
		> i {
			.size(auto, auto);
			border-top-width: 0;
			position: static;
		}
	}
	&:active {
		color: @comboButtonActiveIconColor;
		border-color: @comboButtonActiveBorderColor;
		background-color: @comboButtonActiveBackgroundColor;
	}
}

.z-spinner-input:not(.z-spinner-input-full),
.z-doublespinner-input:not(.z-doublespinner-input-full) {
	padding-right: 65px + 8px;
}

.z-spinner-button,
.z-doublespinner-button {
	width: 65px; // 64 + 1 for border-left
	padding: 0;
	border: none;

	&:hover > i {
		border-top: none;
		position: static;
	}

	> a {
		font-size: 22px;
		.size(32px, 34px);
		padding: 2px 4px;
		border: 1px solid @inputBorderColor;
		line-height: 34px;
		position: static;
		float: right;

		&:first-child {
			border-left: none;
			.rightBorderRadius(@baseBorderRadius);
		}

		&:hover {
			border: 1px solid @comboButtonHoverBorderColor;
			background: @comboButtonHoverBackgroundColor;
		}
		&:active {
			color: @comboButtonActiveIconColor;
			border: 1px solid @comboButtonActiveBorderColor;
			background-color: @comboButtonActiveBackgroundColor;
		}
	}
}

.disabledStyle() {
	color: @inputDisableColor !important;
	border-color: @inputBorderColor !important;
	background: @inputDisableBackgroundColor !important;
	cursor: default !important;
}

.z-timebox-disabled .z-timebox-button {
	.disabledStyle();
	&:hover, &:active {
		.disabledStyle();
	}
}

.z-spinner-disabled .z-spinner-button {
	> a {
		.disabledStyle();
		background: 0 !important;
		&:hover, &:active {
			.disabledStyle();
			background: 0 !important;
		}
	}
}
.z-doublespinner-disabled .z-doublespinner-button {
	> a {
		.disabledStyle();
		background: 0 !important;
		&:hover, &:active {
			.disabledStyle();
			background: 0 !important;
		}
	}
}

.z-combobox-popup, .z-timepicker-popup {
	overflow: hidden;
}
.z-comboitem, .z-timepicker-option {
	font-size: 15px;
	padding: 8px;

	&:after { // for vertical middle
		content: '';
		display: inline-block;
		vertical-align: middle;
		height: 100%;
		width: 0;
	}
	&-inner {
		display: block;
		font-size: 12px;
	}
	&-image {
		float: none;
		.size(22px, 22px);

		&:empty {
			display: none;
		}
	}
}

// ZK-3717 element readonly in tablet
.z-datebox-input[readonly],
.z-timebox-input[readonly] {
	background: @inputBackgroundColor;
	border-color: @inputBorderColor;
	&:focus {
		border-color: @inputFocusBorderColor;
	}
}

//Timebox Wheel
.z-timebox-popup .z-timebox-wheel-body {
	margin: 4px 0;
}
.z-timebox-wheel {
	padding: 0 2px;

	&-time {
		.boxOrientHor();
		width: 50%;
		position: relative;
		background: @baseBackgroundColor;
	}
	&-cave {
		position: relative;
	}
	&-body {
		.boxOrientHor();
		width: 100%;
	}
	&-line {
		.size(100%, 0);
		position: absolute;
		top: 50%;
		z-index: 1;
	}
	&-list {
		.applyCSS3(box-flex, 1);
		color: @textColorDefault;
		background: @baseBackgroundColor;
		height: 120px;
		margin: 0 1px 0 0;
		position: relative;
		overflow: hidden;
		
		ul {
			width: 100%;
			margin: 0;
			padding: 0;
			position: relative;
			list-style: none;
			z-index: 2;
		}
		
		li {
			font-size: 17px;
			display: block;
			height: 40px;
			margin: 0;
			padding: 0;
			line-height: 40px;
			opacity: 0.3;
			text-align: center;
			white-space: nowrap;
			list-style: none;
		}
	}
	&-footer {
		height: 50px;
		padding: 5px 0;
		clear: both;
	}
	&-button {
		.fontStyle(@baseContentFontFamily, 15px, normal, #000000);
		width: 45%;
		border: none;
		.borderRadius(@baseBorderRadius);
		padding: 8px 0;
		.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
	}
	&-left {
		float: left;
		background: @colorPrimary;
		color: @textColorDefault3;
	}
	&-right {
		float: right;
		background: @baseBackgroundColor;
		color: @textColorDefault;
		border: 1px solid @colorGreyLight;
	}
}
li.z-timebox-wheel-list-selected {
	opacity: 1;
}
