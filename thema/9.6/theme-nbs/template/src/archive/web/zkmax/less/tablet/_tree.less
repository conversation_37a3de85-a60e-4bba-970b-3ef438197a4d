.z-tree {
	&-icon,
	&-line {
		.size(24px, 24px);
		line-height: 24px;
		vertical-align: top;
	}
	&-icon {
		font-size: 20px;
		opacity: 0.7;
		left: 3px;
	}
}
.z-treerow {
	&-checkable {
		.size(24px, 24px);
		
		&.z-treerow-radio {
			.borderRadius(12px);
		}
	}

	// Sandbox-G14: use strict selector to override style
	&-selected > .z-treecell > .z-treecell-content
		> .z-treerow-checkable .z-treerow-icon {
		
		font-size: @fontSizeXLarge;
		padding-left: 2px;
		line-height: 22px;

		&.z-icon-radio {
			.size(10px, 10px);
			.borderRadius(5px);
			margin: 6px;
		}
	}
}
.z-treecell-content {
	line-height: 22px;
}
