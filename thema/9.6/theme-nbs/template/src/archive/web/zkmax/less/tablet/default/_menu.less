.z-menu-text, .z-menuitem-text {
	font-size: 17px;
}

.z-menu-image, .z-menuitem-image {
	min-height: 24px;
	min-width: 24px;
}
.z-menupopup .z-menu-image,
.z-menupopup .z-menuitem-image {
	min-height: 24px;
	min-width: 24px;
}
.z-menupopup [class^="z-icon"] {
	font-size: 24px;
	min-width: 24px;
}
.z-menupopup .z-menuitem-icon {
	font-size: 24px;
	top: 10px;
}

.z-menu-icon {
	font-size: 22px;
}

.z-menu-content, .z-menuitem-content {
	padding: 4px 12px;
	min-height: 34px;
	&:before { // text-only menu centering trick
		content: "";
		min-height: 24px; // same as .z-menu-image
		display: inline-block;
		vertical-align: middle;
	}
}
.z-menu-content {
	padding-right: 34px;
}

.z-menubar-icon {
	font-size: 24px;
	margin-top: -14px;
}

// Native scrollable
.z-menubar {
	overflow: auto;
}

.z-menubar-horizontal {
	> ul {
		overflow: visible;
		white-space: nowrap;
	}
	li, .z-menuseparator {
		float: none;
		display: inline-block;
	}
}

.z-menubar-vertical {
	max-width: 36%;
	li {
		margin: 4px 2px;
	}
}
