.z-listhead-menupopup .z-listheader-content {
	padding-right: 24px;
}

.z-listheader-button {
	.size(24px, 32px);
	line-height: 32px;
	font-size: 16px;
	display: block;
}

// Grouping
.z-listgroup-inner .z-listcell-content,
.z-listgroup-inner .z-listgroup-content {
	padding: 3px 5px;
}
.z-listgroup-icon {
	.size(24px, 24px);
	font-size: 22px;
	line-height: 22px;
}

.z-listgroup-selected .z-listgroup-checkable .z-listgroup-icon {
	font-size: 22px;
	line-height: 22px;
}

.z-listgroupfoot .z-listcell-content {
	padding: 4px 5px;
}

// checkbox
.z-listheader-checkable,
.z-listitem-checkable,
.z-listgroup-checkable {
	.size(24px, 24px);
	font-size: 22px;
}

// Sandbox-G14: use strict selector to override style
.z-listitem-selected > .z-listcell > .z-listcell-content
	> .z-listitem-checkable .z-listitem-icon.z-icon-radio {
	.size(16px, 16px);
}
