.z-listheader:last-child .z-listheader-button {
	right: 2px;
}
.z-listheader-button {
	.iconFontStyle(16px, #707070);
	display: block;
	border-left-color: @meshTitleBorderColor;
	right: 1px;
}

.z-listheader-checkable,
.z-listitem-checkable,
.z-listgroup-checkable {
	.size(24px, 24px);
	vertical-align: middle;
}

// Sandbox-G14: use strict selector to override style
.z-listheader-checkable.z-listheader-checked .z-listheader-icon,
.z-listitem-selected > .z-listcell > .z-listcell-content 
	> .z-listitem-checkable .z-listitem-icon {
	font-size: @fontSizeXLarge;
	line-height: 22px;
	padding-left: 2px;
}
.z-listgroup-selected .z-listgroup-checkable .z-listgroup-icon {
	font-size: @fontSizeXLarge;
	line-height: 22px;
}
.z-listitem-checkable.z-listitem-radio {
	.borderRadius(12px);
}
// Sandbox-G14: use strict selector to override style
.z-listitem-selected > .z-listcell > .z-listcell-content 
	> .z-listitem-checkable .z-listitem-icon.z-icon-radio {
	.size(10px, 10px);
	.borderRadius(5px);
	margin: 6px;
}

.z-listgroup-icon {
	font-size: 20px;
	.size(24px, 24px);
	vertical-align: middle;
	
	&-open,
	&-close {
		position: absolute;
		left: 6px;
		top: 4px;
	}
	&-close {
		left: 9px;
	}
}
