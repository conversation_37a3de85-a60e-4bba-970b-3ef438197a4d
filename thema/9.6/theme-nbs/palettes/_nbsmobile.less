@textColorActive:              #0CAFAC;
@colorPrimary:                 #0CAFAC;
@colorPrimaryDark:             #098C89; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #0FCDC9; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #CDEBF6; // List hover Bg
@colorAccent:                  #139FD4;
@colorAccent3:                 #005075; // Tooltip Bg
@colorBackground1:             #266787; // Window Bg, Panel Bg
@containerHeaderColor:         #FFFFFF;
@containerButtonColors:        #FFFFFF, rgba(255,255,255,0.9); // 1: normal, 2: hover

@meshTitleColor:               rgba(0, 0, 0, 0.57);
@meshTitleBackgroundColor:     #ECECEC;
@meshTitleHoverBackgroundColor:#EFEFEF;
@meshTitleActiveBackgroundColor:#BCBCBC;
@meshTitleBorderColor:         #BCBCBC;

@tabboxTabsBackgroundColor:    #ECECEC;
@tabboxTabColor:               rgba(0, 0, 0, 0.57);
@tabboxTabButtonColor:         rgba(0, 0, 0, 0.57);
@tabboxScrollIconColor:        rgba(0, 0, 0, 0.57);
@tabboxTabBackgroundColor:     #ECECEC;
@tabboxSelectedBackgroundColor:#ECECEC;
@tabboxTabHoverBackgroundColor:#EFEFEF;
@tabboxSelectedHoverBackgroundColor:#EFEFEF;
@tabboxSelectedColor:          rgba(0, 0, 0, 0.57);

@borderlayoutHeaderColor:      rgba(0, 0, 0, 0.57);
@groupboxHeaderColor:          rgba(0, 0, 0, 0.57);
@menuItemColor:                rgba(0, 0, 0, 0.57);
@navColor:                     rgba(0, 0, 0, 0.57);
@toolbarButtonColor:           rgba(0, 0, 0, 0.57);

@borderlayoutCollapsedIconColors: rgba(255,255,255,0.68), @textColorLight; // 1: normal, 2: hover
@splitterButtonTextColors:     rgba(255,255,255,0.24), @textColorLighter; // 1: normal, 2: hover

@containerPadding:             0px;

@baseFontSize:                 16px;

//Grid
@meshAutoPagingRowHeight:      44px;
@baseHeightImput:              34px;


//Combo
@comboButtonActiveBackgroundColor: #FFFFFF; 
@splitterBackgroundColorNbs:       #FFFFFF;
@splitterHoverBackgroundColorNbs:  #EFEFEF;
@comboPaddingButton:               6px;
@baseLineHeight:                   20px;

//TabSheet Image
@sizeImageTabSheet: 30px;
//GroupBox
@sizeHeightCaptionImageGroupBox: 25px;
@sizeWidthCaptionImageGroupBox: 25px;

@fontSizeTab:                  17px;