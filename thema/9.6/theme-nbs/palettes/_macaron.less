// <PERSON>aron
@textColorActive:              #F25C05;
@colorPrimary:                 #F25C05;
@colorPrimaryDark:             #D74F00; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #FA7A30; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #DEF3F0; // List hover Bg
@colorAccent:                  #F2B705;
@colorAccent3:                 #005075; // Tooltip Bg
@colorBackground1:             #F4F4F4; // Window Bg, Panel Bg

@meshTitleColor:               rgba(0, 0, 0, 0.57);
@meshTitleBackgroundColor:     #D6F1ED;
@meshTitleHoverBackgroundColor:#DEF3F0;
@meshTitleActiveBackgroundColor:#92D9CF;
@meshTitleBorderColor:         #92D9CF;

@tabboxTabsBackgroundColor:    #D6F1ED;
@tabboxTabColor:               rgba(0, 0, 0, 0.57);
@tabboxTabButtonColor:         rgba(0, 0, 0, 0.57);
@tabboxScrollIconColor:        rgba(0, 0, 0, 0.57);
@tabboxTabBackgroundColor:     #D6F1ED;
@tabboxSelectedBackgroundColor:#D6F1ED;
@tabboxTabHoverBackgroundColor:#DEF3F0;
@tabboxSelectedHoverBackgroundColor:#DEF3F0;
@tabboxSelectedColor:          rgba(0, 0, 0, 0.57);

@borderlayoutHeaderColor:      rgba(0, 0, 0, 0.57);
@groupboxHeaderColor:          rgba(0, 0, 0, 0.57);
@menuItemColor:                rgba(0, 0, 0, 0.57);
@navColor:                     rgba(0, 0, 0, 0.57);
@toolbarButtonColor:           rgba(0, 0, 0, 0.57);
