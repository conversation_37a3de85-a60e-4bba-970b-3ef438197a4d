// Material
@primary2:                     #3F51B5;
@primaryDark2:                 #283593;
@primaryLight2:                #8C9EFF;

@textColorActive:              #FF4081;
@colorPrimary:                 #FF4081;
@colorPrimaryDark:             #F50057; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #FF80AB; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #E1E1E1; // List hover Bg
@colorAccent:                  @primaryLight2;
@colorAccent3:                 #F4F4F4; // Tooltip Bg
@colorBackground1:             @primaryDark2; // Window Bg, Panel Bg
@containerHeaderColor:         #FFFFFF;
@containerButtonColors:        #FFFFFF, rgba(255,255,255,0.9); // 1: normal, 2: hover
@tooltipColor:                 rgba(0, 0, 0, 0.57);

@tabboxTabsBackgroundColor:    @primaryLight2;
@tabboxTabColor:               #FFFFFF;
@tabboxTabHoverColor:          rgba(255,255,255,0.9);
@tabboxTabButtonColor:         #FFFFFF;
@tabboxTabButtonHoverColor:    #FFFFFF;
@tabboxScrollIconColor:        #FFFFFF;
@tabboxScrollIconHoverColor:   rgba(255,255,255,0.9);
@tabboxSelectedBorderColor:    #FF4081;
@tabboxTabBackgroundColor:     @primaryLight2;
@tabboxSelectedBackgroundColor:@primaryLight2;
@tabboxTabHoverBackgroundColor:@primaryLight2;
@tabboxSelectedHoverBackgroundColor:@primaryLight2;
@tabboxSelectedColor:          #FFFFFF;

@meshTitleBackgroundColor:     @primary2;
@meshTitleHoverBackgroundColor:@primaryLight2;
@meshTitleActiveBackgroundColor:@primaryDark2;
@meshTitleBorderColor:         @primaryDark2;
@meshGroupOpenColor:           rgba(0, 0, 0, 0.9);
@meshGroupOpenBorder:          1px solid @meshGroupBorderColor;
@meshGroupFooterOpenColor:     rgba(0, 0, 0, 0.57);

@menuItemColor:                #FFFFFF;
@menuItemHoverColor:           #FFFFFF;
@menuItemBackground:           @primary2;
@menuItemHoverBackground:      @primaryLight2;
@menuItemActiveBackground:     @primaryDark2;
@menuBackground:               @primary2;
@menuScrollableIconColors:     rgba(255,255,255,0.68), rgba(255,255,255,0.9); // 1: normal, 2: hover
@navColor:                     #FFFFFF;
@navHoverColor:                rgba(255,255,255,0.9);
@navBackgroundColor:           @primary2;
@navSelectedBackgroundColor:   transparent;

@borderlayoutCollapsedIconColors: rgba(255,255,255,0.68), @textColorLight; // 1: normal, 2: hover
@splitterButtonTextColors:     rgba(255,255,255,0.24), @textColorLighter; // 1: normal, 2: hover