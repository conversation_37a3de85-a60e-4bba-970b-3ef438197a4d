@textColorActive:              #0CAFAC;
@colorPrimary:                 #0CAFAC;
@colorPrimaryDark:             #098C89; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #0FCDC9; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #CDEBF6; // List hover Bg
@colorAccent:                  #139FD4;
@colorAccent3:                 #005075; // Tooltip Bg
@colorBackground1:             #266787; // Window Bg, Panel Bg
@containerHeaderColor:         #FFFFFF;
@containerButtonColors:        #FFFFFF, rgba(255,255,255,0.9); // 1: normal, 2: hover

@meshTitleColor:               rgba(0, 0, 0, 0.57);
@meshTitleBackgroundColor:     #ECECEC;
@meshTitleHoverBackgroundColor:#EFEFEF;
@meshTitleActiveBackgroundColor:#BCBCBC;
@meshTitleBorderColor:         #BCBCBC;

@tabboxTabsBackgroundColor:    #FFFFFF;
@tabboxTabColor:               rgba(0, 0, 0, 0.57);
@tabboxTabButtonColor:         rgba(0, 0, 0, 0.57);
@tabboxScrollIconColor:        rgba(0, 0, 0, 0.57);
@tabboxTabBackgroundColor:     #FFFFFF;
@tabboxSelectedBackgroundColor:#FFFFFF;
@tabboxTabHoverBackgroundColor:#FFFFFF;
@tabboxSelectedHoverBackgroundColor:#FFFFFF;;
@tabboxSelectedColor:          rgba(0, 0, 0, 0.57);

@borderlayoutHeaderColor:      #FFFFFF;
@groupboxHeaderColor:          rgba(0, 0, 0, 0.57);
@menuItemColor:                rgba(0, 0, 0, 0.57);
@navColor:                     rgba(0, 0, 0, 0.57);
@toolbarButtonColor:           rgba(0, 0, 0, 0.57);

@borderlayoutCollapsedIconColors: rgba(255,255,255,0.68), @textColorLight; // 1: normal, 2: hover
@splitterButtonTextColors:     rgba(255,255,255,0.24), @textColorLighter; // 1: normal, 2: hover
@containerPadding:             0px;
@baseFontSize:                 12px;
@baseLineHeight:               12px;
@fontSizeTab:                  22px;
@baseHeightImput:              24px; //NBS - Inclusão

//Grid
@meshAutoPagingRowHeight:      32px;
//Spinner
@buttonSpinnerBarHeight:       24px;
@buttonSpinnerBarWidth:        12px;

//Button
@buttonBackgroundColor:       #0cafac;
@buttonHoverBackgroundColor:  lighten(@buttonBackgroundColor, 20%);
@buttonActiveBackgroundColor: #098C89; 
@buttonFocusBackgroundColor:  @buttonBackgroundColor;
@buttonFocusBorderColor:      @buttonBackgroundColor;

//Combo
@comboButtonActiveBackgroundColor:  #FFFFFF; 
@splitterBackgroundColorNbs:#FFFFFF;
@splitterHoverBackgroundColorNbs: #EFEFEF;
@comboPaddingButton: 0px 0px 0;
@comboButtonPadding: 0px 0px 0;


//TabSheet Image
@sizeImageTabSheet: 20px;
//GroupBox
@sizeHeightCaptionImageGroupBox: 20px;
@sizeWidthCaptionImageGroupBox: 20px;