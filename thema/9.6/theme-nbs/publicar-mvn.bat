@ECHO OFF
SET MAVEN="C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2021.2.1\plugins\maven\lib\maven3\bin\mvn.cmd"
SET VERSION=9.6.2
rem FREEDOM_BASE_PATH
SET FBP=C:\projects\negocio_delphi\theme-nbs96\target

call %MAVEN% install:install-file -Dfile=%FBP%\nbsmobile.jar -DgroupId=br.com.nbs.freedom -DartifactId=nbsmobile -Dversion=%VERSION% -Dpackaging=jar
call %MAVEN% install:install-file -Dfile=%FBP%\nbs.jar -DgroupId=br.com.nbs.freedom -DartifactId=nbs -Dversion=%VERSION% -Dpackaging=jar


pause