/* Version.java

{{IS_NOTE
	Purpose:
		
	Description:
		
	History:
		Oct 6, 2010 12:30:37 PM , Created by jimmy
}}IS_NOTE

Copyright (C) 2010 Potix Corporation. All Rights Reserved.

{{IS_RIGHT
	This program is distributed under LGPL Version 3.0 in the hope that
	it will be useful, but WITHOUT ANY WARRANTY.
}}IS_RIGHT
*/
package org.zkoss.theme.${theme.name};

/**
 *  The version of the ${theme.name} theme.
 * {@link #UID} must be the same as the version specified in lang-addon.xml.
 * <AUTHOR>
 */
public class Version {
	/** Returns the version UID.
	 */
	public static final String UID = "${project.version}";
}
