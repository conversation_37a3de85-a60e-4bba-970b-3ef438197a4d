// Aquamarine
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #15CAB4;
@colorPrimary:                 #15CAB4;
@colorPrimaryDark:             #10A190; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #2FE9D2; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #3B514F; // List hover Bg
@colorAccent:                  #FF8A0A;
@colorAccent3:                 #F1D5EF; // Tooltip Bg
@colorBackground1:             #454545; // Window Bg, Panel Bg
@colorBackground3:             #2F2F2F; // Container Bg
@colorGreyDark:                #767676;
@colorGreyLight:               #4E4E4E; // Btn disabled Bg
@colorGreyLighter:             #404040; // Field disabled Bg
@inputBackgroundColor:         #262626;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #454545;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';