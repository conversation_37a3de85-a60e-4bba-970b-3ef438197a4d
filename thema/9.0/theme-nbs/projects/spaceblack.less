// Space Black
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #9CA0A1;
@colorPrimary:                 #9CA0A1;
@colorPrimaryDark:             #7B8082; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #AFB3B3; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #52585A; // List hover Bg
@colorAccent:                  #51CBF4;
@colorAccent3:                 #FFFFFF; // Tooltip Bg
@colorBackground1:             #636566; // Window Bg, Panel Bg
@colorBackground3:             #2B2B2B; // Container Bg
@colorGreyDark:                #767676;
@colorGreyLight:               #4E4E4E; // Btn disabled Bg
@colorGreyLighter:             #404040; // Field disabled Bg
@inputBackgroundColor:         #191919;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #6E7476;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';