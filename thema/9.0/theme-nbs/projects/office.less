// Office
@textColorActive:              #0078D7;
@colorPrimary:                 #0078D7;
@colorPrimaryDark:             #0060AC; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #1396FE; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #DEECF9; // List hover Bg
@colorAccent:                  #23A5FF;
@colorAccent3:                 #F4F4F4; // Tooltip Bg
@colorBackground1:             #E1E1E1; // Window Bg, Panel Bg
@containerHeaderColor:         @colorPrimaryDark;
@containerButtonColors:        @colorPrimaryDark, @colorPrimary;  // 1: normal, 2: hover
@tooltipColor:                 rgba(0, 0, 0, 0.57);

@tabboxTabsBackgroundColor:    @colorPrimary;
@tabboxTabSeparatorColor:      transparent;
@tabboxTabColor:               #FFFFFF;
@tabboxTabButtonColor:         #FFFFFF;
@tabboxScrollIconColor:        #FFFFFF;
@tabboxTabBackgroundColor:     @colorPrimary;
@tabboxTabHoverBackgroundColor:@colorPrimaryLight;
@tabboxSelectedRadius:         4px;
@tabboxSelectedHoverBackgroundColor:#FFFFFF;
@tabboxSelectedColor:          @colorPrimary;
@tabboxSelectedBorderColor:    transparent;
@tabboxSelectedBackgroundColor:#FFFFFF;
@tabboxTabHoverColor:          rgba(255,255,255,0.9);
@tabboxTabButtonHoverColor:    rgba(255,255,255,0.9);
@tabboxScrollIconHoverColor:   rgba(255,255,255,0.9);

@meshTitleBorderColor:         #FFFFFF;
@meshGroupOpenColor:           rgba(0, 0, 0, 0.57);
@meshGroupOpenBackgroundColor: #F4F4F4;
@meshGroupOpenBorder:          1px solid @meshGroupBorderColor;

@menuItemColor:                #FFFFFF;
@menuItemBackground:           @colorPrimary;
@menuItemActiveColor:          @textColorLight;
@menuItemActiveBackground:     #FFFFFF;
@menuBackground:               @colorPrimary;
@menuScrollableIconColors:     rgba(255,255,255,0.68), rgba(255,255,255,0.90); // 1: normal, 2: hover

@navColor:                     #FFFFFF;
@navHoverColor:                rgba(255,255,255,0.9);
@navSelectedBackgroundColor:   #FFFFFF;
@navBackgroundColor:           @colorPrimary;
