//Compact variables
@narrowFontSize: 12px;
@narrowIconSizeXSmall: 14px;
@narrowIconSizeSmall: 16px;
@narrowIconSize: 18px;
@narrowIconSizeLarge: 20px;
@narrowContentPadding: 4px 5px;
@narrowItemLineHeight: 20px;
@narrowHeaderLineHeight: 24px;
@narrowBasicHeight: 32px;

@containerHeaderTextSize: 12px;
@containerPadding: 5px;
@baseButtonWidth: 14px;
@baseButtonHeight: 14px;
@buttonPadding: 2px 11px;

// pdfviewer
@pdfviewerToolbarPadding: 3px 8px;
@pdfviewerToolbarButtonSize: 24px;
@pdfviewerToolbarMargins: 6px;

// searchbox
@searchboxHeight: 24px;
@searchboxFontSize: @narrowFontSize;
@searchboxPadding: 0 5px;
@searchboxPopupItemCheckSize: 16px;

// portalchildren
@portalchildrenFrameMargin: 10px;
@portalchildrenFramePadding: 8px;
@portalchildrenFrameTitleFontSize: @narrowFontSize;
@portalchildrenCounterRadius: 10px;
@portalchildrenCounterPadding: 4px;
@portalchildrenFramePanelHeaderTextSize: @narrowFontSize;
@portalchildrenFramePanelHeaderPadding: 6px 10px 0 10px;
@portalchildrenFramePanelChildrenPadding: 6px 10px;
@portalchildrenFramePanelPadding: 8px;

// linelayout
@linelayoutLineWidth: 4px;
@linelayoutCavePadding: 20px;
@linelayoutPointSize: 24px;
@linelayoutPointIconSize: 16px;

// coachmark
@coachmarkPadding: 8px;
@coachmarkPaddingLeft: 10px;
@coachmarkPaddingRight: 22px;
@coachmarkIconSize: 14px;

//cascader
@cascaderHeight: 24px;
@cascaderFontSize: 12px;
@cascaderPadding: 0 5px;
@cascaderPopupCavePadding: 3px;
@cascaderItemHeight: 24px;

//stepbar
@stepbarPadding: 8px;
@stepbarSeparatorSize: 4px;
@stepSize: 24px;
@stepFontSize: 14px;