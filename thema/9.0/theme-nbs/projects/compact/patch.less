@import "~./zul/less/_header.less";

//Norm
.z-label,
.z-radio-content,
.z-checkbox-content,
.z-loading {
	font-size: @narrowFontSize;
}

.z-select {
	font-size: @narrowFontSize;
}
.z-selectbox {
	font-size: @narrowFontSize;
	height: 24px;
	padding-left: 4px;
}

//ZK JavaScript debug box
.z-error {
	padding: 12px 8px;

	.messagecontent {
		font-size: @narrowFontSize;
		padding: 6px 0;
	}

	.button {
		font-size: @narrowIconSizeXSmall;
		.size(@narrowIconSizeXSmall, @narrowIconSizeXSmall);
		margin-left: 5px;

		> .z-icon-times {
			font-size: @narrowIconSizeXSmall;
		}
	}
	.errornumbers {
		font-size: @narrowFontSize;
	}
}
//Loading
.z-loading {
	top: 0;
	left: 0;
}
.z-loading-indicator {
	padding: 10px 16px 4px;
	line-height: @narrowItemLineHeight;
}
.z-apply-loading-indicator {
	font-size: @narrowFontSize;
	padding: 3px 4px 3px 28px;
	position: relative;
	top: 50%;
}
.z-apply-loading-icon,
.z-renderdefer {
	.size(@narrowIconSizeLarge, @narrowIconSizeLarge);
	background: transparent no-repeat center;
	.encodeThemeURL(background-image, @loadingAnimationDefer);
	background-size: contain;
}
.z-loading-icon {
	.size(@narrowIconSizeLarge, @narrowIconSizeLarge);
	background: transparent no-repeat center;
	.encodeThemeURL(background-image, @loadingAnimationLoad);
	background-size: contain;
	margin-left: -10px;
	margin-bottom: 0;
}
.z-apply-loading-icon {
	position: absolute;
	left: 2px;
	top: 50%;
	margin-top: -10px;
}

//Drag-Drop
.z-drop {
	&-content {
		font-size: @narrowFontSize;
		padding: 5px 36px 5px 12px;
		min-height: @narrowBasicHeight;
		line-height: 22px;
	}

	&-icon {
		font-size: @narrowIconSizeSmall;
		.size(@narrowIconSizeSmall, @narrowIconSizeSmall);
		line-height: @narrowIconSizeSmall;
		text-align: center;
		vertical-align: middle;
	}
}

// Window/Panel
.z-panel {
	&-header {
		line-height: @narrowHeaderLineHeight;
		margin: 0;
		padding: 7px 5px 5px;
	}

	&-close {
		line-height: @narrowIconSizeXSmall;
	}

	&-icon {
		font-size: @narrowIconSizeXSmall;
		margin: 2px 5px;
		padding: 0px;
	}
}

.z-window {
	&-header {
		line-height: @narrowHeaderLineHeight;
		margin: 0;
		padding: 3px 0 5px;
	}

	&-icon {
		font-size: @narrowIconSizeXSmall;
		margin: 2px 5px;
		padding: 0px;
	}
}

.z-messagebox {
	&-window {
		padding: @containerPadding;
	}

	&-window .z-window-header, &-window.z-window-modal .z-window-content,
	&-window .z-window-highlighted .z-window-content {
		padding: 3px 5px;
	}

	.z-label {
		font-size: @narrowFontSize;
	}

	&-buttons {
		& > * {
			margin-left: 4px;
		}
	}
}
.z-messagebox-window .z-window-highlighted .z-window-content {
	padding: 20px 12px 15px 18px;
}

//BorderLayout
.z-north,
.z-south,
.z-west,
.z-center,
.z-east {
	&-header {
		font-size: @narrowFontSize;
		line-height: @narrowHeaderLineHeight;
		padding: 4px 4px 3px;
	}

	&-body {
		line-height: 14px;
		padding: 2px;
	}

	&-collapsed {
		.size(@narrowBasicHeight, @narrowBasicHeight);
		padding: 4px;
	}
}
.z-north,
.z-south,
.z-west,
.z-east {
	&-title {
		font-size: @narrowFontSize;
		line-height: @narrowHeaderLineHeight;
	}
}
.z-borderlayout {
	&-icon {
		font-size: @narrowFontSize;
		.displaySize(block, 24px, 24px);
		line-height: 24px;
		position: absolute;
		right: 3px;
	}
}

//Button/Combobutton
.z-button {
	font-size: @narrowFontSize;
	min-height: 24px;
	padding: @buttonPadding;
	line-height: 14px;
}

.z-combobutton {
	&-content {
		font-size: @narrowFontSize;
		line-height: 16px;
		padding: 3px 32px 3px 6px;

		> i {
			vertical-align: bottom;
			font-size: @narrowIconSizeSmall;
		}
	}

	&-icon {
		font-size: @narrowIconSizeSmall;

		&.z-icon-caret-down {
			margin-top: -8px;
			position: absolute;
			top: 50%;
			left: 5px;
		}
	}

	&-image {
		vertical-align: bottom;
	}

	&-button {
		.displaySize(block, 26px, 100%);
	}

	&-toolbar {
		.z-combobutton-content {
			line-height: 16px;
			font-size: @narrowFontSize;
			padding: 3px 32px 3px 6px;
		}
	}
}

//Menu/Menubar
.z-menubar {
	padding: 0px;

	&-horizontal {
		li {
			margin: 2px;
		}

		.z-menuseparator {
			line-height: 26px;
			margin: 2px 4px;
		}
	}
}
.z-menu,
.z-menuitem {
	&-text {
		padding: 1px;
		font-size: @narrowFontSize;
	}

	&-content {
		padding: 2px 6px;
		line-height: @narrowFontSize + 2;
	}

	&-content i {
		vertical-align: text-bottom;
		width: @narrowIconSize;
		height: @narrowIconSize;
		line-height: @narrowIconSize;
		margin-right: 5px;
	}
}
.z-menu {
	&-content {
		padding-right: 26px;
	}

	&-icon {
		font-size: @narrowIconSize;
		top: 2px;
	}
}
.z-menupopup {
	.z-menu-image,
	.z-menuitem-image {
		min-width: @narrowIconSize;
		min-height: @narrowIconSize;
	}

	.z-menu-image,
	.z-colorbox-color {
		min-width: 12px;
		min-height: 12px;
		margin-right: 14px;
	}

	.z-menuitem-icon {
		font-size: @narrowIconSizeXSmall;
		top: 3px;
		left: 8px;
	}

	[class^="z-icon"] {
		min-width: @narrowIconSize;
		min-height: @narrowIconSize;
	}
}

//Toolbar
.z-toolbar {
	padding: 4px 13px;
	line-height: @narrowItemLineHeight;
}
.z-toolbar.z-toolbar-tabs {
	min-height: 30px;
}
.z-toolbarbutton {
	&-content {
		font-size: @narrowFontSize;
		padding: 2px;
		line-height: 16px;

		> img, > i {
			margin-right: 0;
			font-size: @narrowIconSizeSmall;
			vertical-align: bottom;
		}
	}
}
.z-toolbar-content > * {
	margin-left: 6px;
}

//Tabbox
.z-tabbox {
	&-left-scroll,
	&-right-scroll {
		min-height: @narrowBasicHeight;
		width: 24px;
	}

	&-left,
	&-right {
		.z-tabs .z-tab {
			padding: 3px 13px;
		}

		.z-tabs .z-tab .z-tab-content {
			.z-tab-text {
				padding: 0 3px;
			}
		}

		>.z-tabs .z-tab .z-tab-content>.z-tab-text>.z-tab-image {
			margin-left: 0;
			margin-right: 0;
		}
	}

	&-up-scroll,
	&-down-scroll {
		height: 24px;
	}

	&-left {
		> .z-tab-icon {
			left: 10px;
			right: auto;
		}

		> .z-tabs {
			.z-tab {
				border-right: 2px solid transparent;

				&.z-tab-selected {
					border-right: 2px solid @tabboxSelectedBorderColor;
					.leftBorderRadius(@tabboxSelectedRadius);
				}
			}

			.z-tab-button {
				& + .z-tab-text {
					margin-left: 8px;
				}
			}
		}
	}

	&-right {
		> .z-tabs {
			.z-tab-button {
				right: 10px;
			}
		}
	}

	&-accordion {
		.z-tabpanel>.z-tabpanel-content {
			padding: @containerPadding;
		}

		.z-tab-button {
			font-size: @narrowIconSizeXSmall;
			right: 12px;
		}
	}

	&-icon {
		padding: 5px 0;
		line-height: @narrowIconSize;
	}

	// .z-tabbox-scroll
	&-scroll {
		> .z-tabs {
			margin: 0 24px;
		}
	}
}
.z-tabbox-right.z-tabbox-scroll {
	>.z-tabbox-icon {
	padding: 4px 0;
	}

	>.z-tabs {
		margin: 24px 0;
	}
}
.z-tabbox-left.z-tabbox-scroll {
	>.z-tabbox-icon {
		padding: 4px 0;
	}

	>.z-tabs {
		margin: 24px 0;
	}
}
.z-tabs {
	line-height: 14px;
	min-height: @narrowBasicHeight;

	&-content {
		min-height: @narrowBasicHeight;
	}
}
.z-tab {
	font-size: @narrowFontSize;
	line-height: @narrowItemLineHeight;
	padding: 3px 13px;

	&-image {
		width: 12px;
		height: 12px;
	}

	&-text {
		line-height: @narrowHeaderLineHeight;
	}

	&-icon {
		margin-top: -6px;
	}

	&-button {
		font-size: 12px;
		.displaySize(block, @baseButtonHeight, 100%);
		position: absolute;
		right: 0;
		top: 0;

		& + .z-tab-text {
			margin-right: 3px;
		}
	}
}
.z-tabpanel {
	padding: @containerPadding;
}
// IE9/IE10 bug: min-height ignores border-box
.ie9, .ie10 {
	.z-tabbox {
		&-top, &-left, &-right, &-bottom {
			.z-tab {
				height: @narrowBasicHeight;
			}
		}
		&-left-scroll,
		&-right-scroll {
			min-height: 0;
			height: @narrowBasicHeight;
		}
	}
}
.ie11 {
	.z-tabbox {
		&-top, &-left, &-right, &-bottom {
			.z-tab {
				min-height: @narrowBasicHeight;
			}
		}
	}
}

//Groupbox/Caption
.z-caption {
	font-size: @narrowFontSize;
	min-height: @narrowHeaderLineHeight;
	line-height: @narrowItemLineHeight;

	&-content,
	.z-label {
		line-height: @narrowItemLineHeight;
	}

	&-content {
		padding: 0;

		& > * {
			margin-left: 4px;
		}
	}

	&-image {
		height: @narrowIconSizeSmall;
		width: @narrowIconSizeSmall;
	}

	input {
		font-size: @narrowFontSize;
	}

	  .z-button {
		padding: @buttonPadding;
		font-size: @narrowFontSize;
	 }

	.z-a,
	.z-a:visited {
		font-size: @narrowFontSize;
	 }
}

.z-groupbox {
	> .z-groupbox-header {
		font-size: @narrowFontSize;
		line-height: @narrowItemLineHeight;
	}

	&-content {
		font-size: @narrowFontSize;
		padding: @containerPadding;
	}

	.z-caption-content > * {
		margin-left: 3px;
	}

	&-notitle {
		.z-groupbox-content {
			padding: @containerPadding;
		}
	}

	&-3d {
		> .z-groupbox-header {
			height: auto;
			line-height: 30px;

			.z-caption, .z-groupbox-title {
				 padding: 3px 4px;
			}

			.z-caption-content, .z-groupbox-title-content {
				font-size: @narrowFontSize;
			}

			.z-caption-content, .z-groupbox-title-content {
				line-height: @narrowItemLineHeight;
				font-size: @narrowFontSize;
				padding: 3px 4px;

				> .z-caption-image {
					margin-left: 0;
					margin-right: 0;
				}
			}
		}

		> .z-groupbox-content {
			padding: @containerPadding;
		}
	}
}

//Navbar
.z-navbar {
	padding: 0px;
	// overall style
	> ul { //first level
		ul { //second level
			.z-nav-text,
			.z-navitem-text {
				font-size: @narrowFontSize;
			}
		}
	}
	// horizontal style
	&-horizontal {
		line-height: 2@narrowItemLineHeight;
		padding: 0px 2px;

		.z-navseparator {
			line-height: 36px;
		}
	}
}
.z-nav,
.z-navitem {
	&-content {
		padding: 6px 12px;
	}

	&-image {
		max-width: @narrowIconSizeSmall;
		max-height: @narrowIconSizeSmall;
	}

	&-image,
	i {
		margin-right: 8px;
	}

	&-text {
		.fontStyle(@baseContentFontFamily, 12px, normal);
	}
}
.z-nav-info {
	position: absolute;
	right: 16px;
	top: 8px;
}
.z-nav-text-popup,
.z-navitem-text-popup {
	min-width: 180px;
	height: 38px;
	padding: 10px 18px 4px;
	line-height: @narrowItemLineHeight;
}
.z-nav-popup {
	padding: 0 4px 6px;

	.z-nav-text,
	.z-navitem-text {
		font-size: @narrowFontSize;
	}
}
.z-navbar-collapsed {
	.z-nav-info {
		position: relative;
		top: 0px;
		left: 0;
	}
}

//grid
.z-grid {
	&-body &-emptybody td {
		font-size: @narrowFontSize;
	}

	&-body &-emptybody &-emptybody-content {
		padding: @narrowContentPadding;
		line-height: 24px;
	}
}

//Group
.z-group {
	&-inner {
		.z-group-content,
		.z-cell {
			line-height: 24px;
			padding: @narrowContentPadding;
		}
	}

	&-icon {
		vertical-align: text-top;
	}
}

//Cell Content
.z-grid-body .z-cell {
	font-size: @narrowFontSize;
	padding: @narrowContentPadding;
	line-height: 24px;
}

//mesh column
.z-column {
	&-sort {
		.z-column-sorticon {
			position: absolute;
			top: -7px;
			left: 50%;
		}
	}
	&-button {
		.size(34px, 32px);
		line-height: @narrowBasicHeight;
	}
}
.z-row {
	.z-detail-outer {
		padding: 15px 8px;
	}
}
.z-column-content,
.z-row-content,
.z-group-content,
.z-groupfoot-content,
.z-footer-content,
.z-auxheader-content {
	font-size: @narrowFontSize;
	line-height: @narrowHeaderLineHeight;
	padding: @narrowContentPadding;
}

//Listbox
.z-listbox {
	&-body &-emptybody td {
		font-size: @narrowFontSize;
	}

	&-body &-emptybody &-emptybody-content {
		padding: @narrowContentPadding;
		min-height: @narrowBasicHeight;
		line-height: 24px;
	}
}
.z-listheader {
	&-sort {
		.z-listheader-sorticon {
			position: absolute;
			top: -7px;
			left: 50%;
		}
	}
	&-checkable {
		.displaySize(inline-block, @narrowIconSize, @narrowIconSize);
		.size(@narrowIconSize, @narrowIconSize);
		position: relative;
		left: 2px;
		
		&:before {
			.displaySize(block, 12px, 12px);
		}
	}
}
.z-listitem {
	//check mark
	&-checkable {
		.displaySize(inline-block, @narrowIconSize, @narrowIconSize);
		position: relative;
		left: 2px;
	}

	&-selected {
		> .z-listcell > .z-listcell-content
			> .z-listitem-checkable .z-listitem-icon {
			&.z-icon-radio { //for radio
				.size(12px, 12px);
			}
		}
	}
}
.z-listgroup {
	&-inner {
		.z-listcell-content,
		.z-listgroup-content {
			padding: @narrowContentPadding;
		}
	}
	//check mark
	&-checkable {
		.displaySize(inline-block, @narrowIconSize, @narrowIconSize);
		position: relative;
		left: 2px;
	}
}
.z-listgroupfoot {
	& .z-listcell-content {
		padding: @narrowContentPadding;
	}
}
.z-listheader-content,
.z-listcell-content,
.z-listgroup-content,
.z-listgroupfoot-content,
.z-listfooter-content {
	font-size: @narrowFontSize;
	padding: @narrowContentPadding;
	line-height: 24px;
	min-height: @narrowBasicHeight;
}

//Tree
.z-treerow {
	//check mark
	&-checkable {
		.displaySize(inline-block, @narrowIconSize, @narrowIconSize);
		.iconFontStyle(@checkedIconSize, @checkedColor);
	}
}
.z-treecol-content,
.z-treecell-content,
.z-treefooter-content {
	font-size: @narrowFontSize;
	padding: @narrowContentPadding;
	line-height: 24px;
	min-height: @narrowBasicHeight;
}

//Checkbox
.z-checkbox {
	&-switch > &-mold {
		margin: 6px;
		position: relative;
		width: 38px;
		height: 14px;

		&:before {
			width: @narrowIconSize;
			height: @narrowIconSize;
			left: -2px;
			bottom: -2px;
			.boxShadow('0 2px 4px 0 rgba(0,0,0,0.16)');
		}
	}

	&-switch-on > &-mold {
		&:before {
			.transform('translateX(26px)');
		}
	}

	&-toggle > &-mold {
		width: 20px;
		height: 20px;
	}
}


//input
input[type="checkbox"] {
	margin: 0 4px 2px 4px;
	font-size: @narrowIconSize - 2px;
	width: @narrowIconSize;
	height: @narrowIconSize;
	line-height: @narrowIconSize - 2px;
}

//radio, radiogroup
input[type="radio"] {
	margin: 0 4px 2px 4px;
	.size(@narrowIconSize, @narrowIconSize);

	&:before {
		.displaySize(block, 12px, 12px);
	}
}

.z-textbox,
.z-decimalbox,
.z-intbox,
.z-longbox,
.z-doublebox {
	font-size: @narrowFontSize;
	height: 24px;
	padding: 4px 5px;
	line-height: 14px;
}

// error box
.z-errorbox {
	& > .z-errorbox-icon {
		position: absolute;
		top: 5px;
	}
}
.z-errorbox-up + .z-errorbox-icon {
	top: 13px;
}
.z-errorbox-content {
	font-size: @narrowFontSize;
	padding: 5px 20px 5px 34px;
	line-height: 16px;
}
.z-errorbox-icon {
	position: absolute;
	top: -6px;
}

//Combo
.z-combobox,
.z-bandbox,
.z-datebox,
.z-timebox,
.z-spinner,
.z-doublespinner {
	height: 24px;
	line-height: @narrowItemLineHeight;

	&-input {
		font-size: @narrowFontSize;
		height: 24px;
		line-height: 14px;
		padding: @narrowContentPadding;
		padding-right: 29px;
	}

	&-button {
		.iconFontStyle(@narrowIconSize, @comboButtonIconColor);
		min-width: 24px;
		height: 24px;
		padding: 2px;
		right: 24px;
	}
}

.z-timebox-button,
.z-spinner-button,
.z-doublespinner-button {
	width: 24px;
	padding: 0;
	position: relative;

	& > a {
		height: 24px / 2;
		padding: 0;

		> i {
			.transform('translateY(-4px);')
		}
	}
	// separator
	&:hover > i {
		position: absolute;
		top: 50%;
	}
}

// Inplace editing
.z-combobox-inplace .z-combobox-input,
.z-bandbox-inplace .z-bandbox-input,
.z-datebox-inplace .z-datebox-input,
.z-timebox-inplace .z-timebox-input,
.z-spinner-inplace .z-spinner-input,
.z-doublespinner-inplace .z-doublespinner-input {
	border: 0;
	padding: 3px;
	background: none;
}

// combobox emptySearchMessage
.z-combobox-emptySearchMessage {
	padding: 4px 8px;
	position: relative;
	min-height: @comboPopupItemSize + 2px + 4px * 2;
}

// Comboitem
.z-comboitem,
.z-comboitem-button {
	font-size: @narrowFontSize;
	white-space: nowrap;
	cursor: pointer;
}
.z-comboitem {
	padding: 3px;
	position: relative;
	min-height: 12px + 4px * 2; //ZK-2783: give default height to empty item
}
.z-comboitem-inner,
.z-comboitem-content {//description
	.iconFontStyle(12px, @comboPopupDescColor);
}
.z-comboitem,
.z-comboitem a,
.z-comboitem a:visited {
	font-size: @narrowFontSize;
}

.z-comboitem-text {
	line-height: 12px + 2px;
}
.z-comboitem-icon {
	.iconFontStyle(@comboPopupIconSize, @comboPopupIconColor);
	padding-right: 4px;
}
.z-combobox-popup,
.z-bandbox-popup,
.z-datebox-popup,
.z-timebox-popup {
	font-size: @narrowFontSize;
	padding: 3px;
}

//Popup
.z-popup {
	&-content {
		font-size: @narrowFontSize;
		padding: 4px 10px;
	}
}

//paging
.z-paging {
	height: @narrowBasicHeight;

	&-button {
		font-size: @narrowFontSize;
		min-width: 24px;
		height: 24px;
		padding: 4px 3px;
		margin: 0;
	}

	.z-paging-icon {
		font-size: @narrowIconSize;
		line-height: @narrowIconSize;
	}

	&-input {
		font-size: @narrowFontSize;
		.size(42px, 24px);
		padding: 0 8px;
	}

	&-text {
		font-size: @narrowFontSize;
	}

	&-info {
		font-size: @narrowFontSize;
	}
}
.z-paging-os {
	.z-paging-button {
		font-size: @narrowFontSize;
		padding: 4px 3px;
	}
}

//Progressmeter
.z-progressmeter {
	height: @narrowIconSizeXSmall;
}

//Calendar
.z-calendar {
	font-size: 14px;
	padding: 8px 4px;

	th {
		font-size: @narrowFontSize;
	}

	&-title {
		font-size: 14px;
	}

	&-header {
		&:first-child { // calendar title
			line-height: 26px;
		}
	}

	&-decade,
	&-month,
	&-year {
		.z-calendar-cell {
			.size(56px, 44px);
		}
	}

	&-wk& {
		.z-calendar-decade, .z-calendar-month, .z-calendar-year {
			.z-calendar-cell {
				.size(64px, 44px);
			}
		}
	}

	&-cell {
		font-size: @narrowFontSize;
		.size(32px, 24px);
	}

	&-icon {
		font-size: @narrowIconSizeSmall;
		.size(24px, 24px);
	}

	&-right {
		right: 0px;
	}

	&-left {
		left: 0px;
	}

	&-today {
		margin-top: 5px;

		.z-calendar-title {
			padding: 4px 0;
		}
	}
}


// Calendar and Datebox
.z-datebox-timezone {
	font-size: @narrowFontSize;
}

//Chosenbox
.z-chosenbox {
	line-height: normal;
	padding: 0 0 2px 0;
	min-height: 25px;

	&-item {
		margin: 1px 0 0 2px;
		padding: 0;

		&-content {
			font-size: @narrowFontSize;
			padding: 0 2px;
			margin-right: 16px;
		}
	}

	&-button {
		font-size: @narrowIconSizeSmall;
		.size(@narrowIconSizeSmall, @narrowIconSizeSmall);
		line-height: @narrowIconSizeSmall;
		position: absolute;
		right: 2px;
		top: -1px;
	}

	&-input {
		font-size: @narrowFontSize;
		.displaySize(inline-block, 17px, 17px);
		padding: 1px 2px 2px;
	}

	&-textcontent {
		font-size: @narrowFontSize;
	}

	&-popup {
		font-size: @narrowFontSize;
		padding: 3px;
	}

	&-option {
		padding: 1px 8px;
		min-height: 16px;
	}

	&-empty {
		padding: 1px 8px;
		min-height: 16px;
	}
}

//Colorbox
.z-colorbox {
	.displaySize(inline-block, 48px, 32px);
	padding: 3px;
}

.z-colorbox-popup,
.z-menu-popup {
	padding: 0;
}

// Colorpicker
.z-colorpicker {
	.size(306px, 420px);
	padding: 4px;

	&-main {
		top: 30px;
	}

	&-info {
		margin-top: 12px;
		height: 114px;
		top: 30px;
	}

	&-color {
		.size(56px, 72px);
		right: 8px;
	}

	&-newcolor {
		.size(50px, 33px);
	}

	&-current {
		.size(50px, 33px);
	}

	&-rgb, &-hsv {
		.z-colorpicker-input {
			width: 40px;
			margin: 0 0 0 8px;
		}
	}

	&-rgb {top: 5px;}

	&-hsv {top: 40px;}

	&-text,
	&-input {
		font-size: @narrowFontSize;
	}

	&-input {
		height: 24px;
		text-align: center;
	}

	&-hex {
		position: absolute;
		top: 76px;

		.z-colorpicker-input {
			width: 72px;
			height: 32px;
			margin-left: 8px;
		}
	}

	&-button {
		top: 82px;
		right: 8px;
		width: 42px;
	}

}
// Color Palette
.z-colorpalette {
	.size(260px, 226px);
	padding: 4px;

	&-head {
		height: 35px;
	}

	&-newcolor {
		.size(48px, 30px);
		right: 77px;
	}

	&-input,
	&-button {
		position: absolute;
		top: 0;
		right: 0;
	}

	&-input {
		.size(72px, 30px);
	}

	&-color {
		.displaySize(inline-block, 12px, 12px);
	}
}
.z-colorbox-paletteicon,
.z-menu-paletteicon {
	left: 5px;
	top: 5px;
}
.z-colorbox-pickericon,
.z-menu-pickericon {
	left: 30px;
	top: 5px;
}
.z-colorbox-paletteicon,
.z-menu-paletteicon,
.z-colorbox-pickericon,
.z-menu-pickericon {
	.size(22px, 22px);
	font-size: @narrowIconSizeSmall;
	padding: 3px;
	line-height: @narrowIconSizeSmall;
}

//Notification
.z-notification-content {
	font-size: @narrowFontSize;
	vertical-align: middle;
	padding: 32px 16px 32px 56px;
}
.z-notification-pointer ~ .z-notification-content {
	.displaySize(table-cell, 260px, 50px);
	min-height: 50px; // fix for IE9
	padding: 8px 34px 8px 48px;
}
.z-notification-close {
	font-size: @narrowIconSizeXSmall;
	.size(@narrowIconSizeXSmall, @narrowIconSizeXSmall);
	padding: 0 2px;
	line-height: @narrowIconSizeXSmall;
}


//Biglistbox
.z-biglistbox {
	&-outer {
		margin: 0 15px 15px 0;
	}

	//head
	&-header {
		&-content {
			font-size: @narrowFontSize;
			line-height: 19px;
		}
	}

	//body
	&-body {
		//body content
		td {
			font-size: @narrowFontSize;
			line-height: 19px;
		}
	}

	&-verticalbar-tick {
		.size(7px, 15px);
		position: absolute;
		font-size: @narrowIconSizeSmall;
		bottom: 0;

		&:before {
			left: -5px;
		}
	}

	//WScroll bar
	&-wscroll {
		// vertical
		&-vertical {
			.size(15px, 100%);
			position: absolute;
			right: -16px;

			.z-biglistbox-wscroll-drag {
				.size(15px, 119px);

				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up,
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end {
					.size(15px, 16px);

					&:before {
						position: relative;
						right: 2px;
					}
				}

				.z-biglistbox-wscroll-body {
					.size(15px, 55px);
					top: 32px;

					&:before {
						top: 19px;
						right: -1px;
					}

					&:hover, &:active {
						top: 31px;
					}
				}

				.z-biglistbox-wscroll-up {
					top: 16px;

					&:hover, &:active {
						top: 15px;
					}
				}

				.z-biglistbox-wscroll-down {
					bottom: 16px;
				}
			}

			.z-biglistbox-wscroll-endbar {
				.size(15px, 8px);
			}
		}

		// horizontal
		&-horizontal {
			.size(100%, 15px);
			bottom: -16px;

			.z-biglistbox-wscroll-drag {
				.size(119px, 15px);

				.z-biglistbox-wscroll-home,
				.z-biglistbox-wscroll-up,
				.z-biglistbox-wscroll-down,
				.z-biglistbox-wscroll-end {
					.size(16px, 15px);
				}

				.z-biglistbox-wscroll-body {
					.size(55px, 15px);
					left: 32px;

					&:before {
						left: 19px;
						bottom: 0;
					}

					&:hover, &:active {
						left: 31px;
					}
				}

				.z-biglistbox-wscroll-up {
					left: 16px;
					border-left: 0;

					&:hover, &:active {
						left: 15px;
					}
				}

				.z-biglistbox-wscroll-down {
					right: 16px;
				}

				.z-biglistbox-wscroll-end {
					right: 0;
				}
			}

			.z-biglistbox-wscroll-pos {
				.size(119px, 15px);
			}

			.z-biglistbox-wscroll-endbar {
				.size(7px, 15px);
				position: absolute;
				right: -12px;
			}
		}
	}
}

//Rating
.z-rating {
	font-size: @narrowIconSizeXSmall;
	line-height: @narrowIconSizeXSmall;

	> .z-rating-icon {
		padding: 3px 2px;
		width: 20px;
		height: 20px;
	}
}

//Timepicker
.z-timepicker {
	min-height: 24px;

	&-input {
		font-size: @narrowFontSize;
		height: 24px;
		line-height: @narrowFontSize;
		padding-right: 24px + 8px;
	}

	&-button {
		.iconFontStyle(@narrowIconSize, @comboButtonIconColor);
		min-width: 24px;
		height: 24px;
		padding: 3px 0 0;
		right: 24px;
	}
}
.z-timepicker-option {
	font-size: @narrowFontSize;
	padding: 3px;
}
.z-timepicker-popup {
	padding: 3px;
}

//a
.z-a {
	font-size: @narrowFontSize;
}

//Organigram
.z-organigram {
	overflow: auto;
	font-size: @narrowFontSize;
	line-height: 14px;
}
.z-orgnode {
	padding: 4px 10px;
}

//Cropper
.z-cropper {
	&-toolbar {
		> ul {
			> li {
				padding: 5px 11px;
			}
		}
	}
}
//GoldenLayout
.z-goldenlayout {
	.lm_header {
		line-height: @narrowHeaderLineHeight;
		min-height: 30px;
		font-size: @narrowFontSize;

		.lm_tab {
			line-height: @narrowHeaderLineHeight;
			top: -1px;
			padding: 4px 12px 3px;

			.lm_title {
				line-height: @narrowHeaderLineHeight;
			}

			.lm_close_tab {
				width: @narrowFontSize;
				height: @narrowFontSize;
				top: 0px;
			}

			&.lm_active {
				&:after {
					margin: -1px -13px;
				}
			}
		}

		.lm_controls {
			padding: 0;

			> li {
				width: @narrowFontSize;
				line-height: @narrowFontSize;
				padding: 9px 11px 2px 5px;
			}
		}
	}
}
.z-goldenlayout-dragProxy {
	.lm_header .lm_tabs {
		font-size: @narrowFontSize;

		.lm_tab {
			font-size: @narrowFontSize;
			padding: 4px 12px 3px;
			line-height: @narrowHeaderLineHeight;

			&:after {
				margin: -10px -12px;
			}
		}
	}
}

//Signature
.z-signature {
	&-toolbar {
		position: absolute;
		bottom: 6px;
		right: 10px;

		&-hide {
			display: none;
		}
	}

	&-tool-button {
		font-size: @narrowFontSize;
		min-height: 24px;
		margin-right: 5px;

		&-icon {
			font-size: 16px;
		}
	}
}

//Drawer
.z-drawer {
	&-header {
		padding: 9px 8px;
	}

	&-title {
		font-size: @narrowFontSize;
	}

	&-close {
		.size(@narrowFontSize, @narrowFontSize);
		font-size: @narrowFontSize;
		position: absolute;
		right: 8px;
		top: 9px;
	}

	&-cave {
		padding: 8px;
	}
}

//Toast
.z-toast {
	&-content {
		.z-notification-content();
	}
	&-close {
		.z-notification-close();
	}
}

//Rangeslider
.z-rangeslider {
	&-track {
		border-radius: @borderRadiusSmall;
	}
	&-mark {
		&-dot {
			width: 12px;
			height: 12px;
			border-width: 3px;
		}
		&-label {
			font-size: @fontSizeXSmall;
		}
	}
	& .z-sliderbuttons {
		&-button {
			width: 16px;
			height: 16px;
		}
		&-tooltip {
			border-radius: 2px;
			font-size: @fontSizeXSmall;
		}
	}

	&-horizontal {
		& .z-sliderbuttons {
			&-button {
				margin-top: -5px;
				margin-left: -8px;
			}
			&-tooltip {
				top: -8px;
				padding: 2px 4px;
			}
		}
	}
	&-horizontal &-inner {
		height: 6px;
	}
	&-horizontal &-mark-dot {
		top: -3px;
	}

	&-horizontal &-mark-label {
		top: 19px;
	}

	&-vertical {
		& .z-sliderbuttons {
			&-button {
				margin-top: -8px;
				margin-left: -5px;
			}
			&-tooltip {
				left: 24px;
				padding: 4px 2px;
			}
		}
	}

	&-vertical &-inner {
		width: 6px;
	}

	&-vertical &-mark-dot {
		left: -3px;
	}

	&-vertical &-mark-label {
		left: 19px;
	}
}

//Multislider
.z-multislider {
	&-mark {
		&-label {
			font-size: @fontSizeXSmall;
		}
	}

	& .z-sliderbuttons {
		&-tooltip {
			border-radius: 2px;
			font-size: @fontSizeXSmall;
		}
	}
	&-horizontal {
		& .z-sliderbuttons {
			&-button {
				margin-top: -5px;
				margin-left: -8px;
			}
			&-tooltip {
				top: -8px;
				padding: 2px 4px;
			}
		}
	}

	&-horizontal &-inner {
		height: 6px;
	}

	&-horizontal &-mark-label {
		top: 19px;
	}

	&-vertical {
		& .z-sliderbuttons {
			&-button {
				margin-top: -8px;
				margin-left: -5px;
			}
			&-tooltip {
				left: 24px;
				padding: 4px 2px;
			}
		}
	}

	&-vertical &-inner {
		width: 6px;
	}

	&-vertical &-mark-label {
		left: 19px;
	}
}

//inputgroup
.z-inputgroup {
	&-text {
		min-height: 24px;
	}
}

//stepbar
.z-stepbar {
	padding: @stepbarPadding;
}

.z-step {
	&:before {
		margin: 0 8px 0 -8px;
	}

	&-icon-empty {
		border-width: 3px;
	}

	.z-icon-check {
		font-size: 16px;
	}

	&-error &-icon {
		font-size: 16px;
	}
}

//pdfviewer
.z-pdfviewer {
	&-toolbar {
		font-size: @narrowFontSize;

		&-separator {
			height: 18px;
		}
		&-page-active {
			width: 37px;
		}
	}
}

//Searchbox
.z-searchbox {
	padding-right: @narrowIconSize;
	&-icon {
		right: 0;
		font-size: @narrowIconSize;
	}
	&-popup {
		padding: 3px;
	}
	&-search {
		padding: 0 5px;
		margin-bottom: 3px;
	}
	&-item {
		height: 24px;
		padding: @narrowContentPadding;
	}
	&-item-check {
		margin-right: 5px;
	}
}