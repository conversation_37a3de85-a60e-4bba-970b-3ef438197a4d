// Ruby
@textColorDefault:             rgba(255,255,255,0.9);
@textColorLight:               rgba(255,255,255,0.68);
@textColorLighter:             rgba(255,255,255,0.24);
@textColorActive:              #FB6161;
@colorPrimary:                 #FB6161;
@colorPrimaryDark:             #A90404; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #FB8080; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #512D2E; // List hover Bg
@colorAccent:                  #3DC6E9;
@colorAccent3:                 #E8F1F6; // Tooltip Bg
@colorBackground1:             #454545; // Window Bg, Panel Bg
@colorBackground3:             #2F2F2F; // Container Bg
@colorGreyDark:                #767676;
@colorGreyLight:               #4E4E4E; // Btn disabled Bg
@colorGreyLighter:             #404040; // Field disabled Bg
@inputBackgroundColor:         #262626;
@maskBackgroundColor:          #151515;
@popupBackgroundColor:         #454545;
@tooltipColor:                 #000000;
@loadingAnimationDefer:        '~./zul/img/misc/progress-dark-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-dark-72.gif';
@sliderTicks:                  '~./zul/img/slider/scale-ticks-dark.png';