// <PERSON>berry and <PERSON><PERSON><PERSON>
@textColorActive:              #FF5F59;
@colorPrimary:                 #FF5F59;
@colorPrimaryDark:             #FE1C14; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #FF7F7A; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #DEF4FD; // List hover Bg
@colorAccent:                  #1CA5B8;
@colorAccent3:                 #005075; // Tooltip Bg
@colorBackground1:             #F4F4F4; // Window Bg, Panel Bg

@meshTitleColor:               #FFFFFF;
@meshTitleBackgroundColor:     #1CA5B8;
@meshTitleHoverBackgroundColor:#30CADF;
@meshTitleActiveBackgroundColor:#168393;
@meshTitleBorderColor:         #168393;

@tabboxTabsBackgroundColor:    #1CA5B8;
@tabboxTabColor:               #FFFFFF;
@tabboxTabHoverColor:          rgba(255,255,255,0.9);
@tabboxTabButtonColor:         #FFFFFF;
@tabboxTabButtonHoverColor:    #FFFFFF;
@tabboxScrollIconColor:        #FFFFFF;
@tabboxScrollIconHoverColor:   rgba(255,255,255,0.9);
@tabboxTabBackgroundColor:     #1CA5B8;
@tabboxSelectedBackgroundColor:#1CA5B8;
@tabboxTabHoverBackgroundColor:#30CADF;
@tabboxSelectedHoverBackgroundColor:#30CADF;
@tabboxSelectedColor:          #FFFFFF;

@borderlayoutHeaderColor:      #1CA5B8;
@menuItemColor:                #1CA5B8;
@navColor:                     #1CA5B8;
@navHoverColor:                #168393;
