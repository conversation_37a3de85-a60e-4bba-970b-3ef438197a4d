<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0 http://maven.apache.org/xsd/assembly-1.1.0.xsd">
	<id>bin</id>
	<formats>
		<format>zip</format>
	</formats>
	<includeBaseDirectory>false</includeBaseDirectory>
	<files>
		<file>
			<source>readme.txt</source>
			<outputDirectory>/</outputDirectory>
		</file>
		<file>
			<source>${project.build.directory}/${artifactId}-${version}.jar</source>
			<destName>${artifactId}.jar</destName>
			<outputDirectory>/</outputDirectory>
		</file>
	</files>
</assembly>
