<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>___GROUP_ID___</groupId>
	<artifactId>___ARTIFACT_ID___</artifactId>
	<version>___VERSION___</version>
	<properties>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
		<zk.version>9.0.0-RC2-Eval</zk.version>
		<zkspringboot.version>1.0.4</zkspringboot.version>
		<springboot.version>2.1.8.RELEASE</springboot.version>
		<maven.build.timestamp.format>yyyy-MM-dd</maven.build.timestamp.format>
		<packname>-${project.version}</packname>
		<zktheme.resources>${project.basedir}/src/archive</zktheme.resources>
		<zktheme.web.resources>${zktheme.resources}/web</zktheme.web.resources>
		<zktheme.theme.outputDirectory>${project.build.outputDirectory}/web/${project.artifactId}</zktheme.theme.outputDirectory>
	</properties>
	<packaging>jar</packaging>
	<name>___ARTIFACT_ID___</name>
	<scm>
		<url>http://mavensync.zkoss.org/maven2</url>
	</scm>
	<description>___DISPLAY_NAME___</description>
	<licenses>
		<license>
			<name>GNU LESSER GENERAL PUBLIC LICENSE, Version 3</name>
			<url>http://www.gnu.org/licenses/gpl.html</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<developers>
		<developer>
			<id>zkteam</id>
			<name>ZK Team</name>
			<email><EMAIL></email>
			<url>http://www.zkoss.org</url>
			<organization>Potix</organization>
			<organizationUrl>http://www.zkoss.org</organizationUrl>
			<roles>
				<role>architect</role>
				<role>developer</role>
			</roles>
			<timezone>8</timezone>
			<properties>
				<picUrl>http://www.zkoss.org</picUrl>
			</properties>
		</developer>
	</developers>
	<repositories>
		<repository>
			<id>ZK CE</id>
			<name>ZK CE Repository</name>
			<url>http://mavensync.zkoss.org/maven2</url>
		</repository>
		<repository>
			<id>ZK PE EE Evaluation</id>
			<url>http://mavensync.zkoss.org/eval/</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>zkmaven</id>
			<name>ZK Maven Plugin Repository</name>
			<url>http://mavensync.zkoss.org/maven2/</url>
		</pluginRepository>
	</pluginRepositories>
	<dependencies>
		<dependency>
			<groupId>org.zkoss.zk</groupId>
			<artifactId>zkmax</artifactId>
			<version>${zk.version}</version>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-jdk14</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- test dependencies for preview app -->
		<dependency>
			<groupId>org.zkoss.zkspringboot</groupId>
			<artifactId>zkspringboot-autoconfig</artifactId>
			<version>${zkspringboot.version}</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<version>${springboot.version}</version>
			<type>pom</type>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
			<scope>test</scope>
		</dependency>
	</dependencies>
	<build>
		<sourceDirectory>${project.basedir}/src/</sourceDirectory>
		<resources>
			<!-- handle class web resources separately below -->
			<resource>
				<directory>${zktheme.resources}</directory>
				<excludes>
					<exclude>web/**</exclude>
				</excludes>
			</resource>
			<!-- copy non-less into theme folder,
					less files will be handled by plugin below -->
			<resource>
				<directory>${zktheme.web.resources}</directory>
				<excludes>
					<exclude>**/*.less</exclude>
				</excludes>
				<targetPath>${zktheme.theme.outputDirectory}</targetPath>
			</resource>
		</resources>
		<testSourceDirectory>${project.basedir}/preview/</testSourceDirectory>
		<testResources>
			<testResource>
				<directory>${project.basedir}/preview/</directory>
			</testResource>
		</testResources>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.6.0</version>
				<executions>
					<execution>
						<id>compile-less</id>
						<phase>process-resources</phase>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>npx</executable>
							<arguments>
								<argument>zklessc</argument>
								<argument>--source</argument>
								<argument>${zktheme.web.resources}</argument>
								<argument>--output</argument>
								<argument>${zktheme.theme.outputDirectory}</argument>
								<argument>--compress</argument>
							</arguments>
						</configuration>
					</execution>
					<execution>
						<id>preview-app</id>
						<goals>
							<goal>java</goal>
						</goals>
						<configuration>
							<mainClass>zk.example.ThemePreviewApp</mainClass>
							<classpathScope>test</classpathScope>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Build jar -->
			<plugin>
				<groupId>org.apache.felix</groupId>
				<artifactId>maven-bundle-plugin</artifactId>
				<version>2.3.7</version>
				<extensions>true</extensions>
				<configuration>
					<excludeDependencies>*;scope=provided|compile|runtime</excludeDependencies>
					<instructions>
						<_include>${project.basedir}/src/archive/META-INF/MANIFEST.MF</_include>
						<Bundle-Version>${project.version}</Bundle-Version>
						<Bundle-Name>${project.groupId}.${project.artifactId}</Bundle-Name>
						<Bundle-SymbolicName>${project.artifactId}</Bundle-SymbolicName>
						<Export-Package>*</Export-Package>
						<Import-Package>*</Import-Package>
					</instructions>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.2</version>
				<executions>
					<execution>
						<id>bin</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<appendAssemblyId>false</appendAssemblyId>
							<descriptors>
								<descriptor>assembly/zip.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
					<execution>
						<id>bundle</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
						<configuration>
							<descriptors>
								<descriptor>assembly/bundle.xml</descriptor>
							</descriptors>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>
