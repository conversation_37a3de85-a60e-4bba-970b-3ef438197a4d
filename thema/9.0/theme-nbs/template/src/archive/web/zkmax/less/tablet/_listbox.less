.z-listheader-content,
.z-listcell-content,
.z-listgroup-content,
.z-listgroupfoot-content,
.z-listfooter-content {
	font-size: 15px;
	padding: 10px 12px;
}

.z-listbox-body .z-listbox-emptybody td {
	font-size: 15px;
	padding: 10px 12px;
}

.z-listhead-menupopup .z-listheader-content {
	padding-right: 38px;
}

.z-listheader-button {
	.size(38px, 38px);
	line-height: 38px;
	font-size: 22px;
	display: block;
}

// Grouping
.z-listgroup-inner .z-listcell-content,
.z-listgroup-inner .z-listgroup-content {
	padding: 8px 12px;
}
.z-listgroup-icon {
	.size(22px, 22px);
	font-size: 22px;
	line-height: 22px;
}
.z-listgroupfoot .z-listcell-content {
	padding: 8px 12px;
}

// checkbox
.z-listheader-checkable,
.z-listitem-checkable,
.z-listgroup-checkable {
	.size(24px, 24px);
	font-size: 22px;
}

// Sandbox-G14: use strict selector to override style
.z-listitem-selected > .z-listcell > .z-listcell-content
	> .z-listitem-checkable .z-listitem-icon.z-icon-radio {
	.size(16px, 16px);
}
