@import "~./zul/less/_header.less";
@import "~./zkmax/less/_zkvariables.less";

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input,
input:focus,
textarea,
textarea:focus {
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
	-webkit-appearance: none;
	-moz-appearance: none;
	 -webkit-user-modify: read-write-plaintext-only; //Android 4.0.3 bug
	 outline: none;
	 -webkit-user-select: text;
}

// Checkbox and Radio
input[type=checkbox],
input[type=radio] {
	.size(24px, 24px);
}
input[type=checkbox] {
	font-size: 22px;
	line-height: 22px;
}
input[type=radio] {
	&:before {
		.size(16px, 16px);
	}
}

.z-label,
.z-loading,
.z-apply-loading-indicator,
.z-a,
.z-temp .z-loading,
.z-temp .z-loading-indicator {
	font-size: 15px;
}
.z-apply-loading-indicator {
	min-width: 148px;
}
.z-radio,
.z-checkbox {
	display: inline-block;
	line-height: 28px;
	
	&-content {
		font-size: 15px;
		cursor: pointer;
	}
}

.z-html {
	display: inline-block;
}

// JS debug error box
.z-error {
	width: auto;
	max-width: 90%;
	padding: 12px 8px 16px 16px;
	left: 0;
	right: 0;
	margin: 0 auto;

	.errornumbers {
		font-size: 17px;
	}

	.messagecontent {
		padding-top: 16px;
		padding-right: 8px;
		font-size: 15px;
	}

	.button {
		.size(22px, 22px);
		font-size: 22px;
		margin-left: 12px;
		>.z-icon-times { // override
			font-size: 22px;
		}
	}
}

@import "~./zkmax/less/tablet/_biglistbox.less";
@import "~./zkmax/less/tablet/_borderlayout.less";
@import "~./zkmax/less/tablet/_box.less";
@import "~./zkmax/less/tablet/_button.less";
@import "~./zkmax/less/tablet/_calendar.less";
@import "~./zkmax/less/tablet/_chosenbox.less";
@import "~./zkmax/less/tablet/_colorbox.less";
@import "~./zkmax/less/tablet/_combo.less";
@import "~./zkmax/less/tablet/_combobutton.less";
@import "~./zkmax/less/tablet/_grid.less";
@import "~./zkmax/less/tablet/_groupbox.less";
@import "~./zkmax/less/tablet/_listbox.less";
@import "~./zkmax/less/tablet/_input.less";
@import "~./zkmax/less/tablet/_menu.less";
@import "~./zkmax/less/tablet/_paging.less";
@import "~./zkmax/less/tablet/_panel.less";
@import "~./zkmax/less/tablet/_popup.less";
@import "~./zkmax/less/tablet/_slider.less";
@import "~./zkmax/less/tablet/_tabbox.less";
@import "~./zkmax/less/tablet/_toolbar.less";
@import "~./zkmax/less/tablet/_tree.less";
@import "~./zkmax/less/tablet/_window.less";
@import "~./zkmax/less/tablet/_caption.less";
@import "~./zkmax/less/tablet/_nav.less";
