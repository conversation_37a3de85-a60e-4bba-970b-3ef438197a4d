@import "~./zul/less/_header.less";

.z-fisheye,
.z-fisheye-text,
.z-fisheye-image {
	position: absolute;
	cursor: pointer;
}
.z-fisheye {
	z-index: 2;
}
.z-fisheye-image {
	.size(100%, 100%);
	border: 0;
}
.z-fisheye-text {
	.fontStyle(@baseContentFontFamily, @fontSizeXSmall, normal, @tooltipColor);
	display: none;
	.borderRadius(@baseBorderRadius);
	padding: 4px;
	background: @tooltipBackgroundColor;
	text-align: center;
	.boxShadow('0 2px 4px 0 rgba(0,0,0,0.16)');
}
.z-fisheyebar-inner {
	position: relative;
}
