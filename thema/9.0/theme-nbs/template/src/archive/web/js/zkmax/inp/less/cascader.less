@import "~./zul/less/_header.less";

.z-cascader {
	display: inline-block;
	position: relative;
	height: @cascaderHeight;
	width: 180px;
	padding: @cascaderPadding;
	padding-right: 24px;
	border: 1px solid @cascaderBorderColor;
	border-radius: @cascaderBorderRadius;
	color: @cascaderColor;
	background: @cascaderBackgroundColor;
	cursor: pointer;

	&:active {
		border-color: @cascaderActiveBorderColor;
		background: @cascaderActiveBackgroundColor;
	}

	&:hover {
		border-color: @cascaderHoverBorderColor;
	}

	&:focus, &-open:hover {
		border-color: @cascaderFocusBorderColor;
	}

	&-disabled {
		color: @cascaderDisableColor !important;
		background-color: @cascaderDisableBackgroundColor !important;
		cursor: default !important;
		&:focus, &:hover {
			border-color: @cascaderBorderColor;
		}
	}

	&-open {
		border: 1px solid @cascaderFocusBorderColor;
	}

	&:before {
		content: '';
		display: inline-block;
		vertical-align: middle;
		height: 100%;
	}

	&-label {
		user-select: none;
		display: inline-block;
		max-width: 100%;
		vertical-align: middle;
		font-family: @baseContentFontFamily;
		font-size: @cascaderFontSize;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	&-placeholder {
		user-select: none;
		display: none;
		max-width: 100%;
		vertical-align: middle;
		font-family: @baseContentFontFamily;
		font-size: @cascaderFontSize;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		color: @cascaderPlaceholderColor;
	}

	&-icon {
		color: @cascaderIconColor;
		font-size: 22px;
		position: absolute;
		right: 8px;
		top: 50%;
		-ms-transform: translateY(-50%);
		transform: translateY(-50%);
	}

	&-popup {
		min-height: @cascaderPopupItemHeight;
		position: absolute;
		z-index: 1000;
		border: 1px solid @cascaderPopupBorderColor;
		border-radius: @cascaderBorderRadius;
		background: @cascaderBackgroundColor;
		font-family: @baseContentFontFamily;
		font-size: @cascaderFontSize;
		color: @cascaderColor;
		white-space: nowrap;
	}

	&-shadow {
		box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16), 0 2px 4px 0 rgba(0, 0, 0, 0.24);
	}

	& &-popup {
		display: none;
	}

	&-cave {
		margin: 0;
		padding: @cascaderPopupCavePadding;
		border-left: 1px solid @cascaderSeparatorColor;
		max-height: 350px;
		overflow: auto;
		display: inline-block;
		vertical-align: top;
	}

	&-cave:first-child {
		border-left: 0;
	}

	&-item {
		white-space: nowrap;
		list-style: none;
		display: block;
		padding: @cascaderPopupItemPadding;
		border-radius: @cascaderBorderRadius;
		cursor: pointer;
		-moz-user-select: none;
		-webkit-user-select: none;
		-ms-user-select: none;
		user-select: none;
		min-width: 148px;
		height: @cascaderPopupItemHeight;
		line-height: @cascaderPopupItemHeight;
		position: relative;

		> label {
			display: block;
		}

		.z-cascader-icon {
			top: 0;
			line-height: @cascaderPopupItemHeight;
			transform: none;
			margin-left: 4px;
		}
	}

	&-item&-selected {
		color: @cascaderPopupItemSelectedColor;
	}

	&-item&-active, &-item:hover {
		background-color: @cascaderPopupItemHoverBackgroundColor;
	}

	&-item &-icon {
		right: 5px;
	}
}