@import "~./zul/less/_header.less";

.z-timepicker {
	display: inline-block;
	min-height: @baseBarHeight;
	white-space: nowrap;

	&-input {
		.fontStyle(@baseContentFontFamily, @inputTextSize, normal, @inputColor);
		width: 100%;
		height: @baseBarHeight;
		border: 1px solid @inputBorderColor;
		.borderRadius(@baseBorderRadius);
		margin: 0;
		padding: @inputPadding;
		padding-right: @inputPaddingRight + @timepickerButtonWidth;
		line-height: @baseLineHeight;
		background: @inputBackgroundColor;

		// Placeholder ZK-3876: combobox does not provide proper placeholder style
		&::-webkit-input-placeholder {
			color: @inputPlaceholderColor;
		}
		&:-moz-placeholder {
			/* FF 4-18 */
			color: @inputPlaceholderColor;
			opacity: 1;
		}
		&::-moz-placeholder {
			/* FF 19+ */
			color: @inputPlaceholderColor;
			opacity: 1;
		}
		&:-ms-input-placeholder {
			/* IE 10+ */
			color: @inputPlaceholderColor;
		}
	}

	&-input&-hover, &-input&-hover + &-button {
		border-color: @inputHoverBorderColor;
	}

	&-input:focus {
		border: 1px solid @inputFocusBorderColor;
	}
	&-input:focus + &-button {
		border-left: 1px solid @inputFocusBorderColor;
	}

	&-input-full {
		padding-right: @inputPaddingRight;
	}

	&-button {
		.iconFontStyle(@comboButtonIconSize, @comboButtonIconColor);
		display: inline-block;
		position: relative;
		top: 0;
		right: @timepickerButtonWidth;
		min-width: @timepickerButtonWidth;
		height: @baseBarHeight;
		border: 1px solid @inputBorderColor;
		.rightBorderRadius(@baseBorderRadius);
		padding: 6px 8px 0;
		line-height: @baseLineHeight;
		background: @inputBackgroundColor;
		text-align: left;
		vertical-align: middle;
		overflow: hidden;
		cursor: pointer;

		&:hover {
			border-color: @comboButtonHoverBorderColor;
			background: @comboButtonHoverBackgroundColor;
		}
		&:active {
			color: @comboButtonActiveIconColor;
			border-color: @comboButtonActiveBorderColor;
			background-color: @comboButtonActiveBackgroundColor;
		}
	}

	&-disabled &-input,
	&-disabled &-button {
		color: @inputDisableColor !important;
		background: @inputDisableBackgroundColor !important;
		cursor: default !important;
	}
	&-disabled &-button:hover {
		border-color: @inputBorderColor;
	}

	&-readonly {
		& > input {
			color: @inputReadonlyColor;
			background: @inputReadonlyBackgroundColor;
		}
		& > a {
			color: @inputReadonlyColor;
			background-color: @inputBackgroundColor;
		}
	}

	&-invalid {
		border: 1px solid @invalidBorderColor !important;
		margin-right: -1px;
	}
}

.z-timepicker-input {
	&[readonly] {
		cursor: pointer;
	}
}

.z-timepicker-button.z-timepicker-disabled {
	display: none;
}

.z-timepicker-button {
	text-align: center;
}

.z-timepicker-inplace .z-timepicker-input {
	border: 0;
	padding: 3px;
	background: none;
}
.z-timepicker-inplace .z-timepicker-button {
	visibility: hidden; // Bug ZK-2216: Performance issue of Listbox and Combobox with inplace="true"
}

.z-timepicker-option {
	font-size: @comboPopupItemSize;
	white-space: nowrap;
	cursor: pointer;
	display: block;
	padding: 4px 8px;
	position: relative;
	min-height: 20px;
	font-weight: normal;
	color: @comboPopupItemColor;
	.borderRadius(@baseBorderRadius);
}

.z-timepicker-option:hover {
	background-color: @comboPopupItemHoverBackgroundColor;
}

.z-timepicker-option-selected {
	color: @comboPopupItemSelectedColor;
	line-height: @comboPopupItemSize + 2;
}

.z-timepicker-popup {
	max-height: 200px;
	font-family: @baseContentFontFamily;
	font-weight: normal;
	font-size: @fontSizeMedium;
	display: block;
	border: 1px solid @comboPopupBorderColor;
	.borderRadius(@baseBorderRadius);
	padding: 4px 8px;
	background: @popupBackgroundColor;
	position: absolute;
	overflow: auto;
}

.z-timepicker-content {
	border: 0;
	padding: 0;
	margin: 0;
	background: transparent none repeat 0 0;
	position: relative;
	list-style: none outside none;
}

//Shadow
.z-timepicker-shadow {
	.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
}

