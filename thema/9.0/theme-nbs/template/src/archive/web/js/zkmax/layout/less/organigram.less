@import "~./zul/less/_header.less";

.z-organigram {
	overflow: auto;
}

.z-orgchildren {
	display: flex;

	&:not(:only-child) > .z-orgitem:after {
		content: '';
		border-left: @organigramLine;
		height: 10px;
		position: absolute;
		left: 50%;
		top: 0px;
	}
}

.z-orgitem {
	flex: auto;
	display: flex;
	flex-direction: column;
	position: relative;

	&:not(:only-child)::before {
		content: '';
		border-top: @organigramLine;
		width: 100%;
		height: 1px;
		top: 0px;
		position: absolute;
	}

	&:first-child:before {
		width: 50%;
		left: 50%;
	}

	&:last-child:before {
		width: 50%;
	}

	&:not(.z-orgitem-disabled) > .z-orgnode:hover {
		color: @orgnodeColor;
		background-color: @orgnodeHoverBackgroundColor;
	}

	&-selected > .z-orgnode {
		color: @orgnodeSelectedColor;
		background-color: @orgnodeSelectedBackgroundColor;
	}

	&-disabled > .z-orgnode {
		color: @orgnodeDisabledColor;
		background-color: @orgnodeDisabledBackgroundColor;
		cursor: default;
	}

	&-non-selectable > .z-orgnode {
		cursor: default;
	}

	&-close {
		> .z-orgchildren {
			display: none;
		}

		> .z-orgnode:after {
			display: none;
		}
	}
}

.z-orgnode {
	align-self: center;
	position: relative;
	margin: 10px;
	padding: 10px;
	cursor: pointer;
	border: @orgnodeBorder;
	border-radius: @orgnodeBorderRadius;
	background-color: @orgnodeBackgroundColor;
	color: @orgnodeColor;
	font-family: @baseContentFontFamily;

	> i[class="z-orgnode-icon"] {
		display: none;
	}

	&-icon {
		position: absolute;
		width: 12px;
		height: 12px;
		right: 0;
		bottom: 0;
		cursor: pointer;
		color: @orgnodeColor;
		font-size: 12px;
	}

	&:not(:only-child)::after {
		content: '';
		border-left: @organigramLine;
		height: 10px;
		position: absolute;
		bottom: -11px;
		left: 50%;
	}
}