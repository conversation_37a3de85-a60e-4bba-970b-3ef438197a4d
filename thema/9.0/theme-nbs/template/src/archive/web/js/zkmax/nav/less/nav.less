@import "~./zul/less/_header.less";

.restUl() {
	border: 0;
	padding: 0;
	margin: 0;
	list-style: none;
}
.itemSelected(@color, @bgcolor) {
	.z-navitem-selected .z-navitem-content {
		color: @color;
		background: @bgcolor;
	}
}

// Navbar
.z-navbar {
	display: block;
	position: relative;
	white-space: nowrap;
	border-top: 1px solid @navBorderColor;
	border-bottom: 1px solid @navBorderColor;
	padding: 4px;
	ul {
		background: @navBackgroundColor;
		.restUl();
	}
	// overall style
	> ul { //first level
		ul { //second level
			display: none;
			width: auto;

			.z-nav-image,
			.z-navitem-image,
			i {
				margin-right: 6px;
			}
			.z-nav-text,
			.z-navitem-text {
				font-size: @fontSizeMedium;
				margin-left: 2px;
			}
		}
	}
	.itemSelected(@navSelectedColor, @navSelectedBackgroundColor);
	// horizontal style
	&-horizontal {
		padding: 4px 2px;
		li {
			display: inline-block;
			margin: 2px 2px 0;
			border-bottom: 2px solid transparent;
			min-width: 182px;
			vertical-align: middle;
		}
		> ul { //topmost level
			ul { //second level
				position: absolute;
				left: 0;
				margin-top: 5px;
				.borderRadius(4px);
				.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
				z-index: @basePopupZIndex;
				ul { //third level
				}
			}
		}
		.z-navseparator {
			min-width: 1px;
			width: 1px;
			line-height: @baseTitleHeight;
			vertical-align: top; //for collapsed navbar
			position: relative;
			border-left: 1px solid @navBorderColor;
			margin: 0 6px;
		}
		.z-nav-open, .z-navitem-selected {
			border-bottom: 2px solid @colorPrimary;
		}
	}
	// vertical style
	&-vertical {
		> ul { //topmost level
			> li:first-child a {
				border-top-width: 1px;
			}
		}
		.z-nav .z-nav ul {
			padding-left: 20px;
		}
	}
}

// Nav and Navitem
.z-nav,
.z-navitem {
	&-content {
		color: @navColor;
		display: block;
		padding: 6px 12px;
		position: relative;
		overflow: hidden;
		text-decoration: none;
		&:hover {
			color: @navHoverColor;
			background: @navHoverBackgroundColor;
		}
	}
	&-image {
		max-height: @navImageSize;
		width: @navImageSize;
		margin-bottom: 4px;
	}
	&-image,
	i {
		display: inline-block;
		text-align: center;
		margin-right: 4px;
	}
	&-text {
		.fontStyle(@baseContentFontFamily, @fontSizeLarge, normal);
		display: inline-block;
		white-space: nowrap;
	}
}

.z-nav-info {
	.fontStyle(@baseContentFontFamily, @fontSizeXSmall, 700, @navBadgeTextColor);
	.borderRadius(10px);
	padding: 2px 8px;
	line-height: normal;
	margin-left: 4px;
	margin-right: -4px;
	background: @navBadgeBackgroundColor;
	text-align: center;
	position: absolute;
	right: 8px;
	top: 12px;
}

.z-navitem-content[disabled],
.z-navitem-content[disabled]:hover {
	cursor: default;
	
	i, img, .z-navitem-text {
		color: @disabledColor;
		background: @navBackgroundColor;
	}
}

// Collapsed
.z-navbar-collapsed {
	> ul { //topmost level
		> .z-nav > .z-nav-content ,
		> .z-navitem > .z-navitem-content {
			border-width: 0;
			
			> i {
				margin-right: 0;
				text-align: center;
			}
			&:hover {
				color: @navHoverColor;
				background: @navHoverBackgroundColor;
			}
		}
		> .z-navseparator + .z-nav > .z-nav-content,
		> .z-navseparator + .z-navitem > .z-navitem-content {
			border-width: 0;
		}
		> .z-nav > .z-nav-content > .z-nav-text ,
		> .z-navitem > .z-navitem-content > .z-navitem-text {
			display: none;
		}
		> .z-navitem-selected:hover > .z-navitem-content {
			color: @navSelectedColor;
			background: @navSelectedBackgroundColor;
		}
	}
	.z-nav-info {
		position: relative;
		top: -2px;
		left: 0;
	}
}
.z-navbar-horizontal.z-navbar-collapsed {
	> ul { //topmost level
		> .z-nav,
		> .z-navitem {
			min-width: @navCollapsedWidth;
		}
	}
}
.z-navbar-vertical.z-navbar-collapsed {
	> ul { //topmost level
		min-width: @navCollapsedWidth;
		display: inline-block;
	}
}

.z-nav-text-popup,
.z-navitem-text-popup {
	color: @navPopupColor;
	min-width: 180px;
	height: 40px;
	padding: 4px 18px;
	line-height: @baseBarHeight;
	background: @navPopupBackgroundColor;
	cursor: pointer;
	white-space: nowrap;
	z-index: @basePopupZIndex;
	.topBorderRadius(@baseBorderRadius);
	.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');

	&[disabled] {
		cursor: default;

		&:hover {
			cursor: default;
		}
	}

	& + .z-nav-popup {
		.bottomBorderRadius(@baseBorderRadius);
		// Prevent top shadow
		.boxShadow('0 -3px 0 0 @{navPopupBackgroundColor}, 0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');
	}
}

.z-navbar-vertical,
.z-nav-popup {
	li {
		display: block;
		margin: 2px 0;
		border-left: 2px solid transparent;
	}
	.z-navseparator {
		margin: 6px 0;
		height: 1px;
		border-bottom: 1px solid @navBorderColor;
		position: relative;
	}
	.z-nav-content,
	.z-navitem-content {
		border-top-width: 0;
	}
	.z-navseparator + .z-nav > .z-nav-content,
	.z-navseparator + .z-navitem > .z-navitem-content {
		border-top-width: 1px;
	}
	.z-nav-open {
		border-left: 2px solid @colorPrimary;
		.z-nav-open {
			border-left: 2px solid transparent;
		}
		.z-navseparator {
			margin-left: 8px;
		}
	}
	.z-nav-selected {
		border-left: 2px solid @colorPrimary;
		.z-nav-selected {
			border-left: 2px solid transparent;
		}
	}
}
.z-navbar-vertical > ul, .z-nav-popup {
	> .z-navitem-selected {
		border-left: 2px solid @colorPrimary;
	}	
}
.z-nav-popup {
	.restUl();
	padding: 0 4px 4px;
	overflow: hidden;
	z-index: @basePopupZIndex;
	background: @navPopupBackgroundColor;
	.borderRadius(@baseBorderRadius);
	.boxShadow('0 3px 6px 0 rgba(0,0,0,0.16), 0 2px 4px 0 rgba(0,0,0,0.24)');

	.z-nav-text,
	.z-navitem-text {
		font-size: @fontSizeMedium;
	}
	.z-nav-content,
	.z-navitem-content {
		color: @navPopupColor;
		&:hover {
			color: @navPopupHoverColor;
			background: @navPopupHoverBackgroundColor;
		}
	}
	
	ul { //third level
		display: none;
		position: relative;
		.restUl();
	}
	.itemSelected(@navPopupSelectedColor, @navPopupSelectedBackgroundColor);
	.z-nav ul {
		padding-left: 20px;
	}
}
