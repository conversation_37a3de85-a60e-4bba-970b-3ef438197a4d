@import "~./zul/less/_header.less";

.z-progressmeter {
	height: @baseIconHeight;
	border: 1px solid @progressmeterBorderColor;
	.borderRadius(@baseBorderRadius);
	background: @baseBackgroundColor 0 0 repeat-x;
	.encodeThemeURL(background-image, '~./zul/img/misc/prgmeter-anim.gif');
	text-align: left;
	overflow: hidden;

	&-image {
		font-size: 0;
		display: inline-block;  // inline-block has filled bug in Firefox
		vertical-align: top;  // prevent filled bug
		height: @baseIconHeight - 2;
		line-height: 0;
		background-color: @progressmeterBackgroundColor;
	}

	&-indeterminate {
		position: relative;
		opacity: 0.9;
		width: 150%;

		>.z-progressmeter-image {
			position: absolute;
			height: 100%;
			animation: decrease 1.5s 0.5s infinite;
		}

		@keyframes decrease {
			from { left: -80%; width: 80%; }
			to { left: 110%; width: 10%; }
		}
	}
}

// Loadingbar
.z-loadingbar {
	width: 100%;
	height: @loadingbarHeight;
	overflow: hidden;
	position: relative;
	pointer-events: none;
	
	&-colorbar {
		background-color: @loadingbarColor;
		height: 100%;
	}
	
	&-image {
		display: inline-block;
		vertical-align: top;
	}

	&-effectbar {
		display: none;
	}

	&-indeterminate {
		opacity: 0.9;
		width: 150%;

		> .z-loadingbar-image {
			position: absolute;
			animation: ani1 3s ease-in infinite;
		}
		> .z-loadingbar-effectbar {
			display: inline-block;
			position: absolute;
			vertical-align: top;
			animation: ani2 3s ease infinite;
		}

		@keyframes ani1 {
			from {
				left: -10%;
				width: 10%;
			}
			13.33% {
				width: 10%;
			}
			60% {
				left: 100%;
				width: 100%;
			}
			to {
				left: 100%;
			}
		}
		@keyframes ani2 {
			from {
				left: -80%;
				width: 80%;
			}
			30% {
				width: 80%;
			}
			40% {
				left: -80%;
			}
			96% {
				left: 100%;
			}
			to {
				left: 100%;
				width: 5%;
			}
		}
	}

	&-position {
		display: -ms-flexbox;
		display: flex;
		position: fixed;
		width: 100%;
		left: 0;
		pointer-events: none;

		> .z-loadingbar:nth-child(even) .z-loadingbar-colorbar {
			background-color: @loadingbarSecondaryColor;
		}

		&-top {
			-ms-flex-direction: column;
			flex-direction: column;
			bottom: unset;
			top: 0;
		}
		&-bottom {
			-ms-flex-direction: column-reverse;
			flex-direction: column-reverse;
			top: unset;
			bottom: 0;
		}
	}
}