@import "~./zul/less/_header.less";

.z-drawer {
	position: fixed;
	top: 0;
	z-index: 1000;
	width: 0;
	height: 0;

	&-open {
		width: 100%;
		height: 100%;
	}

	&-mask {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background-color: @drawerMaskBackgroundColor;
		opacity: 0;
	}

	&-mask-enabled {
		opacity: @drawerMaskOpacity;
	}

	&-real {
		position: fixed;
		background-color: @drawerBackgroundColor;
		display: -ms-flexbox;
		display: flex;
		-ms-flex-direction: column;
		flex-direction: column;
		// animation
		will-change: transform;
		transition: transform 200ms ease-out;
	}

	&-container {
		-ms-flex: 1;
		flex: 1;
		overflow: auto;
	}

	&-header {
		padding: 13px 16px;
		border-bottom: 1px solid @drawerTitleLineColor;
	}

	&-title {
		.fontStyle(@baseContentFontFamily, @fontSizeLarge, normal, @drawerTitleTextColor);
	}

	&-close {
		.iconFontStyle(@fontSizeLarge, @iconColor);
		.size(@fontSizeLarge, @fontSizeLarge);
		position: absolute;
		right: 16px;
		top: 15px;
		text-align: center;
		cursor: pointer;

		&:hover {
			color: @iconHoverColor;
		}
	}

	&-cave {
		padding: 16px;
	}

	// open style
	&-open&-left &-real {
		box-shadow: 4px 0 8px @drawerShadowColor;
		transform: translateX(0%);
	}
	&-open&-right &-real {
		box-shadow: -4px 0 8px @drawerShadowColor;
		transform: translateX(0%);
	}
	&-open&-top &-real {
		box-shadow: 0 4px 8px @drawerShadowColor;
		transform: translateY(0%);
	}
	&-open&-bottom &-real {
		box-shadow: 0 -4px 8px @drawerShadowColor;
		transform: translateY(0%);
	}

	// position
	&-left &-real {
		left: 0;
		width: 280px;
		height: 100%;
		transform: translateX(-100%);
	}
	&-right &-real {
		right: 0;
		width: 280px;
		height: 100%;
		transform: translateX(100%);
	}
	&-top &-real {
		top: 0;
		bottom: auto;
		left: 0;
		width: 100%;
		height: 280px;
		transform: translateY(-100%);
	}
	&-bottom &-real {
		top: auto;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 280px;
		transform: translateY(100%);
	}
}