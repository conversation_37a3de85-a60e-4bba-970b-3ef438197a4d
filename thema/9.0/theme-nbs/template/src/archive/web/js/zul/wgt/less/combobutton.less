@import "~./zul/less/_header.less";

.z-combobutton {
	display: inline-block;
	min-height: @baseButtonHeight;
	cursor: pointer;
	.borderRadius(@inputBorderRadius);
	overflow: hidden;

	&-content {
		.fontStyle(@baseContentFontFamily, @inputTextSize, normal, @buttonColor);
		display: block;
		min-height: @baseButtonHeight;
		border: 2px solid @buttonBorderColor;
		.borderRadius(@inputBorderRadius);
		padding: @buttonPadding;
		padding-right: 46px;
		line-height: normal;
		background-color: @buttonBackgroundColor;
		vertical-align: middle;
		position: relative;
		white-space: nowrap;

		> i {
			vertical-align: text-bottom;
		}
	}
	&-button {
		font-weight: normal;
		.displaySize(block, 38px, 100%);
		border-left: 1px solid @buttonSeparatorBorderColor;
		line-height: normal;
		position: absolute;
		top: 0;
		right: 0;
	}
	&-icon {
		font-size: @fontSizeLarge;

		&.z-icon-caret-down {
			display: block;
			margin-top: -8px;
			position: absolute;
			top: 50%;
			left: 10px;
		}
	}
	&-image {
		vertical-align: text-bottom;
	}
	&:hover {
		.z-combobutton-content {
			color: @buttonHoverColor;
			border-color: @buttonHoverBorderColor;
			background-color: @buttonHoverBackgroundColor;
		}
	}
	&:focus {
		.z-combobutton-content {
			color: @buttonFocusColor;
			border-color: @buttonFocusBorderColor;
			background-color: @buttonFocusBackgroundColor;
		}
	}
	&:active {
		.z-combobutton-content {
			color: @buttonActiveColor;
			border-color: @buttonActiveBorderColor;
			background-color: @buttonActiveBackgroundColor;
		}
	}
	&[disabled] {
		cursor: default;
		.z-combobutton-content {
			color: @buttonDisableColor;
			border-color: @buttonDisableBorderColor;
			background-color: @buttonDisableBackgroundColor;
			cursor: default;
		}
		.z-combobutton-button {
			border-color: @buttonDisableSeparatorBorderColor;
		}
	}

	//toolbar mold
	&-toolbar {
		.z-combobutton-content {
			font-size: @fontSizeLarge;
			line-height: @fontSizeLarge;
			padding: 6px 46px 6px 12px;
			color: @toolbarButtonColor;
			background-color: @toolbarButtonBackgroundColor;
			.z-combobutton-button {
				border-color: transparent;
			}
		}
		&:hover, &:focus, &:active {
			.z-combobutton-button {
				border-color: @buttonSeparatorBorderColor;
			}
		}
		&[disabled] {
			.z-combobutton-button {
				border-color: @buttonDisableSeparatorBorderColor;
			}
		}
		&.z-combobutton-open {
			.z-combobutton-content {
				color: @buttonColor;
				background-color: @buttonBackgroundColor;
			}
		}
	}
}

.ie, .edge {
	// Icon height workaround
	.z-combobutton-content > i {
		vertical-align: text-bottom;
	}
}
