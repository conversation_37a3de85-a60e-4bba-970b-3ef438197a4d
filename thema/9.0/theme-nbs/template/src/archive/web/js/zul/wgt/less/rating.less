@import "~./zul/less/_header.less";

.z-rating {
    display: inline-block;
    margin: 0;
    padding: 0;
    font-size: @fontSizeMedium;
    line-height: @fontSizeMedium;

    > .z-rating-icon {
        text-align: center;
        color: @ratingIcon;
        padding: 4px;
        width: 26px;
        height: 26px;
        text-decoration: none;
    }

    &-vertical > a {
        display: block;
    }

    > .z-rating-selected {
        color: @ratingIconSelected;
    }

    > .z-rating-hover {
        color: @ratingIconHover;
        text-shadow: 0px 0px 1px @ratingIconHoverTextShadow;
    }

    > .z-rating-disabled {
        opacity: 0.5;
        cursor: default;
    }

    > .z-rating-readonly {
        cursor: default;
    }

}