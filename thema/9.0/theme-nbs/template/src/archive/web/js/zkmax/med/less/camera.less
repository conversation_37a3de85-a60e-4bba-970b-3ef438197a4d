@import "~./zul/less/_header.less";

.z-camera {
	display: none;
	position: relative;

	&-real {
		width: 100%;
		height: 100%;
	}

	&-preview {
		display: inline-block;
	}

	&-recording,
	&-stop,
	&-pause {
		.baseIconFont();
		font-size: 36px;
		position: absolute;
		top: 12px;
		right: 12px;
	}

	&-recording:before {
		content: '\f03d';
		color: #D0021B;
	}

	&-stop:before {
		content: '\f04d';
		color: #FFFFFF;
	}

	&-pause:before {
		content: '\f04c';
		color: #FFFFFF;
	}
}