// Global Variables
// -------------------------------------
// Typography
// -------------------------------------
@baseFontSize:                 16px;
@baseTitleFontFamily:          "Helvetica Neue", Helvetica, Arial, sans-serif;
@baseContentFontFamily:        @baseTitleFontFamily;
@baseLineHeight:               16px;

// Component height
@baseHeight:                   8px;
@baseIconHeight:               @baseHeight * 2; // 16px
@baseButtonHeight:             @baseHeight * 3; // 24px
@baseBarHeight:                @baseHeight * 4; // 32px
@baseTitleHeight:              @baseHeight * 5; // 40px
@baseHeightImput:              34px;
// Component width
@baseWidth:                    8px;
@baseIconWidth:                @baseWidth * 2; // 16px
@baseButtonWidth:              @baseWidth * 3; // 24px
@baseBarWidth:                 @baseWidth * 4; // 32px

// -------------------------------------
// Component sizing
// -------------------------------------
// Based on 14px font-size and 20px line-height
@fontSizeXLarge:               ceil(@baseFontSize * 1.25);   // 20px
@fontSizeLarge:                floor(@baseFontSize * 1.125); // 18px
@fontSizeMedium:               ceil(@baseFontSize);          // 16px
@fontSizeSmall:                floor(@baseFontSize * 0.875); // 14px
@fontSizeXSmall:               ceil(@baseFontSize * 0.75);   // 12px

@baseBorderRadius:             4px;
@borderRadiusLarge:            6px;
@borderRadiusSmall:            3px;



// -------------------------------------
// Component Basic Coloring
// -------------------------------------
// Font color
@textColorDefault:             rgba(0, 0, 0, 0.9);
@textColorLight:               rgba(0, 0, 0, 0.57);
@textColorLighter:             rgba(0, 0, 0, 0.34);
@textColorDefault3:            #FFFFFF;
@textColorActive:              #0093F9;

@colorPrimary:                 #0093F9;
@colorPrimaryDark:             #0064ED; //darken(@colorPrimary, 20%);
@colorPrimaryLight:            #4FB7FF; //lighten(@colorPrimary, 20%);
@colorPrimaryLighter:          #E8F5FF; // List hover Bg
@colorAccent:                  #FFA516;
@colorAccent2:                 #FF4051;
@colorAccent3:                 #261429; // Tooltip Bg
@colorBackground1:             #F9FCFF; // Window Bg, Panel Bg
@colorBackground3:             #FFFFFF; // Container Bg
@colorGreyDark:                #A8A8A8;
@colorGreyLight:               #D9D9D9; // Btn disabled Bg
@colorGreyLighter:             #F2F2F2; // Field disabled Bg

@baseTextColor:                @textColorDefault;

// Border color
@baseBorderColor:              @colorGreyLight;

// Background color
@baseBackgroundColor:          @colorBackground3;

// -------------------------------------
// Icon font color (used for font-awesome)
// -------------------------------------
@iconColor:                    @textColorLight;
@iconHoverColor:               @textColorDefault;
@iconDisabledColor:            @textColorLighter;

// -------------------------------------
// Mesh Table (used for grid, listbox, tree, biglistbox)
// -------------------------------------
@meshAutoPagingRowHeight:      44px;
@meshBackgroundColor:          @colorBackground3;
@meshStripeBackgroundColor:    @meshBackgroundColor;
@meshTitleColor:               @textColorDefault3;
@meshTitleBackgroundColor:     @colorPrimary;
@meshTitleHoverBackgroundColor:@colorPrimaryLight;
@meshTitleActiveBackgroundColor: @colorPrimaryDark;
@meshTitleBorderColor:         @colorPrimaryDark;
@meshContentBorderColor:       @colorGreyLighter;
@meshContentFocusBackgroundColor: @colorPrimaryLighter;
@meshFootBackgroundColor:      @colorGreyLighter;
@meshGroupColor:               @textColorLight;
@meshGroupBorderColor:         @colorGreyLight;
@meshGroupBackgroundColor:     @colorBackground3;
@meshGroupFooterColor:         @textColorLighter;
@meshGroupFooterBackgroundColor: @colorBackground3;
@meshGroupOpenColor:           @colorPrimary;
@meshGroupOpenBackgroundColor: @colorBackground3;
@meshGroupOpenBorder:          2px solid @colorPrimary;
@meshGroupFooterOpenColor:     @colorPrimaryLight;
@gridDetailContentPadding:     0 12px 12px 0;
@gridDetailContentBeforeMargin: 0 0 12px 0;

// -------------------------------------
// Component State
// -------------------------------------
// Active
@activeColor:                  @textColorDefault3;
@activeBorderColor:            transparent;
@activeBackgroundColor:        @colorPrimaryDark;
@activeGradientStart:          @activeBackgroundColor;
@activeGradientEnd:            @activeBackgroundColor;

// Focus
@focusColor:                   @textColorDefault3;
@focusBorderColor:             @colorAccent;
@focusBackgroundColor:         @colorPrimary;
@focusGradientStart:           @focusBackgroundColor;
@focusGradientEnd:             @focusBackgroundColor;

// Hover
@hoverColor:                   @baseTextColor;
@hoverBorderColor:             transparent;
@hoverBackgroundColor:         @colorPrimaryLighter;
@hoverGradientStart:           @hoverBackgroundColor;
@hoverGradientEnd:             @hoverBackgroundColor;

// Disabled
@disabledColor:                @textColorLighter;
@disabledBackgroundColor:      @colorGreyLighter;
@disabledOpacity:              1;

// Invalid
@invalidBorderColor:           @colorAccent2;

// Read-only
@readonlyBorderColor:          @colorGreyLight;
@readonlyBackgroundColor:      @colorGreyLighter;

// Selected (used on listbox, tree, comboitem)
@selectedColor:                @baseTextColor;
@selectedBorderColor:          transparent;
@selectedBackgroundColor:      @colorPrimaryLighter;

// Selected Hover (used on listbox, tree, comboitem)
@selectedHoverColor:           @baseTextColor;
@selectedHoverBorderColor:     transparent;
@selectedHoverBackgroundColor: @colorPrimaryLighter;

// Selected Focus (used on listbox, tree, comboitem)
@selectedFocusColor:           @baseTextColor;
@selectedFocusBorderColor:     transparent;
@selectedFocusBackgroundColor: @colorPrimaryLighter;

// Checked (used on menuitem, listbox, tree, toolbarbutton)
@checkedIconSize:              @fontSizeLarge;
@checkedColor:                 @colorPrimary;
@checkedBorderColor:           @colorGreyLight;
@checkedBackgroundColor:       @textColorDefault3;

// -------------------------------------
// Component independent variables
// -------------------------------------
// Container (Window, Panel)
@containerBackground:          @colorBackground1;
@containerPadding:             16px;
@containerBorderColor:         @colorGreyDark;
@containerBorderRadius:        @baseBorderRadius;
@containerHeaderTextSize:      @fontSizeLarge;
@containerHeaderColor:         @textColorLight;
@containerBodyTextSize:        @fontSizeMedium;
@containerBodyColor:           @textColorDefault;
@containerButtonSize:          @fontSizeLarge;
@containerButtonColors:        @textColorLight, @textColorDefault; // 1: normal, 2: hover

// Borderlayout
@borderlayoutHeaderColor:      @textColorLight;
@borderlayoutHeaderBackgroundColor: @baseBackgroundColor;
@borderlayoutCollapsedIconColors: @textColorLight, @textColorLight; // 1: normal, 2: hover

// Groupbox
@groupboxHeaderColor:          @textColorLight;

// Input (used for textbox, intbox, spinner, ...)
@inputTextSize:                @fontSizeMedium;
@inputBorderColor:             @colorGreyLight;
@inputBorderRadius:            @baseBorderRadius;
@inputBackgroundColor:         @colorBackground3;
@inputPadding:                 0 8px;
@inputPaddingRight:            8px;
@inputColor:                   @textColorDefault;
@inputPlaceholderColor:        @textColorLighter;
@inputHoverBorderColor:        @colorGreyDark;
@inputFocusBorderColor:        @colorPrimary;
@inputDisableColor:            rgba(0, 0, 0, 0.753);
@inputDisableBackgroundColor:  @colorGreyLighter;
@inputReadonlyColor:           @textColorDefault;
@inputReadonlyBackgroundColor: @colorGreyLighter;

// Checkbox/Radio
@checkboxHoverBorderColor:     @colorPrimary;

// Button (used for button, combobutton)
@buttonPadding:                8px 16px;
@buttonColor:                  @textColorDefault3;
@buttonBackgroundColor:        @colorPrimary;
@buttonBorderColor:            transparent;
@buttonHoverColor:             @textColorDefault3;
@buttonHoverBackgroundColor:   @colorPrimaryLight;
@buttonHoverBorderColor:       transparent;
@buttonFocusColor:             @textColorDefault3;
@buttonFocusBackgroundColor:   @colorPrimary;
@buttonFocusBorderColor:       @colorAccent;
@buttonActiveColor:            @textColorDefault3;
@buttonActiveBackgroundColor:  @colorPrimaryDark;
@buttonActiveBorderColor:      transparent;
@buttonDisableColor:           @textColorLighter;
@buttonDisableBackgroundColor: @colorGreyLight;
@buttonDisableBorderColor:     transparent;
@buttonSeparatorBorderColor:   @colorPrimaryDark;
@buttonDisableSeparatorBorderColor:   @colorGreyLight;
// Toolbar button
@toolbarButtonPadding:         6px 12px;
@toolbarButtonColor:           @textColorLight;
@toolbarButtonBackgroundColor: transparent;
@toolbarButtonCheckedColor:           @textColorDefault3;
@toolbarButtonCheckedBackgroundColor: @colorPrimary;

// ComboInput (combobox, datebox, bandbox, timebox, spinner...)
@comboButtonIconSize:          18px;
@comboButtonIconColor:         @textColorDefault;
@comboButtonHoverBorderColor:  @colorPrimaryLight;
@comboButtonHoverBackgroundColor: @colorPrimaryLighter;
@comboButtonActiveIconColor:   @textColorDefault3;
@comboButtonActiveBorderColor: @colorPrimaryDark;
@comboButtonActiveBackgroundColor: @colorPrimary;
@comboPopupBorderColor:        @colorPrimary;
@comboPopupItemSize:           @fontSizeMedium;
@comboPopupItemColor:          @textColorDefault;
@comboPopupIconSize:           @fontSizeLarge;
@comboPopupIconColor:          @textColorDefault;
@comboPopupDescSize:           @fontSizeXSmall;
@comboPopupDescColor:          @textColorLight;
@comboPopupItemHoverBackgroundColor: @colorPrimaryLighter;
@comboPopupItemSelectedColor:  @colorPrimary;
@comboPaddingButton:           6px 8px 0; //NBS - Inclusão

// Timepicker
@timepickerButtonWidth:        36px;

// mask and loading
@maskBackgroundColor:          #E0E1E3;
@loadingBackgroundColor:       @baseBackgroundColor;
@loadingTextColor:             @textColorLight;
@loadingAnimationDefer:        '~./zul/img/misc/progress-32.gif';
@loadingAnimationLoad:         '~./zul/img/misc/progress-72.gif';

// scrollbar
@scrollbarSize:                10px;
@scrollbarEmbeddedSize:        10px;
@scrollbarBarSize:             10px;
@scrollbarRailSize:            6px;
@scrollbarEmbeddedColor:       @colorGreyLight;
@scrollbarBorderColor:         transparent;
@scrollbarBackgroundColor:     @colorGreyLight;
@scrollbarBarBackgroundColor:  @colorPrimary;
@scrollbarBarHoverBackground:  @colorPrimaryDark;
@scrollbarIconDisplay:         none;
@scrollbarButtonBackground:    transparent;
@scrollbarButtonHoverBackground: transparent;
@scrollbarButtonColor:         @colorPrimary;
@scrollbarButtonHoverColor:    @colorPrimaryDark;

// drag and drop
@dragColor:                    @textColorDefault;
@dragBackgroundColor:          @popupBackgroundColor;
@dragHoverBackgroundColor:     @colorPrimaryLighter;
@dragAllowIconColor:           @colorPrimary;
@dragAllowBorderColor:         @colorPrimary;
@dragAllowBackgroundColor:     @popupBackgroundColor;
@dragDisAllowIconColor:        @textColorLighter;
@dragDisAllowBorderColor:      transparent;
@dragDisAllowBackgroundColor:  @colorGreyLighter;

// splitter (hbox, vbox, borderlayout)
@splitterSize:                 8px;
@splitterBorderColor:          @colorGreyLight;
@splitterBackgroundColor:      @colorBackground1;
@splitterHoverBackgroundColor: @colorPrimaryLighter;
@splitterButtonTextSize:       12px;
@splitterButtonTextColors:     @textColorLighter, @textColorLighter; // 1: normal, 2: hover
@splitterDragBackgroundColor:  @colorGreyLight;

// calendar
@calendarBackgroundColor:      @colorBackground3;
@calendarTodayColor:           @textColorLight;
@calendarTitleColor:           @baseTextColor;
@calendarTitleHoverColor:      @baseTextColor;
@calendarCellColor:            @baseTextColor;
@calendarCellHoverBackgroundColor: @colorPrimaryLighter;
@calendarSelectedColor:        @textColorDefault3;
@calendarSelectedHoverColor:   @textColorDefault3;
@calendarSelectedBackgroundColor: @colorPrimary;
@calendarSelectedHoverBackgroundColor: @colorPrimary;
@calendarWeekTitleColor:       @textColorLight;
@weekendColor:                 @baseTextColor;
@weekendBackgroundColor:       transparent;
@weekdayColor:                 @baseTextColor;
@weekdayBackgroundColor:       transparent;
@weekofyearColor:              @textColorLighter;
@weekofyearBackgroundColor:    transparent;

// popup
@basePopupZIndex:              88000;
@popupBorderColor:             @colorGreyLight;
@popupBackgroundColor:         @colorBackground3;

// paging
@pagingColor:                  @textColorDefault;
@pagingHeight:                 44px;
@pagingBorderColor:            @colorGreyLight;
@pagingBackgroundColor:        @colorGreyLighter;
@pagingItemHoverBackgroundColor:   @colorPrimaryLighter;
@pagingItemActiveColor:            @textColorDefault3;
@pagingItemActiveBackgroundColor:  @colorPrimary;
@pagingItemSelectedColor:          @colorPrimary;
@pagingItemSelectedBackgroundColor:transparent;
@pagingButtonMinWidth:         26px;
@pagingButtonHeight:           26px;

// slider
@sliderAreaSize:               6px;
@sliderBackgroundColor:        @colorGreyLight;
@sliderAreaBackgroundColor:    @colorPrimary;
@sliderInputColor:             @textColorActive;
@sliderTicks:                  '~./zul/img/slider/scale-ticks.png';

// tooltip (used in slider, fisheyebar)
@tooltipColor:                 @textColorDefault3;
@tooltipBackgroundColor:       @colorAccent3;

// errorbox (input constraint)
@errorboxColor:                @colorAccent2;
@errorboxBorderColor:          transparent;
@errorboxBackgroundColor:      #FFEAEC;

// notification
@notificationInfoColor:        #78C129;
@notificationWarningColor:     #FF8501;
@notificationErrorColor:       #FF4051;
@notificationInfoBackgroundColor:        #F0F7E8;
@notificationWarningBackgroundColor:     #FFF3E7;
@notificationErrorBackgroundColor:       #FFEAEC;

// progressmeter
@progressmeterBorderColor:     @colorGreyLight;
@progressmeterBackgroundColor: @colorPrimary;

// loadingbar
@loadingbarColor:              @colorPrimary;
@loadingbarSecondaryColor:     @colorPrimaryLight;
@loadingbarHeight:             6px;

// tabbox
@tabboxBackgroundColor:        @colorBackground3;
@tabboxTabsBackgroundColor:    @colorBackground3;
@tabboxToolbarBackgroundColor: @tabboxTabsBackgroundColor;
@tabboxTabColor:               @textColorLight;
@tabboxTabBackgroundColor:     @colorBackground3;
@tabboxTabHoverColor:          @textColorDefault;
@tabboxTabHoverBackgroundColor:@colorBackground3;
@tabboxTabButtonColor:         @iconColor;
@tabboxTabButtonHoverColor:    @iconHoverColor;
@tabboxTabSeparatorColor:      @baseBorderColor;
@tabboxSelectedRadius:         0;
@tabboxSelectedColor:          @colorPrimary;
@tabboxSelectedBorderColor:    @colorPrimary;
@tabboxSelectedHoverColor:     @colorPrimaryDark;
@tabboxSelectedBackgroundColor:@colorBackground3;
@tabboxSelectedHoverBackgroundColor:@colorBackground3;
@tabboxScrollIconColor:        @iconColor;
@tabboxScrollIconHoverColor:   @iconHoverColor;

// menu
@menuBackground:               @baseBackgroundColor;
@menuItemColor:                @textColorLight;
@menuItemBackground:           @baseBackgroundColor;
@menuItemHoverColor:           @textColorLight;
@menuItemHoverBackground:      @hoverBackgroundColor;
@menuItemActiveColor:          @activeColor;
@menuItemActiveBackground:     @colorPrimary;
@menuSeparatorBorderColor:     @colorGreyLight;
@menuSeparatorBackgroundColor: @colorGreyLight;
@menuPopupBackground:          @popupBackgroundColor;
@menuPopupItemColor:           @textColorLight;
@menuPopupItemBackground:      @popupBackgroundColor;
@menuPopupItemHoverColor:      @textColorLight;
@menuPopupItemHoverBackground: @hoverBackgroundColor;
@menuPopupItemActiveColor:     @activeColor;
@menuPopupItemActiveBackground:@colorPrimary;
@menuPopupSeparatorBorder:     @colorGreyLight;
@menuImageSize:                20px;
@menuCheckedColor:             @checkedBackgroundColor;
@menuCheckedBackgroundColor:   @checkedColor;
@menuScrollableIconColors:     @iconColor, @iconHoverColor; // 1: normal, 2: hover

// navbar
@navImageSize:                 20px;
@navColor:                     @textColorLight;
@navHoverColor:                @textColorDefault;
@navBorderColor:               @colorGreyLight;
@navBackgroundColor:           @colorBackground3;
@navHoverBackgroundColor:      @navBackgroundColor;
@navSelectedColor:             @colorPrimary;
@navSelectedBackgroundColor:   @navBackgroundColor;
@navPopupColor:                @textColorLight;
@navPopupHoverColor:           @textColorDefault;
@navPopupBackgroundColor:      @popupBackgroundColor;
@navPopupHoverBackgroundColor: @navBackgroundColor;
@navPopupSelectedColor:        @colorPrimary;
@navPopupSelectedBackgroundColor: @navPopupBackgroundColor;
@navSeparatorColor:            @colorGreyLight;
@navCollapsedWidth:            32px;
@navBadgeTextColor:            @textColorLight;
@navBadgeBackgroundColor:      @colorGreyLight;

// chosenbox
@chosenboxIconSize:            @fontSizeMedium;
@chosenboxItemSize:            @fontSizeMedium;
@chosenboxItemColor:           @textColorDefault3;
@chosenboxItemBorderColor:     @colorPrimaryDark;
@chosenboxItemBackgroundColor: @colorPrimary;
@chosenboxItemFocusBackgroundColor: @colorPrimaryDark;
@chosenboxItemDisabledColor:   @textColorLighter;
@chosenboxItemDisabledBorderColor: @colorGreyLight;
@chosenboxItemDisabledBackgroundColor: @colorGreyLight;
@chosenboxPopupHoverBackgroundColor: @colorPrimaryLighter;
@chosenboxCreateIconSize:      @fontSizeLarge;
@chosenboxCreateIconColor:     @colorPrimary;

// biglistbox
@biglistboxFrozenBackgroundColor:   @colorGreyLight;
@biglistboxFrozenGhostBackgroundColor: @colorPrimary;
@biglistboxScrollBarBorderColor: @colorGreyLight;
@biglistboxScrollBarTextColor: @textColorLight;
@biglistboxScrollBarEndBarBackgroundColor: @colorGreyLighter;
@biglistboxScrollBarHoverBackgroundColor: @colorPrimaryLighter;
@biglistboxScrollBarActiveBackgroundColor: @colorPrimary;
@biglistboxScrollBarActiveBorderColor: @colorGreyLight;
@biglistboxScrollBarHoverBorderColor: @colorGreyDark;


@baseHeightGroupBox: 12px;

@fontSizeTab: 22px;

@buttonSpinnerBarHeight: 32px;
@buttonSpinnerBarWidth: 34px;

// rating
@ratingIcon: #4a4a4a;
@ratingIconHover: @textColorActive;
@ratingIconHoverTextShadow: #3393f9;
@ratingIconSelected: @textColorActive;
@ratingDisabled: @colorGreyLight;
@ratingDisabledSelected: #b4d8ff;

// goldenlayout
@goldenLayoutBackgroundColor: @colorBackground1;
@goldenLayoutBorderColor: #d2d2d2;
@goldenLayoutHeaderColor: @textColorLight;
@goldenLayoutHeaderHoverColor: @textColorDefault;
@goldenLayoutHeaderActiveHoverColor: @colorPrimaryDark;
@goldenLayoutHeaderBackgroundColor: @colorBackground3;
@goldenLayoutSelectedBorderColor: @colorPrimary;
@goldenLayoutSelectedBackgroundColor: @colorBackground3;
@goldenLayoutPanelBackgroundColor: @colorBackground3;
@goldenLayoutProxyBoxShadowColor: rgba(0, 0, 0, 0.19);
@goldenLayoutDroptargetIndicatorBorderColor: @colorPrimary;
@goldenLayoutDroptargetIndicatorBackgroundColor: rgba(0, 147, 249, 0.15);
@goldenLayoutSplitterDragging: @colorGreyLight;

// organigram
@organigramLine: 1px solid #9B9B9B;
@orgnodeColor: rgba(0, 0, 0, 0.9);
@orgnodeBackgroundColor: #FFFFFF;
@orgnodeHoverBackgroundColor: #E8F5FF;
@orgnodeSelectedColor: #FFFFFF;
@orgnodeSelectedBackgroundColor: #0093F9;
@orgnodeDisabledColor: #B0B0B0;
@orgnodeDisabledBackgroundColor: #F2F2F2;
@orgnodeBorder: 1px solid rgba(0, 0, 0, 0.15);
@orgnodeBorderRadius: 4px;

// signature
@signatureBorderColor: @colorGreyLight;
@signatureBorderRadius: @baseBorderRadius;

// drawer
@drawerMaskBackgroundColor: #000;
@drawerMaskOpacity: 0.5;
@drawerTitleTextColor: @textColorLight;
@drawerTitleLineColor: @colorGreyLight;
@drawerBackgroundColor: @colorBackground3;
@drawerShadowColor: rgba(0, 0, 0, 0.3);

// rangeslider
@rangesliderTrackColor: @colorGreyLight;
@rangesliderEmptyBackgroundColor: #FFF;
@rangesliderShadowColor: rgba(0, 0, 0, 0.3);
@rangesliderButtonSize: 20px;
@rangesliderButtonBorderWidth: 2px;
@rangesliderButtonColor: @colorPrimary;
@rangesliderButtonHoverColor: @colorPrimaryLight;
@rangesliderButtonActiveColor: @colorPrimaryDark;
@rangesliderButtonFocusBorderColor: @colorAccent;
@rangesliderDisabledTrackColor: @colorGreyLighter;
@rangesliderDisabledButtonColor: lighten(@rangesliderButtonColor, 30%);
@rangesliderBorderRadius: @baseBorderRadius;
@rangesliderTooltipBackgroundColor: rgba(0, 0, 0, 0.8);
@rangesliderDisabledTooltipBackgroundColor: rgba(0, 0, 0, 0.5);
@rangesliderInnerSize: 8px;
@rangesliderDotSize: @baseIconWidth;
@rangesliderDotBorderWidth: @rangesliderDotSize * 0.25;

// multislider
@multisliderTrackColor: @colorGreyLight;
@multisliderShadowColor: rgba(0, 0, 0, 0.2);
@multisliderButtonSize: 20px;
@multisliderButtonBorderWidth: 2px;
@multisliderButtonColor: @colorPrimary;
@multisliderButtonHoverColor: @colorPrimaryLight;
@multisliderButtonActiveColor: @colorPrimaryDark;
@multisliderButtonFocusBorderColor: @colorAccent;
@multisliderButtonColor2: @colorPrimaryLight;
@multisliderButtonHoverColor2: lighten(@multisliderButtonColor2, 15%);
@multisliderButtonActiveColor2: darken(@multisliderButtonColor2, 20%);
@multisliderBorderRadius: @baseBorderRadius;
@multisliderTooltipBackgroundColor: rgba(0, 0, 0, 0.8);
@multisliderDisabledTooltipBackgroundColor: rgba(0, 0, 0, 0.5);
@multisliderInnerSize: 8px;
@multisliderDisabledTrackColor: @colorGreyLighter;
@multisliderDisabledButtonColor: lighten(@multisliderButtonColor, 30%);
@multisliderDisabledButtonColor2: lighten(@multisliderButtonColor2, 20%);

// inputgroup
@inputgroupTextBackgroundColor: #E9ECEF;

// pdfviewer
@pdfviewerContainerBackgroundColor: gray;
@pdfviewerSelectionBackgroundColor: blue;
@pdfviewerToolbarBackgroundColor: @colorBackground3;
@pdfviewerToolbarBorderColor: @baseBorderColor;
@pdfviewerToolbarBorderRadius: @baseBorderRadius;
@pdfviewerToolbarPadding: 4px 16px;
@pdfviewerToolbarButtonSize: 26px;
@pdfviewerToolbarMargins: 8px;
@pdfviewerToolbarButtonColor: @textColorDefault;
@pdfviewerToolbarButtonBackgroundColor: @colorBackground3;
@pdfviewerToolbarButtonHoverColor: @textColorDefault;
@pdfviewerToolbarButtonHoverBackgroundColor: @colorPrimaryLighter;
@pdfviewerToolbarButtonActiveColor: @textColorDefault3;
@pdfviewerToolbarButtonActiveBackgroundColor: @colorPrimary;
@pdfviewerToolbarSeparatorColor: @baseBorderColor;

// searchbox
@searchboxHeight: 34px;
@searchboxFontSize: @fontSizeMedium;
@searchboxPadding: @inputPadding;
@searchboxBorderColor: @inputBorderColor;
@searchboxColor: @inputColor;
@searchboxPlaceholderColor: @inputPlaceholderColor;
@searchboxIconColor: @iconColor;
@searchboxBackgroundColor: @inputBackgroundColor;
@searchboxBorderRadius: @inputBorderRadius;
@searchboxHoverBorderColor: @inputHoverBorderColor;
@searchboxFocusBorderColor: @inputFocusBorderColor;
@searchboxActiveBorderColor: @colorPrimaryDark;
@searchboxActiveBackgroundColor: @colorPrimary;
@searchboxActiveColor: @textColorDefault3;
@searchboxActiveIconColor: @textColorDefault3;
@searchboxDisableBorderColor: @inputBorderColor;
@searchboxDisableBackgroundColor: @inputDisableBackgroundColor;
@searchboxDisableColor: @inputDisableColor;
@searchboxDisableIconColor: @iconDisabledColor;
@searchboxPopupBackgroundColor: @textColorDefault3;
@searchboxPopupBorderColor: @comboPopupBorderColor;
@searchboxPopupBorderRadius: @baseBorderRadius;
@searchboxPopupShadow: 0 3px 6px rgba(0, 0, 0, 0.3);
@searchboxPopupItemBorderRadius: @baseBorderRadius;
@searchboxPopupItemHoverBackgroundColor: @colorPrimaryLighter;
@searchboxPopupItemHoverColor: @textColorDefault;
@searchboxPopupItemActiveBackgroundColor: @colorPrimaryLight;
@searchboxPopupItemActiveColor: @textColorDefault;
@searchboxPopupItemSelectedColor: @textColorActive;
@searchboxPopupItemCheckSize: 20px;
@searchboxPopupItemCheckBorderColor: @checkedBorderColor;
@searchboxPopupItemCheckBorderRadius: @baseBorderRadius;
@searchboxPopupItemCheckBackgroundColor: @baseBackgroundColor;
@searchboxPopupItemCheckCheckedColor: @textColorDefault3;
@searchboxPopupItemCheckCheckedBackgroundColor: @colorPrimary;
// stepbar
@stepbarPadding: 16px;
@stepbarSeparatorSize: 6px;
@stepSize: 32px;
@stepFontSize: 16px;
@stepInactiveColor: @colorGreyLight;
@stepActiveColor: @colorPrimary;
@stepErrorColor: @colorAccent2;
@stepCompleteColor: @colorPrimary;
@stepHoverColor: @colorPrimaryLight;
@stepClickColor: @colorPrimaryDark;

// portalchildren
@portalchildrenFrameBorderColor: @colorGreyDark;
@portalchildrenFrameRadius: 4px;
@portalchildrenFrameBackgroundColor: @colorBackground1;
@portalchildrenFrameMargin: 16px;
@portalchildrenFramePadding: 16px;
@portalchildrenFrameTitleFontFamily: @baseTitleFontFamily;
@portalchildrenFrameTitleFontSize: @fontSizeMedium;
@portalchildrenFrameTitleFontColor: @containerHeaderColor;
@portalchildrenCounterRadius: 10px;
@portalchildrenCounterPadding: 8px;
@portalchildrenCounterBackground: @colorGreyLight;
@portalchildrenFramePanelHeaderTextColor: @textColorActive;
@portalchildrenFramePanelHeaderTextSize: @fontSizeMedium;
@portalchildrenFramePanelHeaderPadding: 8px 16px 0 16px;
@portalchildrenFramePanelChildrenPadding: 8px 16px;
@portalchildrenFramePanelBackgroundColor: @colorBackground3;
@portalchildrenFramePanelPadding: 8px;
@portalchildrenFrameDragButtonSize: @baseIconWidth;
@portalchildrenFrameDragButtonColor: @colorGreyDark;

// linelayout
@linelayoutLineColor: @colorPrimary;
@linelayoutLineWidth: 6px;
@linelayoutCavePadding: 24px;
@linelayoutPointBorder: 0;
@linelayoutPointRadius: 50%;
@linelayoutPointSize: 32px;
@linelayoutPointIconSize: 24px;
@linelayoutPointIconColor: @textColorDefault3;
@linelayoutPointIconFixTop: 0;
@linelayoutPointIconFixLeft: 0;
@linelayoutPointBackgroundColor: @colorPrimary;
@linelayoutPointShadow: 0 0 4px rgba(0,0,0,0.3);

// coachmark
@coachmarkPadding: 12px;
@coachmarkPaddingLeft: 16px;
@coachmarkPaddingRight: 30px;
@coachmarkIconSize: 18px;
@coachmarkMaskBackground: rgba(0, 0, 0, 0.5);
@coachmarkBackgroundColor: @colorBackground3;

//cascader
@cascaderHeight: 34px;
@cascaderColor: @textColorDefault;
@cascaderFontSize: @fontSizeMedium;
@cascaderPadding: @inputPadding;
@cascaderSeparatorColor: @colorGreyLight;
@cascaderBorderColor: @inputBorderColor;
@cascaderBorderRadius: @inputBorderRadius;
@cascaderBackgroundColor: @inputBackgroundColor;
@cascaderIconColor: @iconColor;
@cascaderPlaceholderColor: @inputPlaceholderColor;
@cascaderActiveBorderColor: @colorPrimaryDark;
@cascaderActiveBackgroundColor: @colorPrimary;
@cascaderHoverBorderColor: @inputHoverBorderColor;
@cascaderFocusBorderColor: @inputFocusBorderColor;
@cascaderDisableColor: @inputDisableColor;
@cascaderDisableBackgroundColor: @inputDisableBackgroundColor;
@cascaderPopupBorderColor: @comboPopupBorderColor;
@cascaderPopupCavePadding: 4px;
@cascaderPopupItemPadding: 0px 28px 0px 5px;
@cascaderPopupItemHeight: 34px;
@cascaderPopupItemHoverBackgroundColor: @colorPrimaryLighter;
@cascaderPopupItemSelectedColor: @colorPrimary;