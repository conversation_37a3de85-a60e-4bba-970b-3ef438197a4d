// Z<PERSON> Mixins Utility

// EL Function
// --------------------------------------------

//---------------------------------------------
// web/core.dsp.tld
//---------------------------------------------
.encodeURL(@property, @url, @rest...) when (@property = background) {
	e('background: url(${c:encodeURL("@{url}")}) @{rest};');
}
.encodeURL(@property, @url, @rest...) when (@property = background-image) {
	e('background-image: url(${c:encodeURL("@{url}")});');
}
.encodeThemeURL(@property, @url, @rest...) when (@property = background) {
	e('background: url(${c:encodeThemeURL("@{url}")}) @{rest};');
}
.encodeThemeURL(@property, @url, @rest...) when (@property = background-image) {
	e('background-image: url(${c:encodeThemeURL("@{url}")});');
}
// only tablet.less uses this, no need render css for old IE
.encodeURL-verGradient(@url, @start, @end) {
	e('background:	url(${c:encodeURL("@{url}")});');
	e('background:	url(${c:encodeURL("@{url}")}), -moz-linear-gradient(top, @{start} 0%, @{end} 100%);'); /* FF3.6+ */
	e('background:	url(${c:encodeURL("@{url}")}), -webkit-gradient(linear, left top, left bottom, color-stop(0%, @{start}),color-stop(100%, @{end}));'); /* Chrome,Safari4+ */
	e('background:	url(${c:encodeURL("@{url}")}), -webkit-linear-gradient(top, @{start} 0%, @{end} 100%);'); /* Chrome10+,Safari5.1+ */
	e('background:	url(${c:encodeURL("@{url}")}), -o-linear-gradient(top, @{start} 0%, @{end} 100%);'); /* Opera 11.10+ */
	e('background:	url(${c:encodeURL("@{url}")}), -ms-linear-gradient(top, @{start} 0%, @{end} 100%);'); /* IE10+ */
	e('background:	url(${c:encodeURL("@{url}")}), linear-gradient(to bottom, @{start} 0%, @{end} 100%);'); /* W3C */
}
//---------------------------------------------
// theme.dsp.tld
//---------------------------------------------
// gradient
.gradient(@direction, @value) {
	// can be used directly for all except old webkit
	@valueList: ~`(function(a){return a.replace(/;/g, ',')})(@{value})`;
	@fallbackColor: ~`(function(a){return a.split(';')[0].trim().split(/\s(\d+%)$/g)[0]})(@{value})`;
	// for old webkit
	@valuesWebkit: ~`(function(a){var result='';var b=a.split(';');b.forEach(function(c){var d=c.trim().split(/\s(\d+%)$/g);result+='color-stop(' + d[1] + ',' + d[0] + '),';});return result.slice(0, -1)})(@{value})`;
	// for ie9, color stop is the same in all gradient direction, only prefix/suffix are different
	@svgContent: ~`(function(a){var result='';var b=a.split(';');b.forEach(function(c){var d=c.trim().split(/\s(\d+%)$/g);result+='<stop offset="' + d[1] + '" stop-color="' + d[0] + '"/>';});return result})(@{value})`;
	.gradient-ver();
	.gradient-hor();
	.gradient-diagm();
	.gradient-diagp();
	.gradient-rad();
}
.gradient-ver() when (@direction = 'ver') {
	background:	@fallbackColor; /* Old browsers */
	@svgPrefix: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><linearGradient id="zkie9" gradientUnits="userSpaceOnUse" x1="0%" y1="0%" x2="0%" y2="100%">';
	@svgSuffix: ~'</linearGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	@svg: e('@{svgPrefix}@{svgContent}@{svgSuffix}');
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-linear-gradient(top, @valueList); /* FF3.6+ */
	background:	-webkit-gradient(linear, left top, left bottom, @valuesWebkit); /* Chrome,Safari4+ */
	background:	-webkit-linear-gradient(top, @valueList); /* Chrome10+,Safari5.1+ */
	background:	-o-linear-gradient(top, @valueList); /* Opera 11.10+ */
	background:	-ms-linear-gradient(top, @valueList); /* IE10+ */
	background:	linear-gradient(to bottom, @valueList); /* W3C */
}

.gradient-hor() when (@direction = 'hor') {
	background:	@fallbackColor; /* Old browsers */
	@svgPrefix: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><linearGradient id="zkie9" gradientUnits="userSpaceOnUse" x1="0%" y1="0%" x2="100%" y2="0%">';
	@svgSuffix: ~'</linearGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	@svg: e('@{svgPrefix}@{svgContent}@{svgSuffix}');
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-linear-gradient(left, @valueList); /* FF3.6+ */
	background:	-webkit-gradient(linear, left top, right top, @valuesWebkit); /* Chrome,Safari4+ */
	background:	-webkit-linear-gradient(left, @valueList); /* Chrome10+,Safari5.1+ */
	background:	-o-linear-gradient(left, @valueList); /* Opera 11.10+ */
	background:	-ms-linear-gradient(left, @valueList); /* IE10+ */
	background:	linear-gradient(to right, @valueList); /* W3C */
}
.gradient-diagm() when (@direction = 'diag-') {
	background:	@fallbackColor; /* Old browsers */
	@svgPrefix: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><linearGradient id="zkie9" gradientUnits="userSpaceOnUse" x1="0%" y1="0%" x2="100%" y2="100%">';
	@svgSuffix: ~'</linearGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	@svg: e('@{svgPrefix}@{svgContent}@{svgSuffix}');
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-linear-gradient(-45deg, @valueList); /* FF3.6+ */
	background:	-webkit-gradient(linear, left top, right bottom, @valuesWebkit); /* Chrome,Safari4+ */
	background:	-webkit-linear-gradient(-45deg, @valueList); /* Chrome10+,Safari5.1+ */
	background:	-o-linear-gradient(-45deg, @valueList); /* Opera 11.10+ */
	background:	-ms-linear-gradient(-45deg, @valueList); /* IE10+ */
	background:	linear-gradient(135deg, @valueList); /* W3C */
}
.gradient-diagp() when (@direction = 'diag+') {
	background:	@fallbackColor; /* Old browsers */
	@svgPrefix: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><linearGradient id="zkie9" gradientUnits="userSpaceOnUse" x1="0%" y1="100%" x2="100%" y2="0%">';
	@svgSuffix: ~'</linearGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	@svg: e('@{svgPrefix}@{svgContent}@{svgSuffix}');
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-linear-gradient(45deg, @valueList); /* FF3.6+ */
	background:	-webkit-gradient(linear, left bottom, right top, @valuesWebkit); /* Chrome,Safari4+ */
	background:	-webkit-linear-gradient(45deg, @valueList); /* Chrome10+,Safari5.1+ */
	background:	-o-linear-gradient(45deg, @valueList); /* Opera 11.10+ */
	background:	-ms-linear-gradient(45deg, @valueList); /* IE10+ */
	background:	linear-gradient(45deg, @valueList); /* W3C */
}
.gradient-rad() when (@direction = 'rad') {
	background:	@fallbackColor; /* Old browsers */
	@svgPrefix: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><radialGradient id="zkie9" gradientUnits="userSpaceOnUse" cx="50%" cy="50%" r="50%">';
	@svgSuffix: ~'</radialGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	@svg: e('@{svgPrefix}@{svgContent}@{svgSuffix}');
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-radial-gradient(center, ellipse cover, @valueList); /* FF3.6+ */
	background:	-webkit-gradient(radial, center center, 0px, center center, 100%, @valuesWebkit); /* Chrome,Safari4+ */
	background:	-webkit-radial-gradient(center, ellipse cover, @valueList); /* Chrome10+,Safari5.1+ */
	background:	-o-radial-gradient(center, ellipse cover, @valueList); /* Opera 11.10+ */
	background:	-ms-radial-gradient(center, ellipse cover, @valueList); /* IE10+ */
	background:	radial-gradient(ellipse at center, @valueList); /* W3C */
}

// box layout
.boxOrientHor() {
	display: -webkit-box;
	display: -moz-box;
	display: box;

	-webkit-box-orient: horizontal;
	-moz-box-orient: horizontal;
	-o-box-orient: horizontal;
	-ms-box-orient: horizontal;
	box-orient: horizontal;
}
.boxOrientHorFlex() {
	display: -webkit-box;
	display: -moz-box;
	display: box;

	-webkit-box-orient: horizontal;
	-moz-box-orient: horizontal;
	-o-box-orient: horizontal;
	-ms-box-orient: horizontal;
	box-orient: horizontal;

	-webkit-box-flex: 1;
	-moz-box-flex: 1;
	-o-box-flex: 1;
	-ms-box-flex: 1;
	box-flex: 1;
}
// box-shadow
.boxShadow(@value) when (isstring(@value)) {
	-webkit-box-shadow: e(@value);
	-moz-box-shadow: e(@value);
	-o-box-shadow: e(@value);
	-ms-box-shadow: e(@value);
	box-shadow: e(@value);
}
.boxShadow(@value) when not (isstring(@value)) {
	-webkit-box-shadow: @value;
	-moz-box-shadow: @value;
	-o-box-shadow: @value;
	-ms-box-shadow: @value;
	box-shadow: @value;
}
// border-radius
.borderRadius(@size) when (isstring(@size)) {
	-webkit-border-radius: e(@size);
	-moz-border-radius: e(@size);
	-o-border-radius: e(@size);
	-ms-border-radius: e(@size);
	border-radius: e(@size);
}
.borderRadius(@size) when not (isstring(@size)) {
	-webkit-border-radius: @size;
	-moz-border-radius: @size;
	-o-border-radius: @size;
	-ms-border-radius: @size;
	border-radius: @size;
}
// transform
.transform(@value) when (isstring(@value)) {
	-webkit-transform: e(@value);
	-moz-transform: e(@value);
	-o-transform: e(@value);
	-ms-transform: e(@value);
	transform: e(@value)
}
.transform(@value) when not (isstring(@value)) {
	-webkit-transform: @value;
	-moz-transform: @value;
	-o-transform: @value;
	-ms-transform: @value;
	transform: @value
}
// CSS3
.applyCSS3(@key, @value) {
	@ekey: e(@key);
	-webkit-@{ekey}: e(@value);
	-moz-@{ekey}: e(@value);
	-o-@{ekey}: e(@value);
	-ms-@{ekey}: e(@value);
	@{ekey}: e(@value)
}

//---------------------------------------------
// Other helpful utilities
//---------------------------------------------
// gradient utility
.horGradient(@start, @end) when (@start = @end) {
	background: @start;
}
.horGradient(@start, @end) when not (@start = @end) {
	background:	@start; /* Old browsers */
	@svg: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><linearGradient id="zkie9" gradientUnits="userSpaceOnUse" x1="0%" y1="0%" x2="100%" y2="0%"><stop stop-color="@{start}" offset="0%"/><stop stop-color="@{end}" offset="100%"/></linearGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-linear-gradient(left, @start 0%, @end 100%); /* FF3.6+ */
	background:	-webkit-gradient(linear, left top, right top, color-stop(0%,@start), color-stop(100%, @end)); /* Chrome,Safari4+ */
	background:	-webkit-linear-gradient(left, @start 0%, @end 100%); /* Chrome10+,Safari5.1+ */
	background:	-o-linear-gradient(left, @start 0%, @end 100%); /* Opera 11.10+ */
	background:	-ms-linear-gradient(left, @start 0%, @end 100%); /* IE10+ */
	background:	linear-gradient(to right, @start 0%, @end 100%); /* W3C */
}
.verGradient(@start, @end) when (@start = @end) {
	background: @start;
}
.verGradient(@start, @end) when not (@start = @end) {
	background:	@start; /* Old browsers */
	@svg: ~'<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1 1" preserveAspectRatio="none"><linearGradient id="zkie9" gradientUnits="userSpaceOnUse" x1="0%" y1="0%" x2="0%" y2="100%"><stop stop-color="@{start}" offset="0%"/><stop stop-color="@{end}" offset="100%"/></linearGradient><rect x="0" y="0" width="1" height="1" fill="url(#zkie9)" /></svg>';
	.base64DataUriBackground(@svg); /* IE9 */
	background:	-moz-linear-gradient(top, @start 0%, @end 100%); /* FF3.6+ */
	background:	-webkit-gradient(linear, left top, left bottom, color-stop(0%, @start),color-stop(100%, @end)); /* Chrome,Safari4+ */
	background:	-webkit-linear-gradient(top, @start 0%, @end 100%); /* Chrome10+,Safari5.1+ */
	background:	-o-linear-gradient(top, @start 0%, @end 100%); /* Opera 11.10+ */
	background:	-ms-linear-gradient(top, @start 0%, @end 100%); /* IE10+ */
	background:	linear-gradient(to bottom, @start 0%, @end 100%); /* W3C */
}
.base64DataUriBackground(@svgToEncode, @type: ~"image/svg+xml") {
	@dataUriPrefix: ~"url(data:@{type};base64,";
	@dataUriSuffix: ~")";
	@b64DataUri: ~`(function(a,b,c){function e(a){a=a.replace(/\r\n/g,'\n');var b='';for(var c=0;c<a.length;c++){var d=a.charCodeAt(c);if(d<128){b+=String.fromCharCode(d)}else if(d>127&&d<2048){b+=String.fromCharCode(d>>6|192);b+=String.fromCharCode(d&63|128)}else{b+=String.fromCharCode(d>>12|224);b+=String.fromCharCode(d>>6&63|128);b+=String.fromCharCode(d&63|128)}}return b}function f(a){var b='';var c,f,g,h,i,j,l;var m=0;a=e(a);while(m<a.length){c=a.charCodeAt(m++);f=a.charCodeAt(m++);g=a.charCodeAt(m++);h=c>>2;i=(c&3)<<4|f>>4;j=(f&15)<<2|g>>6;l=g&63;if(isNaN(f)){j=l=64}else if(isNaN(g)){l=64}b=b+d.charAt(h)+d.charAt(i)+d.charAt(j)+d.charAt(l)}return b}var d='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';return a+f(b)+c})('@{dataUriPrefix}','@{svgToEncode}','@{dataUriSuffix}')`;
	background: @b64DataUri;
}
.resetGradient() {
	background: none;
	filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}

// border-radius utility
.topBorderRadius(@size) {
	-webkit-border-radius: @size @size 0 0;
	-moz-border-radius: @size @size 0 0;
	-o-border-radius: @size @size 0 0;
	-ms-border-radius: @size @size 0 0;
	border-radius: @size @size 0 0;
}
.rightBorderRadius(@size) {
	-webkit-border-radius: 0 @size @size 0;
	-moz-border-radius: 0 @size @size 0;
	-o-border-radius: 0 @size @size 0;
	-ms-border-radius: 0 @size @size 0;
	border-radius: 0 @size @size 0;
}
.bottomBorderRadius(@size) {
	-webkit-border-radius: 0 0 @size @size;
	-moz-border-radius: 0 0 @size @size;
	-o-border-radius: 0 0 @size @size;
	-ms-border-radius: 0 0 @size @size;
	border-radius: 0 0 @size @size;
}
.leftBorderRadius(@size) {
	-webkit-border-radius: @size 0 0 @size;
	-moz-border-radius: @size 0 0 @size;
	-o-border-radius: @size 0 0 @size;
	-ms-border-radius: @size 0 0 @size;
	border-radius: @size 0 0 @size;
}

// opacity utility
.opacity(@opacity) {
	opacity: @opacity;
	@opacity100: @opacity * 100;
	filter: ~'alpha(opacity=@{opacity100});';
}

// icon font utility
.baseIconFont() {
	display: inline-block;
	font-family: ZK85Icons, FontAwesome;
	font-style: normal;
	font-weight: normal;
	font-size: inherit;
	line-height: 1;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	text-rendering: auto; // optimizelegibility throws things off #1094
}

// other utility
.size(@width, @height) {
	width: @width;
	height: @height;
}
.displaySize(@display, @width, @height) {
	display: @display;
	.size(@width, @height);
}
.fontStyle(@family, @size, @weight, @color) {
	font-family: @family;
	font-size: @size;
	font-weight: @weight;
	font-style: normal;
	color: @color;
}
.fontStyle(@family, @size, @weight) {
	font-family: @family;
	font-size: @size;
	font-weight: @weight;
	font-style: normal;
}
.iconFontStyle(@size, @color) {
	font-size: @size;
	color: @color;
}

.userSelectNone() {
	-webkit-touch-callout: none;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
