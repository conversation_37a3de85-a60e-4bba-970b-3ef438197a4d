#!/bin/bash
# build
#
#	Purpose:
#		
#	Description:
#		
#	History:
#		Wed Oct 18 11:34:55	 2017, Created by rudyhaung
#
#Copyright (C) 2017 Potix Corporation. All Rights Reserved.
#

group="org.zkoss.themepack"
install=false # -i Install the theme into Maven local repo
update=false  # -u Update git submodule first
source=false  # -s Generate source archives for publishing
allthemes=    # -t Build themes only, separeted by space (ex: a b c)
doclean=false # -c Do `mvn clean` first
pack=false    # -p Build theme-pack (Themes all-in-one jar)
compact=false  # -n Build compact (formerly narrow) theme too (original + compact)
parallel=false # -P Enable parallel build

while getopts 'iust:cpnP' flag; do
  case "${flag}" in
    i) install=true ;;
    u) update=true ;;
    s) source=true ;;
    t) allthemes=${OPTARG} ;;
    c) doclean=true ;;
    p) pack=true ;;
    n) compact=true ;;
    P) parallel=true ;;
    ?) exit 1 ;;
  esac
done

source ./yaml.sh
# parse_yaml build.projects.yml && echo
create_variables build.projects.yml

if [ "$allthemes" = "" ]
then
    # Themes to process (e.g. allthemes=sapphire silvertail)
    allthemes="${build_themes[@]}"
fi

# UpVer
zkver=$(head -1 version)
themever=$(tail -1 version)
datever=`date +%Y%m%d`
mvnver=$themever.FL.$datever
#sed -ie "s/zk.version>.*<\/zk.version/zk.version>$zkver.FL.$datever<\/zk.version/" pom.xml
#sed -i "1,/version>.*<\/version/s/version>.*<\/version/version>$mvnver<\/version/" pom.xml

buildVer=$(mvn help:evaluate --no-snapshot-updates -Dexpression=project.version | grep -e '^[^\[]')

if [ "$update" = true ]
then
    # Git submodule (ensure its contents are newest)
    git submodule foreach --recursive git reset --hard
    git submodule update --init --remote
fi
rm -rf src/archive/web
cp -R template/src/archive/web src/archive/

echo "Patching _header.less..."
echo -e '\n@import "_zkthemepalette.less";' >> src/archive/web/zul/less/_header.less

[ "$doclean" = true ] && mvn clean

# Build a theme
# $1: Theme project name (used in projects/)
# $2: Theme identify name (used in ZK)
# $3: Theme display name (used in ZK)
function buildTheme {
    local theme="$1"
    local themeIdentifier="$2"
    local themeDisplay="$3"

    if [ -f "projects/${theme}.less" ]
    then
        local skipAssembly="-Dassembly.skipAssembly=true"
        [ "$source" = true ] && skipAssembly=""
        
        echo "Building ${themeIdentifier}..."
        if ! mvn -Dtheme.name="${themeIdentifier}" -Dtheme.display="${themeDisplay}" \
            -Dtheme.project="${theme}" ${skipAssembly} package; then
            exit 1
        fi
    fi
}

# A build worker
# $1: theme list
# $2: working directory
function buildWorker {
    for theme in $1
    do
        [ "$theme" = "iceblue" ] && continue # we don't need to build another iceblue
        (
        cd "$2" || exit
        name_key="themes_${theme}_name"
        display_key="themes_${theme}_display"
        buildTheme "${theme}" "${!name_key}" "${!display_key}"
        )
    done
}

# A build worker (Compact)
# $1: theme list
# $2: working directory
function buildWorkerCompact {
    (
    cd "$2" || exit
    echo "Copying compact patch..."
    cp projects/compact/patch.less src/archive/web/zul/less/
    cp projects/compact/_compact.less src/archive/web/zul/less/
    echo -e '\n@import "_compact.less";' >> src/archive/web/zul/less/_header.less
    yes | cp -f projects/compact/tablet/tablet.less src/archive/web/zkmax/less/
    yes | cp -Rf projects/compact/tablet/tablet src/archive/web/zkmax/less/

    for theme in $1
    do
        name_key="themes_${theme}_name"
        display_key="themes_${theme}_display"
        buildTheme "${theme}" "${!name_key}_c" "${!display_key} Compact"
    done
    )
}

# Generate source files
if [ "$source" = true ]
then
    echo "Generating source archive..."
    echo $allthemes | xargs -n 1 -P 4 ./generate-source.sh
fi

if [ "$parallel" = true ]
then
    echo "Try parallel build."
    PDIR=$(mktemp -d) || { echo "Failed to parallel!"; parallel=false; }
    [ "$parallel" = true ] && cp -R . "$PDIR"
    [ -d "$PDIR/target" ] && find "$PDIR/target" -type f \( -name '*.jar' -o -name '*.zip' \) -delete

    themes=(${allthemes})
    themes_size=${#themes[@]}
    themes_size_half=$((themes_size / 2))
    themes_worker1="${themes[@]:0:$themes_size_half}"
    themes_worker2="${themes[@]:$themes_size_half}"
    echo "Worker1: $themes_worker1"
    echo "Worker2: $themes_worker2"
else
    themes_worker1=$allthemes
fi

# Normal
[ "$parallel" = true ] && buildWorker "$themes_worker2" "$PDIR" &
buildWorker "$themes_worker1" "$(pwd)"

# Compact
if [ "$compact" = true ]
then
    if [ "$parallel" = true ]
    then
        echo "Wait for sub processes to finish..."
        wait
        buildWorkerCompact "$themes_worker2" "$PDIR" &
    fi
    buildWorkerCompact "$themes_worker1" "$(pwd)"
fi

if [ "$parallel" = true ]
then
    echo "Wait for sub processes to finish..."
    wait
    echo "Merge the result."
    find "$PDIR/target" -type f \( -name '*.jar' -o -name '*.zip' \) -exec cp -f {} ./target \;
    rm -rf "$PDIR"
fi

# Install
if [ "$install" = true ]
then
    for theme in $allthemes
    do
        echo "Installing ${themeIdentifier}..."
        name_key="themes_${theme}_name"
        display_key="themes_${theme}_display"

        if [ "$compact" = true ]
        then
            themeIdentifier="${!name_key}_c"
            themeDisplay="${!display_key} Compact"
            mvn -Dfile="target/${themeIdentifier}-${buildVer}.jar" \
                -DgroupId="${group}" -DartifactId="${themeIdentifier}" \
                -Dversion="${buildVer}" -Dpackaging=jar install:install-file
        fi

        [ "$theme" = "iceblue" ] && continue # no iceblue
        themeIdentifier="${!name_key}"
        themeDisplay="${!display_key}"
        
        mvn -Dfile="target/${themeIdentifier}-${buildVer}.jar" \
            -DgroupId="${group}" -DartifactId="${themeIdentifier}" \
            -Dversion="${buildVer}" -Dpackaging=jar install:install-file
    done
fi

# Theme pack
if [ "$pack" = true ]
then
    packName=theme-pack
    packDir=target/${packName}
    rm -rf $packDir && mkdir -p $packDir

    # Extract all themes
    cd $packDir
    find ../ -type f -name "*-${buildVer}.jar" -exec jar -xf {} \;

    # Find themes
    declare -a THEMES
    for d in web/*
    do
        [ -d "$d" ] || continue
        THEMES[${#THEMES[@]}+1]=$(basename "$d")
    done
    echo "Found themes: ${THEMES[@]}"

    if [ ${#THEMES[@]} -eq 0 ]
    then
        echo "ERROR: No theme found. Abort."
        exit 1
    fi

    # Edit ZK metainfo
    # config.xml
    sed -i "s/config-name>.*<\/config-name/config-name>${packName}<\/config-name/" metainfo/zk/config.xml

    function theme_listener(){
        echo "\t<listener>\n\t\t<listener-class>org.zkoss.theme.$1.ThemeWebAppInit</listener-class>\n\t</listener>"
    }

    sed -i '/<listener>/{:a;N;/<\/listener>/!ba};/<listener-class/d' metainfo/zk/config.xml
    for theme in ${THEMES[@]}
    do
        C=$(theme_listener $theme | sed 's/\//\\\//g')
        sed -i "/<\/config>/ s/.*/${C}\n&/" metainfo/zk/config.xml
        echo "Adding $theme listener..."
    done

    # lang-addon.xml
    sed -i -e "s/addon-name>.*<\/addon-name/addon-name>${packName}<\/addon-name/" metainfo/zk/lang-addon.xml

    # Repack
    jar -cf "../${packName}-${buildVer}.jar" .
    echo "Pack completed."

    theme=theme-pack
    cd ../../

    if [ "$source" = true ]
    then
        echo "Generateing source archive of ${theme}..."
        ./generate-source.sh

        echo "Building ${theme}..."
        # generate sources.jar
        mvn -Dtheme.project="${theme}" -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/sources.xml \
            -DfinalName=${theme}-$buildVer assembly:single

        # generate pom.xml + bundle.jar
        mvn -Dtheme.project="${theme}" -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/bundle.xml \
            -DfinalName=${theme}-$buildVer assembly:single

        # generate bin.zip, src.zip
        mvn -Dtheme.project="${theme}" -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/src.xml \
            -DfinalName=${theme}-src-$buildVer assembly:single
        mvn -Dtheme.project="${theme}" -Dtheme.name="${theme}" -Dtheme.display="Theme pack" \
            -Dmaven.main.skip=true -Ddescriptor=assembly/bin.xml \
            -DfinalName=${theme}-bin-$buildVer assembly:single
        mv target/${theme}-src-$buildVer-src.zip target/${theme}-src-$buildVer.zip
        mv target/${theme}-bin-$buildVer-bin.zip target/${theme}-bin-$buildVer.zip
    fi

    if [ "$install" = true ]
    then
        echo "Installing ${theme}..."
        mvn -Dfile="target/${theme}-${buildVer}.jar" \
            -DgroupId=${group} -DartifactId=${theme} \
            -Dversion=${buildVer} -Dpackaging=jar install:install-file
    fi
fi
