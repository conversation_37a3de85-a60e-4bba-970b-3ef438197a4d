package freedom.util;

import freedom.client.controls.ITFBox;
import freedom.client.controls.impl.TFMemo;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.event.UploadEvent;
import freedom.client.util.FreedomUtilities;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.Textbox;

public class MemoUtil {

    /**
     * Torna um campo de texto (TFMemo) expansível à medida que o usuário digita.
     *
     * @param campoTexto O campo de texto a ser tornado expansível.
     * @param boxAncestralExpansivo A classe CSS que define até qual elemento a expansão deve ocorrer. importante pois se o pai não puder expandir .
     */
    public static void tornarMemoExpansivelConformeDigita(TFMemo campoTexto, ITFBox boxAncestralExpansivo) {
        FreedomUtilities.invokeLater(() -> {
            // Adiciona um ouvinte de evento para ajustar a altura conforme o usuário digita
            String textareaId = ((Textbox) campoTexto.getImpl()).getUuid();
            String boxAncestralExpansivoClasse = boxAncestralExpansivo.getName().toLowerCase();
            //org.zkoss.zk.ui.util.Clients.evalJavaScript(buildInputListenerScript(textareaId, classeExpansivel));

            ((Textbox) campoTexto.getImpl()).addEventListener(Events.ON_FOCUS, (event) -> {
                org.zkoss.zk.ui.util.Clients.evalJavaScript(buildInputListenerScript(textareaId, boxAncestralExpansivoClasse));
                org.zkoss.zk.ui.util.Clients.evalJavaScript(buildAdjustHeightScript(textareaId, boxAncestralExpansivoClasse));
            });
        });
    }

    /**
     * Constrói o script JavaScript para ajustar a altura das divs pais até encontrar o elemento com a classeExpansivel.
     *
     * @param textareaId O ID único do textarea.
     * @param boxAncestralExpansivoClasse A classe CSS que define até qual elemento a expansão deve ocorrer.
     * @return O script JavaScript gerado.
     */
    private static String buildAdjustHeightScript(String textareaId, String boxAncestralExpansivoClasse) {
        return  "(()=>{" +
                "let elementosAjustes = document.querySelector('#" + textareaId + "');\n" +
                "elementosAjustes.style.height = 'auto';\n" +
                "elementosAjustes.style.height = (elementosAjustes.scrollHeight) + 'px';\n" +
                "elementosAjustes = elementosAjustes.parentNode;\n" +
                "while (elementosAjustes && !elementosAjustes.classList.contains('" + boxAncestralExpansivoClasse + "')){\n" +
                "    elementosAjustes.classList.add('boxMemoExpansivo');\n" +
                "    elementosAjustes = elementosAjustes.parentNode;\n" +
                "}"+
                "})();";
    }

    /**
     * Constrói o script JavaScript para adicionar um ouvinte de evento que ajusta a altura do textarea conforme o usuário digita.
     *
     * @param textareaId O ID único do textarea.
     * @param boxAncestralExpansivoClasse A classe CSS que define até qual elemento a expansão deve ocorrer.
     * @return O script JavaScript gerado.
     */
    private static String buildInputListenerScript(String textareaId, String boxAncestralExpansivoClasse) {
        return String.format(
                            "(()=>{" +
                            "let textarea = document.querySelector('#%s');\n" +
                            "if (!textarea.hasAbaxaci){" +
                            "textarea.addEventListener('input', adjustTextAreaHeight);\n" +
                            "textarea.addEventListener('focus', adjustTextAreaHeight);\n" +
                            "textarea.addEventListener('blur', resetTextAreaHeight);\n" +
                            "function adjustTextAreaHeight() {\n" +
                            "    this.style.height = 'auto';\n" +
                            "    this.style.height = (this.scrollHeight) + 'px';\n" +
                            "}\n" +
                            "function adjustTextAreaHeightAndParentHeight() {\n" +
                            "    adjustTextAreaHeight.call(this);\n" +
                            "    let elementosAjustes = this.parentNode;\n" +
                            "}\n" +
                            "function resetTextAreaHeight() {\n" +
                            "    this.style.height = 'auto';\n" +
                            "}\n" +
                                    "" +
                            "let elementosAjustes = document.querySelector('#" + textareaId + "');\n" +
                            "elementosAjustes = elementosAjustes.parentNode;\n" +
                            "while (elementosAjustes && !elementosAjustes.classList.contains('" + boxAncestralExpansivoClasse + "')){\n" +
                            "    elementosAjustes.classList.add('boxMemoExpansivo');\n" +
                            "    elementosAjustes = elementosAjustes.parentNode;\n" +
                            "}" +
                                    ""+
                            " textarea.hasAbaxaci = true;" +
                            "}" +
                                    "" +
                            "})()",
                textareaId, boxAncestralExpansivoClasse
        );
    }

}
