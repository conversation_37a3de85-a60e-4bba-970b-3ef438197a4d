/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.client.util.Dialog;
import freedom.data.DataException;
import freedom.data.Value;

/**
 *
 * <AUTHOR>
 */
public class PackageResult {

    private final String retFuncao;

    public PackageResult(String msgRetorno) {
        this.retFuncao = msgRetorno;
    }

    public boolean podeContinuar() {
        return retFuncao.equals("S");
    }

    private void showMensagemPackage() {
        Dialog.create()
                .title("Validação")
                .message(retFuncao)
                .showWarning();
    }

    public static boolean abortar(String msgRetorno) {
        PackageResult pr = new PackageResult(msgRetorno);
        boolean vaiAbortar = !pr.podeContinuar();
        if (vaiAbortar) {
            pr.showMensagemPackage();
        }
        return vaiAbortar;
    }

    public static boolean abortar(Value msgRetorno) {
        PackageResult pr = new PackageResult(msgRetorno.asString());
        boolean vaiAbortar = !pr.podeContinuar();
        if (vaiAbortar) {
            pr.showMensagemPackage();
        }
        return vaiAbortar;
    }

    public static void testarRetorno(String msgRetorno) throws DataException {
        PackageResult pr = new PackageResult(msgRetorno);
        if (!pr.podeContinuar()) {
            throw new DataException(pr.retFuncao);
        }
    }

    public static void showWarning(String msgRetorno) throws DataException {
        PackageResult pr = new PackageResult(msgRetorno);
        if (!pr.podeContinuar()) {
            pr.showMensagemPackage();
        }
    }

}
