/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.client.controls.IBaseComponent;
import freedom.client.controls.ITFDataSourceField;
import freedom.client.controls.impl.TFTable;
import freedom.client.controls.impl.TFTableField;
import freedom.data.DataException;
import freedom.data.impl.Column;
import freedom.data.impl.RowType;


import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 */
public class TableUtil {

    public static boolean getExistField(TFTable table, String fieldName) {
        return table.getFieldDefs().stream().filter((f) -> f.getName().equals(fieldName)).findAny().isPresent();
    }

    /**
     * Copia o registro corrente do table source para o table target
     *
     * @param tbSource Tabela de Origem
     * @param tbTarget Tabela de Destino
     * @param resetTableTarget Define se irá limpar os dados do table de destino
     * ou não
     * @throws DataException
     */
    public static void copyCurrentRecord(TFTable tbSource, TFTable tbTarget, Boolean resetTableTarget) throws DataException {

        if (resetTableTarget) {
            tbTarget.close();
            tbTarget.clearFilters();
            tbTarget.clearParams();
        }

        tbTarget.append();

        for (Column col : tbTarget.getTable().getCols()) {
            if (getExistField(tbSource, col.getName())) {
                tbTarget.setField(col.getName(), tbSource.getField(col.getName()));
            }
        }
        tbTarget.post();
    }

    /**
     * Copia o registro todos os registros visivies do table source para o table
     * target
     *
     * @param tbSource Tabela de Origem
     * @param tbTarget Tabela de Destino
     * @param resetTableTarget Define se irá limpar os dados do table de destino
     * ou não
     * @throws DataException
     */
    public static void copyRecords(TFTable tbSource, TFTable tbTarget, Boolean resetTableTarget) throws DataException {
        if (resetTableTarget) {
            tbTarget.close();
            tbTarget.clearFilters();
            tbTarget.clearParams();
        }

        tbSource.first();
        while (!tbSource.eof()) {
            copyCurrentRecord(tbSource, tbTarget, false);
            tbSource.next();
        }

    }

    public static void addFakeRecordsInTable(TFTable table, int quantidade, int limiteNumero, int limiteTexto) throws DataException {
        String tipo;
        List<ITFDataSourceField> fieldDefs = table.getFieldDefs();
        Random random = new Random();

        for (int i = 0; i < quantidade; i++) {
            table.append();
            for (ITFDataSourceField fieldDef : fieldDefs) {
                String fieldType = fieldDef.getFieldType();
                switch (fieldType) {
                    case "ftInteger":
                        int randomInteger = random.nextInt(limiteNumero + 1);
                        table.setField(fieldDef.getName(), randomInteger);
                        break;

                    case "ftString":
                        String randomString = generateRandomString(limiteTexto, random);
                        table.setField(fieldDef.getName(), randomString);
                        break;

                    case "ftDecimal":
                        double randomDecimal = random.nextDouble() * limiteNumero;
                        table.setField(fieldDef.getName(), randomDecimal);
                        break;

                    case "ftDate":
                         Date randomDate = new Date();
                         table.setField(fieldDef.getName(), randomDate);
                        break;

                    default:
                        // Caso o tipo de campo não seja reconhecido, você pode optar por lançar uma exceção
                        // ou simplesmente ignorar o campo.
                        break;
                }
            }
        }
        table.post();
    }

    private static String generateRandomString(int length, Random random) {
        if (random == null){
            random = new Random();
        }
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < length; i++) {
            result.append(characters.charAt(random.nextInt(characters.length())));
        }
        return result.toString();
    }

    /**
     * Restaura o registro atual de uma tabela para um estado anterior utilizando um backup.
     * Essa função é uma alternativa ao método `table.cancel`, que reverte o registro para o estado inicial do banco de dados.
     * Exemplo de uso:
     *      RowType backupRegistro = tabelaExemplo.toRowType();
     *      TableUtils.restaurarRegistroComBackup(tabelaExemplo, backupRegistro);
     * @param tabela Tabela cujo registro atual será substituído pelo registro de backup.
     * @param registroBackup Registro de backup que será restaurado na tabela.
     * @throws DataException
     */
    public static String restaurarRegistroComBackup(TFTable tabela, RowType registroBackup) throws DataException {
        if (tabela == null || tabela.isEmpty()) {
            return "Não foi possivel restaurar um backup do registro, a Tabela está vazia";
        }
        if (registroBackup == null) {
            return "Não foi possivel restaurar um backup do registro, registro de backup está vazio";
        }
        if (!tabela.getName().equals(registroBackup.getTableId())){
            return "Não foi possivel restaurar um backup do registro, o registro de beckup não pertence a tabela";
        }
        tabela.edit();
        for (Map.Entry<String, Object> coluna : registroBackup.getItems().get(0).getValues().entrySet()) {
            try {
                tabela.setField(coluna.getKey(), coluna.getValue());
            } catch (DataException e) {
                throw new DataException("Erro ao restaurar backup: " + e.getMessage());
            }
        }
        tabela.post();
        return "";
    }

    /**
     * Insere ou atulaliza na tabela um registro registro passado como parametro
     * Caso um registro com a mesma chave primaria já exista apenas atualiza, caso não exista ele cria um novo
     * @param tabela
     * @param registro
     * @throws DataException
     */
    public static void inserirRegistro(TFTable tabela, RowType registro) throws DataException {
        tabela.disableControls();
        tabela.disableMasterTable();
        try {
            if (tabela == null) {
                return;
            }
            if (tabela.isEmpty()){
                tabela.addRowType(registro);
                return;
            }
            if (registro == null) {
                return;
            }

            List<String> keys = tabela.getFieldDefs().stream().filter(ITFDataSourceField::isPrimaryKey).map(IBaseComponent::getName).collect(Collectors.toList());
            List<Object> values = registro.getItems().get(0).getValues().entrySet().stream()
                                            .filter(entry -> keys.contains(entry.getKey()))
                                            .map(Map.Entry::getValue)
                                            .map(value -> value).collect(Collectors.toList());

            if (tabela.locate(String.join(",",keys), values.toArray())) {
                tabela.edit();

                for (Map.Entry<String, Object> coluna : registro.getItems().get(0).getValues().entrySet()) {
                    try {
                        tabela.setField(coluna.getKey(), coluna.getValue());
                    } catch (DataException e) {
                        throw new DataException("Erro ao restaurar backup: " + e.getMessage());
                    }
                }
                tabela.post();
            }else{
                tabela.addRowType(registro);
            }
        } finally {
            tabela.enableControls();
            tabela.enableMasterTable();
        }
    }

}
