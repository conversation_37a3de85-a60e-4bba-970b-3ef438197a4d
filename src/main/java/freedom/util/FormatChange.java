/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import static freedom.util.MaskUtil.format;

/**
 * <AUTHOR>
 */
public class FormatChange {

    public static String removeMaskFone(String valor) {

        valor = valor.replace("(", "");
        valor = valor.replace(")", "");
        valor = valor.replace("-", "");
        valor = valor.replace(" ", "");
        valor = valor.replace("_", "");

        return valor;
    }

    public static String formataCpfCnpj(String valor) {
        String maskCpf = "999.999.999-99";
        String maskCnpj = "99.999.999/9999-99";

        if (valor.length() == 11) {
            valor = format(valor, maskCpf);
        } else if (valor.length() == 14) {
            valor = format(valor, maskCnpj);
        }
        return valor;
    }
    
}
