package freedom.util;

import freedom.client.controls.impl.TFString;

public class EdtString {

    public static void definirMascaraDeTelefone(
            TFString edtString
    ) {
        String telefone = edtString.getValue().asString();
        if (telefone.length() > 10) {
            edtString.setMask(
                    "(99) 99999-999?9"
            );
        } else if (telefone.length() >= 8) {
            edtString.setMask(
                    "(99) 9999-9999?9"
            );
        }
        edtString.setValue(
                edtString.getValue()
        );
    }

}
