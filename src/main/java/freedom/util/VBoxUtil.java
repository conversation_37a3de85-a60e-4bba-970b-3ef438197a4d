package freedom.util;

import freedom.client.controls.impl.TFVBox;

public final class VBoxUtil {

    private VBoxUtil () {
        throw new UnsupportedOperationException(
                "Esta é uma classe utilitária e não pode ser instanciada."
        );
    }

    public static void aplicarBordaComEstiloArredondadoParaVBox(
            TFVBox... vboxes
    ) {
        for (TFVBox vbox : vboxes) {
            vbox.setAttribute(
                    Constantes.SCLASS_BASE
                    ,"boximagestrech boxborderpainel"
            );
        }
    }

}
