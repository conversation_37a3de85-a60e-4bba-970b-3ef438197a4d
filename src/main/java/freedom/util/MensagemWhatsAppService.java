/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.data.Value;
import java.io.ByteArrayOutputStream;
import java.util.List;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public class MensagemWhatsAppService {

    private final MensagemWhatsApp zap;

    public MensagemWhatsAppService(MensagemWhatsApp zap) {
        this.zap = zap;
    }

    public Object enviarMensagem(String url, int codEmpresa, String numberFrom,
            String numberTo, String token, String mensagem, String tokenAuth) throws Exception {
        return zap.enviarMensagem(url, codEmpresa, numberFrom, numberTo, token, mensagem, tokenAuth);
    }

    public JSONObject postFile(String url, int codEmpresa, String fileName, ByteArrayOutputStream anexo,
            ETypeFileWhatsApp typeFile, String token, String tokenAuth) throws Exception {
        return zap.postFile(url, codEmpresa, fileName, anexo, typeFile, token, tokenAuth);
    }

    public Object enviarArquivo(String numberFrom, String numberTo, String url,
            int codEmpresa, String fileName, ByteArrayOutputStream anexo,
            ETypeFileWhatsApp typeFile, String token, String tokenAuth,
            JSONObject jsonPostFile) throws Exception {
        return zap.enviarArquivo(numberFrom, numberTo, url, codEmpresa, fileName, anexo, typeFile,
                token, tokenAuth, jsonPostFile);
    }

    public String excluirTemplate(String url, String token, String tokenAuth, int id) throws Exception {
        return zap.excluirTemplate(url, token, tokenAuth, id);
    }

    public Object getUltimaMensagem(String url, String idMensagem, String tokenAuth) throws Exception {
        return zap.getUltimaMensagem(url, idMensagem, tokenAuth);
    }

    public Boolean getSessaoAtiva(
            String url
            ,String numberFrom
            ,String numberTo
            ,String tokenAuth
    ) throws Exception {
        return this.zap.getSessaoAtiva(
                url
                ,numberFrom
                ,numberTo
                ,tokenAuth
        );
    }

    public Object enviarMensagemTemplate(String url, int codEmpresa, String idTemplate,
            String numberFrom, String numberTo, List<TemplateVariable> variables, String token,
            String tokenAuth) throws Exception {
        return zap.enviarMensagemTemplate(url, codEmpresa, idTemplate, numberFrom, numberTo, variables, token, tokenAuth);
    }

    public String ajustaFone(String fone, String TP) {
        return zap.ajustaFone(fone, TP);
    }

    public String gerarHashChat(String from, String to) {
        return zap.gerarHashChat(from, to);
    }

    public List<TemplateVariable> getListTemplateVarValue(Value listVarValue, String mensagem) {
        return zap.getListTemplateVarValue(listVarValue, mensagem);
    }

    public String getTokenAut(String url, String usuario, String senha, Value mensageOut) throws Exception {
        return zap.getTokenAut(url, usuario, senha, mensageOut);
    }

    public String criarTemplate(String url, String token, String tokenAuth, String nomeTemplate,
            String text, String numberFrom, String emailNotification, Value mensageOut) throws Exception {
        return zap.criarTemplate(url, token, tokenAuth, nomeTemplate, text, numberFrom, emailNotification, mensageOut);
    }

}
