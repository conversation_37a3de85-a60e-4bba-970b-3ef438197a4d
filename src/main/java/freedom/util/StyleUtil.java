package freedom.util;

import freedom.client.controls.IInputComponent;
import freedom.client.util.CSSUtil;
import org.zkoss.zk.ui.HtmlBasedComponent;

public class StyleUtil {

    public static void setCSSColorStyle(IInputComponent component, String color) {
        HtmlBasedComponent c = ((HtmlBasedComponent) component.getImpl());
        c.setStyle(CSSUtil.add(c.getStyle(), "color: " + color + " !important"));
    }

    public static void setCSSFonteSizeStyle(IInputComponent component, Integer size) {
        HtmlBasedComponent c = ((HtmlBasedComponent) component.getImpl());
        c.setStyle(CSSUtil.add(c.getStyle(), "font-size: " + size + "px !important"));
    }

}
