package freedom.util;

public class Constantes {

    public static final int ICON_PARTS_APROVACAO_SIZE = 14;
    public static final String A_DESCRICAO = "A.DESCRICAO";
    public static final String ACAO_ALTERAR = "A";
    public static final String ACAO_CADASTRO_RAPIDO = "R";
    public static final String ACAO_EXCLUIR = "E";
    public static final String ACAO_INCLUIR = "I";
    public static final String ADVERTENCIAS_PENDENTES = "{advertencias-pendentes}";
    public static final String AFTER_CENTER = "after_center";
    public static final String APARAMETROS = "APARAMETROS";
    public static final String ATACADO = "Atacado";
    public static final String ATENCAO = "Aten\u00e7\u00e3o";
    public static final String B_9_BABB_IMPORTANT = "b9babb !important";
    public static final String BEFORE_CENTER = "before_center";
    public static final String BOLETO = "boleto";
    public static final String BOTTOM = "bottom";
    public static final String CHECKED_S = "CHECKED = 'S'";
    public static final String CLASSIFICAO_PECAS =  "N,P";
    public static final String CL_BLACK = "clBlack";
    public static final String CL_BLUE = "clBlue";
    public static final String CL_GRAY = "clGray";
    public static final String CL_GREEN = "clGreen";
    public static final String CL_ORANGE = "FF8C00";
    public static final String CL_RED = "clRed";
    public static final String CL_SILVER = "clSilver";
    public static final String CLICKED = "clicked";
    public static final String COD_BARRAS = "COD_BARRAS";
    public static final String COD_CLASSE_CONTABIL = "COD_CLASSE_CONTABIL";
    public static final String COD_CLIENTE = "COD_CLIENTE";
    public static final String COD_CONTROLE = "COD_CONTROLE";
    public static final String COD_CURVA = "COD_CURVA";
    public static final String COD_EMP_SELECIONADO = "COD_EMP_SELECIONADO";
    public static final String COD_EMP_USER_LOGADO = "COD_EMP_USER_LOGADO";
    public static final String COD_EMPRESA = "COD_EMPRESA";
    public static final String COD_EMPRESA_2 = "codEmpresa";
    public static final String COD_EMPRESA_DEPARTAMENTO = "COD_EMPRESA_DEPARTAMENTO";
    public static final String COD_EMPRESA_LOGADO = "COD_EMPRESA_LOGADO";
    public static final String COD_EMPRESA_USUARIO = "COD_EMPRESA_USUARIO";
    public static final String COD_EVENTO_DIF = "COD_EVENTO_DIF";
    public static final String COD_FORMA_PGTO = "COD_FORMA_PGTO";
    public static final String COD_FORNECEDOR = "COD_FORNECEDOR";
    public static final String COD_FUNDO_LARANJA = "FFDAB9";
    public static final String COD_GRUPO = "COD_GRUPO";
    public static final String COD_GRUPO_INTERNO = "COD_GRUPO_INTERNO";
    public static final String COD_GRUPO_INTERNO_IN = "COD_GRUPO_INTERNO_IN";
    public static final String COD_ITEM = "COD_ITEM";
    public static final String COD_ITEM_COD_FORNECEDOR = "COD_ITEM,COD_FORNECEDOR";
    public static final String COD_MAX_DESC = "COD_MAX_DESC";
    public static final String COD_MODELO = "COD_MODELO";
    public static final String COD_ORC_MAPA = "COD_ORC_MAPA";
    public static final String COD_PRODUTO = "COD_PRODUTO";
    public static final String COD_STATUS = "COD_STATUS";
    public static final String COD_SUB_GRUPO_INTERNO = "COD_SUB_GRUPO_INTERNO";
    public static final String COD_SUB_GRUPO_INTERNO_IN = "COD_SUB_GRUPO_INTERNO_IN";
    public static final String COD_TIPO_ENDERECO = "COD_TIPO_ENDERECO";
    public static final String COD_TIPO_FATURA = "COD_TIPO_FATURA";
    public static final String COLOR_BLACK = "clBlack";
    public static final String COLOR_HASHTAG7CB5EC = "#7cb5ec";
    public static final String COLOR_MAROON = "clMaroon";
    public static final String COLOR_NAVY = "clNavy";
    public static final String COLOR_PARTS_APPROVED = "C7E0C7";
    public static final String COLOR_PARTS_DIVERGENT = "FBCFD0";
    public static final String COLOR_PARTS_WARNING = "FFFFAA";
    public static final String COLOR_RED = "clRed";
    public static final String COMERCIAL = "Comercial";
    public static final String COMPRAS_BLOQUEADAS = "COMPRAS_BLOQUEADAS";
    public static final String CONFIRMACAO = "Confirma\u00e7\u00e3o";
    public static final String CONTROLE = "CONTROLE";
    public static final String CONTROLE_RESERVA = "CONTROLE_RESERVA";
    public static final String COR_FUNDO_VERDE = "66CDAA";
    public static final String COR_FUNDO_VERMELHO = "FFA07A";
    public static final String CRMGOLD = "crmgold";
    public static final String CRMPARTS_BOLETO_JASPER = "/crmparts/boleto.jasper";
    public static final String CRMPARTS_ORDER_BY_FORMAS_PGTO = "DECODE(TIPO_PGTO, 'V', 1, 'Z', 2, 'A', 3, 'P', 4, 'M', '5', 'T', '6', 7), DESCRICAO";
    public static final String CRUZAR = "C";
    public static final String CURVA_ABC = "CURVA_ABC";
    public static final String CUSTOMER_PHONE_NUMBER = "customer_phone_number";
    public static final String DATA_EMISSAO = "DATA_EMISSAO";
    public static final String DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss";
    public static final String DESCONTO_PERCENTUAL = "DESCONTO_PERCENTUAL";
    public static final String DESCRICAO = "DESCRICAO";
    public static final String DESCRUZAR = "D";
    public static final String DESEJA_REALMENTE_APLICAR = "Deseja realmente aplicar?";
    public static final String DESEJA_REALMENTE_EXCLUIR = "Deseja realmente excluir?";
    public static final String DESEJA_REALMENTE_LIMPAR_OS_FILTROS = "Deseja realmente limpar os filtros?";
    public static final String DEVE_SER_PREENCHIDO = "\" deve ser preenchido.";
    public static final String DEVE_SER_PREENCHIDO_COM_VALOR_MAIOR_QUE_0_ZERO = "\" deve ser preenchido com valor maior que \"0\" (zero).";
    public static final String DIVERGENCIAS_PENDENTES = "{divergencias-pendentes}";
    public static final String EDEDED = "#EDEDED";
    public static final String EEE9E9 = "#EEE9E9";
    public static final String EH_DRT = "EH_DRT";
    public static final String EM_PROMOCAO = "EM_PROMOCAO";
    public static final String EMPRESA_DESTINO = "Empresa destino: ";
    public static final String EMPRESA_ORIGEM = "Empresa origem: ";
    public static final String END_CENTER = "end_center";
    public static final String ESSE_ITEM_ESTA_EM_PROMOCAO = "Esse item est\u00e1 em Promo\u00e7\u00e3o";
    public static final String ET_EXPRESSION = "etExpression";
    public static final String FALHA_AO_DEFINIR_FORMA_DE_COBRANCA = "Falha ao definir forma de cobran\u00e7a";
    public static final String FALHA_AO_FILTRAR = "Falha ao filtrar";
    public static final String FAMILA_MODELO = "FAMILA_MODELO";
    public static final String FFFFFF = "#FFFFFF";
    public static final String FILE_TEXT_O = "file-text-o";
    public static final String FILTRO = "FILTRO";
    public static final String FLEX_FALSE = "ftFalse";
    public static final String FLEX_TRUE = "ftTrue";
    public static final String FONT_NAME_TAHOMA = "Tahoma";
    public static final String FRM_VENDA = "FrmVenda";
    public static final String FS_BOLD = "[fsBold]";
    public static final String FS_BOLD_FS_UNDERLINE = "[fsBold,fsUnderline]";
    public static final String FS_REGULAR = "[fsRegular]";
    public static final String FT_FALSE = "ftFalse";
    public static final String FT_TRUE = "ftTrue";
    public static final String GRAVADO_COM_SUCESSO = "Gravado com sucesso.";
    public static final String HBBUTTONLEFTRADIUS = "hbbuttonleftradius";
    public static final String HBBUTTONRIGHTRADIUS = "hbbuttonrightradius";
    public static final String HBOXBASECOMP = "hboxbasecomp";
    public static final String ICON_PARTS_APROVAR = "check";
    public static final String ICON_PARTS_APROVAR_COLOR = "267D39";
    public static final String ICON_PARTS_DESAPROVAR = "reply";
    public static final String ID_COMPRA = "ID_COMPRA";
    public static final String ID_CP_PROMOCAO = "ID_CP_PROMOCAO";
    public static final String ID_CTE_COMPRA = "ID_CTE_COMPRA";
    public static final String ID_ENTRADA = "ID_ENTRADA";
    public static final String ID_GRUPO = "ID_GRUPO";
    public static final String ID_MARKUP_TIPO = "ID_MARKUP_TIPO";
    public static final String ID_PENDENCIA = "ID_PENDENCIA";
    public static final String ID_SITUACAO_ESPECIAL = "ID_SITUACAO_ESPECIAL";
    public static final String INFORMACAO = "Informa\u00e7\u00e3o";
    public static final String INSTRUCOES = "INSTRUCOES";
    public static final String ITENS_DESCRICAO = "ITENS_DESCRICAO";
    public static final String ITENS_FORNEC_COMPRA_BLOQUEADA = "ITENS_FORNEC_COMPRA_BLOQUEADA";
    public static final String LETRA_DESCONTO = "LETRA_DESCONTO";
    public static final String LIBERADAS =  "{todas-liberadas}";
    public static final String MAPA_LOCACAO = "mapaLocacao";
    public static final String MIDDLE_CENTER = "middle_center";
    public static final String MINI_2 = "MINI2";
    public static final String NA_GUIA = "Na guia \"";
    public static final String NAO_DEFINIDO = "NAO_DEFINIDO";
    public static final String NENHUM = "Nenhum";
    public static final String NOVO_PRECO = "NOVO_PRECO";
    public static final String NR_FATURA = "NR_FATURA";
    public static final String NUNCA_COMPROU = "NUNCA_COMPROU";
    public static final String O_CAMPO = "O campo \"";
    public static final String O_DEPARTAMENTO = "O departamento \"";
    public static final String O_ITEM = "O item \"";
    public static final String OFICINA = "Oficina";
    public static final String ON_CHECK = "onCheck";
    public static final String ON_CLICK = "onClick";
    public static final String ON_CLOSE = "onClose";
    public static final String ON_DOUBLE_CLICK = "onDoubleClick";
    public static final String ON_EDIT_CHANGE = "OnEditChange";
    public static final String ON_EDIT_CHANGE2 = "onEditChange";
    public static final String OPT_OUT = "Opt-Out";
    public static final String ORA_04068 = "ORA-04068";
    public static final String PARAMETRO_COD_TIPO_FATURA_VALOR = "Par\u00e2metro \"COD_TIPO_FATURA\" Valor \"";
    public static final String PARAMETRO_DATA_EMISSAO_VALOR = "Par\u00e2metro \"DATA_EMISSAO\" Valor \"";
    public static final String PARAMETRO_NR_FATURA_VALOR = "Par\u00e2metro \"NR_FATURA\" Valor \"";
    public static final String PARM_SYS = "PARM_SYS";
    public static final String PARM_SYS_2 = "PARM_SYS2";
    public static final String PARM_SYS_3 = "PARM_SYS3";
    public static final String PART_NUMBER = "PART_NUMBER";
    public static final String PASSO_3_FINALIZAR = "Passo 3: Finalizar {";
    public static final String PECAS = "Pecas";
    public static final String PERIODO_DIAS = "PERIODO_DIAS";
    public static final String PHONE_NUMBER = "phone_number";
    public static final String PROTECAO_LGPD_ESTE_CLIENTE_BLOQUEOU_O_ACESSO_AOS_SEUS_DADOS = "Prote\u00e7\u00e3o LGPD: Este cliente bloqueou o acesso aos seus dados!";
    public static final String RELATORIO_BOLETO = "Relat\u00f3rio \"boleto\"";
    public static final String RESIDENCIAL = "Residencial";
    public static final String RESPONSAVEL_PELO_EVENTO = "RESPONSAVEL_PELO_EVENTO";
    public static final String RIGHT = "right";
    public static final String SCLASS_BASE = "SCLASS_BASE";
    public static final String SEL = "SEL";
    public static final String SELECIONE_NO_MINIMO_1_REGISTRO = "Selecione, no m\u00ednimo, 1 registro.";
    public static final String SELECIONE_UM_CLIENTE = "Selecione um cliente.";
    public static final String SEQ_EVENTO = "SEQ_EVENTO";
    public static final String SEQ_FILA_IMPRESSAO = "SEQ_FILA_IMPRESSAO";
    public static final String SEQUENCIA = "SEQUENCIA";
    public static final String SERIE = "SERIE";
    public static final String SUCESS = "sucess";
    public static final String TB_BUSCA_VENDEDOR_VEIC_ACESSORIO = "tbBuscaVendedorVeicAcessorio";
    public static final String TB_FILA_IMPRESSAO = "tbFilaImpressao";
    public static final String TIPO_ENDERECO = "TIPO_ENDERECO";
    public static final String TIPO_FONE = "TIPO_FONE";
    public static final String TIPO_GRUPO = "TIPO_GRUPO";
    public static final String TODOS = "todos";
    public static final String TODOS_MAIUSCULO = "TODOS";
    public static final String TOT_ITEN = "TOT_ITEN";
    public static final String UF_PADRAO = "-zz";
    public static final String URL_COMUM = "nbs/whatsapp/plugin-zenvia/";
    public static final String USA_POS_SITEF = "USA_POS_SITEF";
    public static final String USUARIO = "USUARIO";
    public static final String USUARIO_LOGADO = "USUARIO_LOGADO";
    public static final String USUARIO_PAINEL_GRUPO = "USUARIO_PAINEL_GRUPO";
    public static final String VAREJO = "Varejo";
    public static final String VBOXBASECAIXA_VBOXCAIXACLIENTESESPECIAIS = "vboxbasecaixa vboxcaixaclientesespeciais";
    public static final String VBOXBASECOMP = "vboxbasecomp";
    public static final String VENDA = "venda";
    public static final String VENDA_A_PRAZO = "Venda a Prazo";
    public static final String VENDAS = "Vendas";
    public static final String VENDEDOR_NOME = "VENDEDOR_NOME";

    // Construtor privado para evitar instanciamento
    private Constantes() {
        throw new UnsupportedOperationException("Esta classe não pode ser instanciada.");
    }

}

