package freedom.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class JsonUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter DATE_FORMATTER_JSON = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public static double getValueAsDecimal(String value) {
        try {
            value = value.trim();
            if (value.isEmpty()) {
                return 0.0;
            }
            char decimalSeparator = value.lastIndexOf(',') > value.lastIndexOf('.') ? ',' : '.';
            char thousandSeparator = decimalSeparator == ',' ? '.' : ',';

            // Remove o separador de milhares
            String cleanValue = value.replace(String.valueOf(thousandSeparator), "");

            // Substitui o separador decimal por ponto, se necessário
            if (decimalSeparator == ',') {
                cleanValue = cleanValue.replace(',', '.');
            }
            return Double.parseDouble(cleanValue);
        } catch (NumberFormatException nx) {
            return 0.0;
        }
    }

    public static double getJsonValueAsDecimal(String json,
                                               String tag) {
        String value = getJsonValueAsString(json, tag);
        return getValueAsDecimal(value);
    }

    public static String getJsonValueAsString(String json,
                                              String tag) {
        if (json == null || tag == null) return "";

        String lowerJson = json.toLowerCase();
        String lowerTag = tag.toLowerCase();

        String key = "\"" + lowerTag + "\"";

        int tagIndex = lowerJson.indexOf(key);
        if (tagIndex == -1) {
            return "";
        }

        int valueStart;
        int valueEnd;
        int delimiterPos;
        boolean isString;

        valueStart = json.indexOf(':', tagIndex) + 1;
        if (valueStart <= tagIndex) {
            return "";
        }

        // Verificar se o valor é uma string (começa com aspas)
        isString = json.charAt(valueStart) == '"';
        if (isString) {
            // Se for string, encontrar o fim da string
            valueEnd = json.indexOf('"', valueStart + 1);
            return json.substring(valueStart + 1, valueEnd);
        } else {
            // Se não for string, procurar o próximo delimitador (vírgula ou fechamento)
            delimiterPos = json.indexOf(',', valueStart);
            if (delimiterPos == -1) {
                valueEnd = json.indexOf('}', valueStart) - 1;
            } else {
                valueEnd = delimiterPos - 1;
            }
            return json.substring(valueStart, valueEnd + 1).trim().replace("\r", "").replace("\n", "");
        }
    }

    public static boolean getJsonValueAsBoolean(String json,
                                                String tag) {
        String value = getJsonValueAsString(json,
                tag);
        return (value != null && value.equalsIgnoreCase("S"));
    }

    public static String convertClassToJson(Object classObject) {
        try {
            return objectMapper.writeValueAsString(classObject);
        } catch (JsonProcessingException e) {
            return null;
        }
    }

    @SuppressWarnings("unused")
    public static Integer getJsonValueInteger(String jsonString, String param) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonString);
            JsonNode valueNode = jsonNode.get(param);
            if (valueNode != null && valueNode.isInt()) {
                return valueNode.intValue();
            }
            return null;
        } catch (JsonProcessingException e) {
            return 0;
        }
    }

    private static String escapeJson(String value) {
        return value
                .replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    public static void addJsonValue(StringBuilder json, String key, Object value) {
        if (json == null) {
            throw new IllegalArgumentException("StringBuilder json não pode ser null");
        }
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Key não pode ser null ou vazia");
        }

        if (json.length() > 1 && json.charAt(json.length() - 1) != '{') {
            json.append(",");
        }

        if (value == null) {
            json.append(String.format("\"%s\": null", escapeJson(key)));
        } else if (value instanceof String) {
            json.append(String.format("\"%s\": \"%s\"", escapeJson(key), escapeJson((String) value)));
        } else if (value instanceof Number) {
            json.append(String.format("\"%s\": %s", escapeJson(key), value));
        } else if (value instanceof Boolean) {
            json.append(String.format("\"%s\": %s", escapeJson(key), value.toString().toLowerCase()));
        } else if (value instanceof LocalDate) {
            json.append(String.format("\"%s\": \"%s\"", escapeJson(key), ((LocalDate) value).format(DATE_FORMATTER_JSON)));
        } else if (value instanceof LocalDateTime) {
            json.append(String.format("\"%s\": \"%s\"", escapeJson(key), ((LocalDateTime) value).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)));
        } else if (value instanceof Date) {
            LocalDate localDate = ((Date) value).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            json.append(String.format("\"%s\": \"%s\"", escapeJson(key), localDate.format(DATE_FORMATTER_JSON)));
        } else {
            json.append(String.format("\"%s\": %s", escapeJson(key), convertClassToJson(value)));
        }
    }

}
