/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

/**
 *
 * <AUTHOR>
 */
public class MensagemValidacao {

    private String campo;
    private String mensagem;

    public MensagemValidacao() {
        this.campo = "Ok";
        this.mensagem = "Ok";
    }

    public void Edit(String campo, String mensagem) {
        this.campo = campo;
        this.mensagem = mensagem;
    }

    public String getCampo() {
        return campo;
    }

    public void setCampo(String campo) {
        this.campo = campo;
    }

    public String getMensagem() {
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    @Override
    public String toString() {
        return mensagem;
    }

    public Boolean isOk() {
        return (this.campo.contains("Ok") || this.campo.isEmpty());
    }

    public Boolean validarAliquotaMaiorZero(Double value, String t) {
        if (value <= 0) {
            mensagem = t + " não pode ser menor ou igual a zero.";
            return false;
        } else if (value > 100) {
            mensagem = t + " não pode ser maior que 100.";
            return false;
        } else {
            return true;
        }
    }

    public Boolean validarAliquotaZero(Double value, String t) {
        if (value < 0) {
            mensagem = t + " não pode ser menor que zero.";
            return false;
        } else if (value > 100) {
            mensagem = t + " não pode ser maior que 100.";
            return false;
        } else {
            return true;
        }
    }

    public Boolean valorMaiorQueZero(Double value, String t) {
        if (value < 0) {
            mensagem = t + " não pode ser menor que zero.";
            return false;
        } else {
            return true;
        }

    }

}
