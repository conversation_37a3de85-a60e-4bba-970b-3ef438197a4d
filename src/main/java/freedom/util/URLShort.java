/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;

/**
 *
 * <AUTHOR>
 */
public class URLShort {

    private static final String TINYURL_STRING = "https://tinyurl.com/api-create.php?url=";

    public String shorter(String url) throws Exception {
        String tinyUrlLookup = TINYURL_STRING + url;
        BufferedReader reader = new BufferedReader(new InputStreamReader(new URL(tinyUrlLookup).openStream()));
        String xTinyUrl = reader.readLine();
        return xTinyUrl;
    }
}
