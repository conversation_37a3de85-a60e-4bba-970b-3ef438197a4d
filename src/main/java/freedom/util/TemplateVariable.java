/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.data.Value;

/**
 *
 * <AUTHOR>
 */
public class TemplateVariable {

    private String varibleName;
    private final Value value;

    public TemplateVariable(String varibleName, Value value) {
        this.varibleName = varibleName;
        this.value = value;
    }

    public String getVaribleName() {
        return varibleName;
    }

    public Value getValue() {
        return value;
    }

    public void setVaribleName(String varibleName) {
        this.varibleName = varibleName;
    }

}
