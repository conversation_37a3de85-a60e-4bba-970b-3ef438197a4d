/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.data.Value;
import java.io.ByteArrayOutputStream;
import java.util.List;
import org.json.JSONObject;

/**
 *
 * <AUTHOR>
 */
public interface MensagemWhatsApp {

    /**
     * Método utilizado para enviar mensagem avulsa, utilizar somente se existir
     * seção ativa.
     *
     * @param url Informe a url api ex.: http://meuip:porta
     * @param codEmpresa Código Empresa.
     * @param numberFrom Número origem mensage
     * @param numberTo Número destino mensagem
     * @param token Token conversa
     * @param mensagem Conteúdo da mensagem
     * @param tokenAuth Token de autenticação
     * @return Retorna um JSONObject
     * @throws Exception
     */
    public Object enviarMensagem(String url, int codEmpresa, String numberFrom,
            String numberTo, String token, String mensagem, String tokenAuth) throws Exception;

    public JSONObject postFile(String url, int codEmpresa, String fileName, ByteArrayOutputStream anexo,
            ETypeFileWhatsApp typeFile, String token, String tokenAuth) throws Exception;

    public Object enviarArquivo(String numberFrom, String numberTo, String url,
            int codEmpresa, String fileName, ByteArrayOutputStream anexo,
            ETypeFileWhatsApp typeFile, String token, String tokenAuth,
            JSONObject jsonPostFile) throws Exception;

    public String excluirTemplate(String url, String token, String tokenAuth, int id) throws Exception;

    public Object getUltimaMensagem(String url, String idMensagem, String tokenAuth) throws Exception;

    public Boolean getSessaoAtiva(String url, String numberFrom, String numberTo, String tokenAuth) throws Exception;

    public Object enviarMensagemTemplate(String url, int codEmpresa, String idTemplate,
            String numberFrom, String numberTo, List<TemplateVariable> variables, String token,
            String tokenAuth) throws Exception;

    public String ajustaFone(String fone, String TP);

    public String gerarHashChat(String from, String to);

    public List<TemplateVariable> getListTemplateVarValue(Value listVarValue, String mensagem);

    public String getTokenAut(String url, String usuario, String senha, Value mensageOut) throws Exception;

    public String criarTemplate(String url, String token, String tokenAuth, String nomeTemplate,
            String text, String numberFrom, String emailNotification, Value mensageOut) throws Exception;
}
