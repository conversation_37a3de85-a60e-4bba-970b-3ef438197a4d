package freedom.util;

import freedom.client.controls.IComponent;
import freedom.client.controls.impl.*;
import org.zkoss.zk.ui.AbstractComponent;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.HtmlBasedComponent;
import org.zkoss.zul.Button;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;

public class TestUtil {

    public static void setIdTest(
            IComponent componente
            , IComponent formulario
    ) {
        setIdTest(
                ((HtmlBasedComponent) componente.getImpl())
                , formulario
        );
    }

    public static void setIdTest(
            HtmlBasedComponent componente
            , IComponent formulario
    ) {
        componente.setClientAttribute(
                "idTest"
                , (componente.getId() + formulario.getName())
        );

    }

    public static void percorrerElemetnto(
            Component elemento
    ) {
        percorrerElemetnto(
                elemento
                , elemento.getId()
        );
    }

    public static void percorrerElemetnto(
            Component elemento
            , String nome
    ) {
        if (elemento instanceof Button) {
            elemento.setClientDataAttribute(
                    "idTest"
                    , (elemento.getId() + "_" + nome)
            );
            System.out.println(
                    "TFButton: " + elemento.getId()
            );
        }
        for (int i = elemento.getChildren().size() - 1; i >= 0; i--) {
            percorrerElemetnto(
                    elemento.getChildren()
                            .get(i)
                    , nome
            );
        }
    }

    private final static List<Class<? extends IComponent>> listaDeComponentesParaGerarIdTestDinamicamente = Arrays.asList(
            TFButton.class
            ,TFCheckBox.class
            ,TFCombo.class
            ,TFDate.class
            ,TFDecimal.class
            ,TFDecimalSpinner.class
            ,TFDualList.class
            ,TFGrid.class
            ,TFHBox.class
            ,TFIconClass.class
            ,TFImage.class
            ,TFInteger.class
            ,TFLabel.class
            ,TFMemo.class
            ,TFPageControl.class
            ,TFRadioButton.class
            ,TFRichEdit.class
            ,TFSpinner.class
            ,TFString.class
            ,TFTabsheet.class
            ,TFTime.class
            ,TFTreeGrid.class
            ,TFVBox.class
    );

    /**
     * Percorre os campos do formulário para definir um atributo
     * {@code idTest} em componentes ZK específicos, facilitando a
     * automação de testes.
     * <p>
     * O método inspeciona todos os campos declarados na classe do formulário e
     * em suas superclasses.
     * <p>
     * Se o campo for de um dos tipos listados em
     * {@code listaDeComponentesParaGerarIdTestDinamicamente}, um atributo
     * {@code idTest} será gerado e atribuído ao componente.
     * <p>
     * O ID é formado pela concatenação
     * do nome do formulário e do nome do componente (Ex: "FrmXXXBtnXXX").
     * <p>
     * Erros de acesso são registrados no console de erro
     * (System.err) e não interrompem a execução.
     *
     * @param formulario O formulário (instância de {@link TFForm}) cujos
     *                   componentes serão inspecionados e atualizados.
     *
     * <h3>Exemplo de Uso no Evento de Criação do Formulário:</h3>
     * <pre>{@code
     * public void FFormCreate(Event<Object> event) {
     *     try {
     *         TestUtil.setIdTestComponents(
     *             this
     *         );
     *     } catch (
     *         Exception exception
     *     ) {
     *         String mensagem = (
     *             "Erro ao definir idTest para os componentes do formulário \""
     *             + this.getClass().getSimpleName()
     *             + "\""
     *         );
     *         EmpresaUtil.showError(
     *             mensagem
     *            ,exception
     *         );
     *     }
     * }
     * }</pre>
     */
    public static void setIdTestComponents(
            TFForm formulario
    ) {
        Class<?> classeDoFormulario = formulario.getClass();
        while ((classeDoFormulario != null)
                && (classeDoFormulario != Object.class)) {
            Field[] vetorDeCamposDoFormulario = classeDoFormulario.getDeclaredFields();
            for (Field elementoDoVetorDeCamposDoFormulario : vetorDeCamposDoFormulario) {
                elementoDoVetorDeCamposDoFormulario.setAccessible(
                        true
                );
                try {
                    Object objetoDoFormulario = elementoDoVetorDeCamposDoFormulario.get(
                            formulario
                    );
                    if (objetoDoFormulario == null) {
                        continue;
                    }
                    boolean objetoDoFormularioEstaNalistaDeComponentesParaGerarIdTestAutomaticamente = TestUtil.listaDeComponentesParaGerarIdTestDinamicamente.contains(objetoDoFormulario.getClass());
                    if (objetoDoFormularioEstaNalistaDeComponentesParaGerarIdTestAutomaticamente) {
                        TestUtil.setIdTest(
                                formulario
                                ,objetoDoFormulario
                        );
                    }
                } catch (
                        IllegalAccessException illegalAccessException
                ) {
                    String mensagem = (
                            "Erro ao definir \"idTest\" para o campo \""
                                    + elementoDoVetorDeCamposDoFormulario.getName()
                                    + "\" do formulário \""
                                    + formulario.getName()
                                    + "\": \""
                                    + illegalAccessException.getMessage()
                                    + "\"."
                    );
                    System.err.println(
                            mensagem
                    );
                }
            }
            classeDoFormulario = classeDoFormulario.getSuperclass();
        }
    }

    private static void setIdTest(
            TFForm form
            ,Object value
    ) {
        IComponent comp = (IComponent) value;
        ((AbstractComponent) comp.getImpl()).setClientAttribute(
                "idTest"
                , (form.getName() + comp.getName())
        );
    }

}
