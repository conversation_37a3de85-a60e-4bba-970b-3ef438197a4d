/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import java.awt.Color;
import java.awt.image.BufferedImage;
import java.awt.image.PixelGrabber;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import javax.imageio.ImageIO;

/**
 *
 * <AUTHOR>
 */
public class ImgUtil {

    /**
     * Função verifica se a imagem é totalmente branca ou transparente, caso for
     * branca ou transparente retorna false se não true. Fonte:
     * https://stackoverflow.com/questions/31228443/check-if-an-image-is-all-white-or-fully-transparent-in-java
     *
     * @param imageBytes
     * @return
     * @throws IOException
     * @throws InterruptedException
     */
    public static boolean isImageValid(byte[] imageBytes) throws IOException, InterruptedException {
        if (imageBytes == null) {
            return false;
        }
        if (imageBytes.length == 0) {
            return false;
        }
        BufferedImage img = ImageIO.read(new ByteArrayInputStream(imageBytes));
        int w = img.getWidth(null);
        int h = img.getHeight(null);
        int[] pixels = new int[w * h];
        PixelGrabber pg = new PixelGrabber(img, 0, 0, w, h, pixels, 0, w);
        pg.grabPixels();
        boolean isValid = false;
        for (int pixel : pixels) {
            Color color = new Color(pixel);
            if (color.getAlpha() == 0 || color.getRGB() != Color.WHITE.getRGB()) {
                isValid = true;
                break;
            }
        }
        return isValid;
    }

}
