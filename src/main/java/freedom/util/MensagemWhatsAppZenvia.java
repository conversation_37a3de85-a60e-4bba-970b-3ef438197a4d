/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.data.Value;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.*;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MensagemWhatsAppZenvia implements MensagemWhatsApp {

    private final String monitorAtivo;

    public MensagemWhatsAppZenvia(
            String monitorAtivo
    ) {
        this.monitorAtivo = monitorAtivo;
    }

    @Override
    public Object enviarMensagemTemplate(String url, int codEmpresa, String idTemplate,
                                         String numberFrom, String numberTo, List<TemplateVariable> variables, String token,
                                         String tokenAuth) throws Exception {

        String urlTemplate = url.trim();

        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }

        urlTemplate = urlTemplate + Constantes.URL_COMUM + "mensagens/out/template";

        HttpResponse response = responseEnviarMsgTemplate(urlTemplate, codEmpresa, idTemplate,
                numberFrom, numberTo, variables, token, tokenAuth);

        InputStream instream = response.getEntity().getContent();
        String retorno = convertStreamToString(instream);

        return getObjectJson(retorno);
    }

    @Override
    public Object getUltimaMensagem(String url, String idMensagem, String tokenAuth) throws Exception {

        String urlTemplate = url.trim();

        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }

        urlTemplate = urlTemplate + Constantes.URL_COMUM + "mensagens/respostaMensagem";

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(urlTemplate + "?idApiMessage=" + idMensagem, tokenAuth);
        HttpResponse response = reqHttpUtil.methodGetZenvia();

        InputStream instream = response.getEntity().getContent();
        String retorno = convertStreamToString(instream);

        return getObjectJson(retorno);
    }

    private Object getObjectJson(String retorno) throws Exception {
        Object json;
        if (retorno != null && retorno.startsWith("[")) {
            json = new JSONArray(retorno);
        } else if (retorno != null && retorno.startsWith("{")) {
            json = new JSONObject(retorno);
        } else {
            throw new Exception(retorno);
        }
        return json;
    }

    private HttpResponse responseEnviarMsgTemplate(String url, int codEmpresa, String idTemplate,
                                                   String numberFrom, String numberTo, List<TemplateVariable> variables, String token,
                                                   String tokenAuth) throws Exception {

        String request;

        JSONObject jsonHeader = new JSONObject();
        jsonHeader.put(Constantes.COD_EMPRESA_2, codEmpresa);
        JSONObject fields = new JSONObject();

        if (variables != null) {
            JSONObject props = new JSONObject();
            for (TemplateVariable variable : variables) {
                props.put(variable.getVaribleName(), variable.getValue().asString());
            }
            fields.put("fields", props);
        }

        fields.put("idTemplate", idTemplate);

        jsonHeader.put("content", fields);
        jsonHeader.put(Constantes.CUSTOMER_PHONE_NUMBER, numberTo);
        jsonHeader.put(Constantes.PHONE_NUMBER, numberFrom);

        request = jsonHeader.toString();

        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    request
                    , this.getClass()
            );
        }

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(url, tokenAuth);
        return reqHttpUtil.methodPostZenvia(request, token);
    }

    private static String convertStreamToString(InputStream is) {
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        StringBuilder sb = new StringBuilder();
        String line;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line)
                        .append(System.lineSeparator());
            }
        } catch (IOException e) {
            System.out.println(e.getMessage());
        } finally {
            try {
                is.close();
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
        }
        return sb.toString();
    }

    @Override
    public String ajustaFone(String fone, String tp) {

        fone = fone.replace("-", "");

        if (fone != null && !fone.contains("+") && !fone.isEmpty()) {
            if (fone.length() <= 11 && fone.length() >= 10) {
                return "+55" + fone;
            }
            if (fone.length() <= 13 && fone.length() >= 12) {
                return "+" + fone;
            }
        }
        if (fone != null) {
            return "Fone " + (tp.equals("C") ? "do cliente" : "da loja")
                    + " {" + fone + "} não esta no formato esperado (DD)99999-9999 ou (+DI)(DD)99999-9999";
        } else {
            return "Fone " + (tp.equals("C") ? "do cliente" : "da loja")
                    + " não informado!";
        }
    }

    @Override
    public String gerarHashChat(String from, String to) {
        to = retornaNumeroComMais(to);
        from = retornaNumeroComMais(from);

        String baseDoHash = from.concat(":").concat(to);

        return org.apache.commons.codec.digest.DigestUtils.md5Hex(baseDoHash);
    }

    private String retornaNumeroComMais(String numero) {
        if (StringUtils.isNumeric(numero)) {
            return numero.contains("+") ? numero : "+".concat(numero);
        }

        return numero;
    }

    @Override
    public List<TemplateVariable> getListTemplateVarValue(Value listVarValue, String mensagem) {
        List<TemplateVariable> list = new ArrayList<>();

        String[] st = listVarValue.asString().split("<#13>");

        String nextValue;
        String param;
        String valor;
        String paramCheck;
        for (String st1 : st) {
            nextValue = st1;

            if (!nextValue.trim().isEmpty()) {

                param = nextValue.substring(1, nextValue.indexOf("]"));
                if (!nextValue.contains("{{")) {
                    valor = null;
                } else {
                    valor = nextValue.substring(nextValue.indexOf("{{") + 2).replace("}}", "");
                }

                paramCheck = "[" + param.trim().toUpperCase() + "]";

                if (mensagem.toUpperCase().contains(paramCheck)) {
                    list.add(new TemplateVariable(param, new Value(valor)));
                }
            }
        }

        return list;
    }

    private HttpResponse responseSessaoAtiva(String url, String tokenAuth) throws Exception {
        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(url, tokenAuth);
        return reqHttpUtil.methodGetZenvia();
    }

    @Override
    public Boolean getSessaoAtiva(
            String url
            , String numberFrom
            , String numberTo
            , String tokenAuth
    ) throws Exception {
        String mensagem = (
                "MensagemWhatsAppZEnvia.getSessaoAtiva"
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "url \""
                        + url
                        + "\""
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "numberFrom \""
                        + numberFrom
                        + "\""
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "numberTo \""
                        + numberTo
                        + "\""
                        + System.lineSeparator()
                        + System.lineSeparator()
                        + "tokenAuth isEmpty \""
                        + tokenAuth.isEmpty()
                        + "\""
        );
        FRLogger.log(
                mensagem
                , this.getClass()
        );
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    "Inicio: getSessaoAtiva"
                    , this.getClass()
            );
        }
        String urlTemplate = url.trim();
        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    "getSessaoAtiva -> Antes urlTemplate: " + urlTemplate
                    , this.getClass()
            );
        }
        urlTemplate = (
                urlTemplate
                        + Constantes.URL_COMUM
                        + "mensagens/sessaoAtiva?from=%2B"
                        + numberFrom.replace(
                        "+"
                        , ""
                )
                        + "&to=%2B"
                        + numberTo.replace(
                        "+"
                        , ""
                )
        );
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    (
                            "getSessaoAtiva -> Depois urlTemplate: "
                                    + urlTemplate
                    )
                    , this.getClass()
            );
        }
        HttpResponse response = responseSessaoAtiva(
                urlTemplate
                , tokenAuth
        );
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    (
                            "getSessaoAtiva -> response"
                                    + response.toString()
                    )
                    , this.getClass()
            );
        }
        if (response.getStatusLine().getStatusCode() != 200) {
            throw new Exception(response.getStatusLine().getStatusCode() + " - "
                    + response.getStatusLine().getReasonPhrase()
                    + " (Erro crítico ao consultar sessão ativa).");
        }
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    "getSessaoAtiva -> strResponse"
                    , this.getClass()
            );
        }
        String strResponse = EntityUtils.toString(response.getEntity());
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    (
                            "getSessaoAtiva -> strResponse "
                                    + strResponse
                    )
                    , this.getClass()
            );
        }
        JSONObject obj = new JSONObject(strResponse);
        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    (
                            "getSessaoAtiva -> JSONObject "
                                    + obj
                    )
                    , this.getClass()
            );
        }
        return obj.getJSONObject("data").getBoolean("sessaoAtiva");
    }

    private HttpResponse responseEnviarMensagem(String url, int codEmpresa,
                                                String numberFrom, String numberTo, String token, String mensagem,
                                                String tokenAuth) throws Exception {

        String request;

        JSONObject jsonBody = new JSONObject();
        jsonBody.put(Constantes.COD_EMPRESA_2, codEmpresa);
        JSONObject fields = new JSONObject();
        fields.put("text", mensagem);
        fields.put("type", "text");
        jsonBody.put("content", fields);
        jsonBody.put(Constantes.CUSTOMER_PHONE_NUMBER, numberTo);
        jsonBody.put(Constantes.PHONE_NUMBER, numberFrom);
        jsonBody.put("sistema", "service");

        request = jsonBody.toString();

        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    (
                            "responseEnviarMensagem: "
                                    + request
                    )
                    , this.getClass()
            );
        }

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(url, tokenAuth);
        return reqHttpUtil.methodPostZenvia(request, token);
    }

    @Override
    public Object enviarMensagem(String url, int codEmpresa, String numberFrom,
                                 String numberTo, String token, String mensagem, String tokenAuth) throws Exception {
        String urlTemplate = url.trim();

        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }

        urlTemplate = urlTemplate + Constantes.URL_COMUM + "mensagens/out/text";

        if (this.monitorAtivo.equals("S")) {
            FRLogger.log(
                    (
                            "enviarMensagem: URL: "
                                    + urlTemplate
                    )
                    , this.getClass()
            );
        }

        HttpResponse response = responseEnviarMensagem(urlTemplate, codEmpresa,
                numberFrom, numberTo, token, mensagem, tokenAuth);

        InputStream stream = response.getEntity().getContent();
        String retorno = convertStreamToString(stream);

        return getObjectJson(retorno);
    }

    @Override
    public String getTokenAut(String url, String usuario, String senha, Value mensageOut) throws Exception {
        String urlTemplate = url.trim();

        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }

        String urlTokenComum = "token";
        urlTemplate = urlTemplate + urlTokenComum + "?usuario=" + usuario + "&senha=" + senha + "&idioma=PT&pacote=LEADZAP";

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(urlTemplate, "");
        HttpResponse response = reqHttpUtil.methodPostZenvia("", "");

        InputStream instream = response.getEntity().getContent();
        String retorno = convertStreamToString(instream);

        JSONObject json = (JSONObject) getObjectJson(retorno);

        String token = "";

        if (json.has("sucesso") && json.getBoolean("sucesso")) {
            token = json.getJSONObject("data").getString("token");
        } else {
            mensageOut.setValue(json.toString());
        }

        return token;
    }

    @Override
    public String criarTemplate(String url, String token, String tokenAuth, String nomeTemplate,
                                String text, String numberFrom, String emailNotification, Value mensageOut) throws Exception {

        String urlTemplate = url.trim();

        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }

        urlTemplate = urlTemplate + Constantes.URL_COMUM + "template/criar";

        String textSend = text.replace("[", "{{").replace("]", "}}");

        JSONObject jsonBody = new JSONObject();
        JSONObject bodyDetail = new JSONObject();
        bodyDetail.put("text", textSend);
        bodyDetail.put("type", "TEXT_TEMPLATE");
        jsonBody.put("body", bodyDetail);
        jsonBody.put("category", "MARKETING");
        jsonBody.put("name", nomeTemplate);
        jsonBody.put("notificationEmail", emailNotification);
        jsonBody.put("senderId", numberFrom);

        String request = jsonBody.toString();

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(urlTemplate, tokenAuth);
        HttpResponse response = reqHttpUtil.methodPostZenvia(request, token);

        InputStream instream = response.getEntity().getContent();
        String retorno = convertStreamToString(instream);

        JSONObject json = (JSONObject) getObjectJson(retorno);

        String idTemplate = "";

        if (json.has(Constantes.SUCESS) && json.getBoolean(Constantes.SUCESS)) {
            idTemplate = String.valueOf(json.getJSONObject("data").getInt("id"));
        } else {
            mensageOut.setValue(json.toString());
        }

        return idTemplate;
    }

    @Override
    public String excluirTemplate(String url, String token, String tokenAuth, int id) throws Exception {

        String result;

        String urlTemplate = url.trim();

        if (!StringUtils.reverse(urlTemplate.trim()).equals("/")) {
            urlTemplate = urlTemplate + "/";
        }

        urlTemplate = urlTemplate + Constantes.URL_COMUM + "template/excluir/" + id;

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(urlTemplate, tokenAuth);
        HttpResponse response = reqHttpUtil.methodDeleteZenvia(token);

        InputStream instream = response.getEntity().getContent();
        String retorno = convertStreamToString(instream);

        JSONObject json = (JSONObject) getObjectJson(retorno);

        if (json.has(Constantes.SUCESS) && json.getBoolean(Constantes.SUCESS)) {
            result = "OK";
        } else if (json.has(Constantes.SUCESS) && !json.getBoolean(Constantes.SUCESS)) {
            if (json.getString("message").contains("404")) {
                result = "OK";
            } else if (json.getString("message").contains("Template")) {
                result = "OK";
            } else {
                result = json.toString();
            }
        } else {
            result = json.toString();
        }

        return result;
    }

    @Override
    public JSONObject postFile(String url, int codEmpresa, String fileName, ByteArrayOutputStream anexo,
                               ETypeFileWhatsApp typeFile, String token, String tokenAuth) throws Exception {

        String fileBase64 = Base64.getEncoder().encodeToString(anexo.toByteArray());

        if (fileBase64 == null || fileBase64.isEmpty()) {
            return null;
        }

        String urlEnviarArquivo = url.trim();

        if (!StringUtils.reverse(urlEnviarArquivo.trim()).equals("/")) {
            urlEnviarArquivo = urlEnviarArquivo + "/";
        }

        urlEnviarArquivo = urlEnviarArquivo + Constantes.URL_COMUM + "files";

        JSONObject jsonRequest = new JSONObject();

        jsonRequest.put(Constantes.COD_EMPRESA_2, codEmpresa);
        jsonRequest.put("file", fileBase64);
        jsonRequest.put("fileMimeType", getMimeType(typeFile));
        jsonRequest.put("fileName", fileName);

        String request = jsonRequest.toString();

        RequisicaoHttpUtilZenvia reqHttpUtil = new RequisicaoHttpUtilZenvia(urlEnviarArquivo, tokenAuth);
        HttpResponse response = reqHttpUtil.methodPostZenvia(request, token);

        InputStream instream = response.getEntity().getContent();
        String retorno = convertStreamToString(instream);

        return (JSONObject) getObjectJson(retorno);
    }

    @Override
    public Object enviarArquivo(String numberFrom, String numberTo, String url,
                                int codEmpresa, String fileName, ByteArrayOutputStream anexo,
                                ETypeFileWhatsApp typeFile, String token, String tokenAuth,
                                JSONObject jsonPostFile) throws Exception {

        if (jsonPostFile == null) {

            jsonPostFile = postFile(url, codEmpresa, fileName, anexo, typeFile, token, tokenAuth);

            if (jsonPostFile == null) {
                return null;
            }
        }

        if (jsonPostFile.has(Constantes.SUCESS) && jsonPostFile.getBoolean(Constantes.SUCESS)) {

            String fileUrl = jsonPostFile.getJSONObject("data").getString("fileUrl");

            JSONObject jsonMsg = new JSONObject();

            jsonMsg.put(Constantes.COD_EMPRESA_2, codEmpresa);
            jsonMsg.put(Constantes.CUSTOMER_PHONE_NUMBER, numberTo);
            jsonMsg.put("fileMimeType", getMimeType(typeFile));
            jsonMsg.put("fileName", fileName);
            jsonMsg.put("fileUrl", fileUrl);
            jsonMsg.put(Constantes.PHONE_NUMBER, numberFrom);

            String urlEnviarArquivo1 = url.trim();

            if (!StringUtils.reverse(urlEnviarArquivo1.trim()).equals("/")) {
                urlEnviarArquivo1 = urlEnviarArquivo1 + "/";
            }

            String request = jsonMsg.toString();

            urlEnviarArquivo1 = urlEnviarArquivo1 + Constantes.URL_COMUM + "mensagens/out/file";

            RequisicaoHttpUtilZenvia reqHttp = new RequisicaoHttpUtilZenvia(urlEnviarArquivo1, tokenAuth);
            HttpResponse response = reqHttp.methodPostZenvia(request, token);

            InputStream stream = response.getEntity().getContent();
            String ret = convertStreamToString(stream);

            return this.getObjectJson(ret);

        }

        return jsonPostFile;
    }

    private String getMimeType(ETypeFileWhatsApp typeFile) {
        String fileMimeType;
        switch (typeFile) {
            case PDF:
                fileMimeType = "PDF";
                break;
            case JPEG:
            case JPG:
                fileMimeType = "JPEG";
                break;
            case PNG:
                fileMimeType = "PNG";
                break;
            default:
                fileMimeType = "";
                break;
        }
        return fileMimeType;
    }

}
