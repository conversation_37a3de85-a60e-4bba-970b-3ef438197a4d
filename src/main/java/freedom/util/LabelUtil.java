package freedom.util;

import freedom.client.controls.impl.TFLabel;
import freedom.client.util.CSSUtil;
import org.zkoss.zk.ui.HtmlBasedComponent;

public final class LabelUtil {

    private LabelUtil () {
        throw new UnsupportedOperationException(
                "Esta é uma classe utilitária e não pode ser instanciada."
        );
    }

    /**
     * Aplica estilos CSS a um {@link TFLabel} para forçar a quebra de linha do texto que exceder a largura do componente.
     *
     * @param label O componente {@code TFLabel} que terá a quebra de linha habilitada.
     *
     * <pre>{@code
     * @Override
     * public void FFormCreate(Event<Object> event) {
     *     LabelUtil.quebrarLinha(
     *         this.lblXXX
     *     );
     * }
     * }</pre>
     */
    public static void quebrarLinha(
            TFLabel label
    ) {
        HtmlBasedComponent htmlBasedComponent = (HtmlBasedComponent) (label.getImpl());
        htmlBasedComponent.setStyle(
                CSSUtil.remove(
                        htmlBasedComponent.getStyle()
                        , "white-space"
                )
        );
        htmlBasedComponent.setStyle(
                CSSUtil.add(
                        htmlBasedComponent.getStyle()
                        , "display:block;word-break:break-word;padding:5px"
                )
        );
    }

}
