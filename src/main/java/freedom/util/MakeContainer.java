/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.client.controls.impl.*;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.impl.Row;
import org.zkoss.zk.ui.Component;
import org.zkoss.zk.ui.event.Events;
import org.zkoss.zul.*;

/**
 * <AUTHOR>
 */
public class MakeContainer {

    private static final IWorkList wl = WorkListFactory.getInstance();
    private boolean isReponsive = false;

    public MakeContainer() {
        isReponsive = !wl.get("MOBILE_RESPONSIVE").isNull();
    }

    public interface CallBackEventMaker {

        void salvar(String obs, int bookmark);

        void cancelar(int bookmark);

        void excluir(int bookmark);

        void clickMemo();
    }

    public void clearContainer(TFVBox container) {
        for (int i = container.getChildren().size() - 1; i >= 0; i--) {
            container.getChildren().get(i).detach();
        }
    }

    private void habilitarIcoExand(TFMemo tf, TFIconClass icoExpand) {
        String[] lines1 = tf.getValue().asString().split("[\n]");
        if (lines1.length > 4) {
            icoExpand.setVisible(true);
        }
    }

    private int getHeigthMemo(String tf) {
        String[] lines = tf.split("[\n]");
        int heigthl = 150;

        if (!isReponsive) {
            heigthl = 120;
        }

        int totalLines = lines.length;

        if (totalLines == 1) {
            int totalChar = tf.length();
            int maxTotalCharLine = 70;

            if (!isReponsive) {
                maxTotalCharLine = 140;
            }

            if (totalChar > maxTotalCharLine) {
                totalLines = (int) Math.floor(totalChar / maxTotalCharLine);

                switch (totalLines) {
                    case 1:
                        if ((totalChar > maxTotalCharLine) && (totalChar < (maxTotalCharLine * 2))) {
                            totalLines = 2;
                        }
                        break;
                    case 2:
                        if ((totalChar > (maxTotalCharLine * 2)) && (totalChar < (maxTotalCharLine * 3))) {
                            totalLines = 3;
                        }
                        break;
                    case 3:
                        if ((totalChar > (maxTotalCharLine * 3)) && (totalChar < (maxTotalCharLine * 4))) {
                            totalLines = 4;
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        switch (totalLines) {
            case 1:
                heigthl = 70;
                break;
            case 2:
                heigthl = 90;
                break;
            case 3:
                heigthl = 110;
                if (!isReponsive) {
                    heigthl = 100;
                }
                break;
            case 4:
                heigthl = 120;
                break;
            case 5:
                heigthl = 150;
                if (!isReponsive) {
                    heigthl = 140;
                }
                break;
            default:
                break;
        }
        return heigthl;
    }

    private void disableMemo(TFVBox containerPai, Hlayout infoCntBotao, TFMemo memo, Div infoCnt,
                             int heigth, TFHBox row, TFIconClass icoOptions, TFIconClass icoExpand) {
        infoCntBotao.setVisible(false);
        ((Textbox) memo.getImpl()).setInplace(false);

        infoCnt.setHeight(heigth + "px");
        row.setHeight(heigth);
        row.invalidate();
        memo.setReadOnly(true);
        icoOptions.setVisible(true);
        habilitarIcoExand(memo, icoExpand);

        infoCntBotao.invalidate();
        containerPai.invalidate();
    }

    private void expandMemo(TFVBox containerPai, TFIconClass icoExpand, Div infoCnt, int heigth, TFHBox row, TFMemo memo) {
        boolean restore = !Boolean.parseBoolean(icoExpand.getAttribute("clicked").toString());
        icoExpand.setAttribute("clicked", restore);

        if (restore) {
            infoCnt.setHeight(heigth + "px");
            row.setHeight(heigth);
            icoExpand.setIconClass("sort");
            row.invalidate();
            containerPai.invalidate();
        } else {
            String[] lines = memo.getValue().asString().split("[\n]");
            if (lines.length > 4) {
                int tt = heigth;
                for (int x = 0; x < lines.length - 4; x++) {
                    tt += 15;
                }
                infoCnt.setHeight(tt + "px");
                row.setHeight(tt);
                icoExpand.setIconClass("compress");
                row.invalidate();
            } else {
                infoCnt.setHeight(heigth + "px");
                row.setHeight(heigth);
                icoExpand.setIconClass("sort");
                row.invalidate();
            }
            containerPai.invalidate();
        }
    }

    public void makeRowContainer(TFVBox containerPai, TFVBox container, int bookmark,
                                 String valueObs, String captionRow, TFForm formMain, int paddingTopImageTimeLine,
                                 boolean showOptionsPopup, boolean clickEditContainter, CallBackEventMaker callBackEvent,
                                 boolean inEditChange, boolean buttonTop, boolean visibleImageLeft) throws DataException {

        container.setSpacing(5);
        int heigth = getHeigthMemo(valueObs);

        //Button
        Hlayout infoCntBotao = new Hlayout();
        //Button top
        if (buttonTop) {
            infoCntBotao.setParent(((Vlayout) container.getImpl()));
        }

        //Linha
        TFHBox row = new TFHBox();
        row.setFlexHflex("ftTrue");
        row.setHeight(heigth);
//        row.setSpacing(5);
        ((Hlayout) row.getImpl()).setParent(((Vlayout) container.getImpl()));

        // BEGIN IMG_COL
        if (visibleImageLeft) {
            TFVBox imgCol = new TFVBox();
            imgCol.setFlexVflex("ftTrue");
            imgCol.setWidth(60);
            ((Vlayout) imgCol.getImpl()).setParent(((Hlayout) row.getImpl()));

            TFHBox spcMiddle = new TFHBox();
            spcMiddle.setFlexVflex("ftMin");
            spcMiddle.setFlexHflex("ftTrue");
            ((Hlayout) spcMiddle.getImpl()).setParent(((Vlayout) imgCol.getImpl()));

            Div divTimeLine = new Div();
            divTimeLine.setVflex("true");
//        divTimeLine.setHflex("true");
            divTimeLine.setParent(((Vlayout) imgCol.getImpl()));
            divTimeLine.setSclass("timelineBarPontilhado");

            TFHBox spcLeft = new TFHBox();
            spcLeft.setFlexHflex("ftTrue");
            ((Hlayout) spcLeft.getImpl()).setParent(((Hlayout) spcMiddle.getImpl()));

            TFHBox spcCenter = new TFHBox();
            ((Hlayout) spcCenter.getImpl()).setStyle("display:table-cell;vertical-align:middle;text-align:center;padding-top: "
                    + paddingTopImageTimeLine + "px;margin:0;border:1px solid #e5e5e5;border-radius:50%;background-color:#faf9fa;");
            spcCenter.setHeight(49);
            spcCenter.setWidth(49);
            ((Hlayout) spcCenter.getImpl()).setParent(((Hlayout) spcMiddle.getImpl()));

            TFIconClass img = new TFIconClass();
            img.setIconClass("file-text-o");
            img.setSize(25);
            img.setColor("b9babb !important");
            ((Component) img.getImpl()).setParent(((Hlayout) spcCenter.getImpl()));

            TFHBox spcRight = new TFHBox();
            spcRight.setFlexHflex("true");
            ((Hlayout) spcRight.getImpl()).setParent(((Hlayout) spcMiddle.getImpl()));

            TFHBox spcBottom = new TFHBox();
            spcBottom.setFlexVflex("true");
            ((Hlayout) spcBottom.getImpl()).setParent(((Vlayout) imgCol.getImpl()));
        }
        // END IMG_COL

        TFVBox infoCol = new TFVBox();
        infoCol.setFlexVflex("ftTrue");
        ((Vlayout) infoCol.getImpl()).setHflex("9");
        ((Vlayout) infoCol.getImpl()).setParent(((Hlayout) row.getImpl()));

        Div infoParent = new Div();
        infoParent.setVflex("true");
        infoParent.setHflex("true");
        infoParent.setStyle("padding-left: 10px;");
        infoParent.setParent(((Vlayout) infoCol.getImpl()));

        Div infoCnt = new Div();
        infoCnt.setId("editor" + bookmark);
        infoCnt.setHeight(heigth + "px");
        infoCnt.setHflex("true");
        infoCnt.setSclass("speech-bubble");
        infoCnt.setParent(infoParent);
        infoCnt.setStyle("padding-left: 5px;padding-top: 5px; padding-right: 5px; padding-bottom: 5px;");

        Vlayout infoCnt0 = new Vlayout();
        infoCnt0.setVflex("true");
        infoCnt0.setHflex("true");
        infoCnt0.setParent(infoCnt);

        Hlayout infoCnt1 = new Hlayout();
        infoCnt1.setVflex("true");
        infoCnt1.setHflex("true");
        infoCnt1.setParent(infoCnt0);

        Vlayout infoCntMemoBotao = new Vlayout();
        infoCntMemoBotao.setHflex("true");
        infoCntMemoBotao.setVflex("true");
        infoCntMemoBotao.setParent(infoCnt1);

        Vlayout infoCntLabel = new Vlayout();
        infoCntLabel.setVflex("min");
        infoCntLabel.setHflex("true");
        infoCntLabel.setStyle("padding-left: 9px;");
        infoCntLabel.setParent(infoCntMemoBotao);

        TFLabel labelCap = new TFLabel();
        labelCap.setCaption(captionRow);
        labelCap.setFontColor("#B6B6B4");
        ((Label) labelCap.getImpl()).setParent(infoCntLabel);

        Vlayout infoCntMemo = new Vlayout();
        infoCntMemo.setHflex("true");
        infoCntMemo.setVflex("true");
        infoCntMemo.setParent(infoCntMemoBotao);

        if (!buttonTop) {
            infoCntBotao.setVflex("min");
            infoCntBotao.setHflex("true");
            infoCntBotao.setParent(infoCnt0);
        }

        infoCntBotao.setVisible(false);

        TFMemo tf = new TFMemo();
        tf.setAttribute(Row.ROW_BOOKMARK, bookmark);
        tf.setValue(valueObs);

        TFIconClass icoExpand = new TFIconClass();

        ((Textbox) tf.getImpl()).addEventListener(Events.ON_BLUR, (org.zkoss.zk.ui.event.Event event) -> {
            habilitarIcoExand(tf, icoExpand);
        });

        ((Textbox) tf.getImpl()).setStyle("overflow:hidden;background-color:inherit;border-color:#faf9fa;resize:none;");
        ((Textbox) tf.getImpl()).setInplace(true);
        tf.setFlexVflex("ftTrue");
        tf.setFlexHflex("ftTrue");
        ((Textbox) tf.getImpl()).setParent(infoCntMemo);
        tf.applyProperties();
        tf.setReadOnly(true);

        Vlayout infoCnt3 = new Vlayout();
        infoCnt3.setHflex("min");
        infoCnt3.setVflex("true");
        infoCnt3.setParent(infoCnt1);

        Hlayout ic1 = new Hlayout();
        ic1.setHeight("30px");
        ic1.setHflex("min");
        ic1.setParent(infoCnt3);

        icoExpand.setIconClass("sort");
        icoExpand.setSize(16);
        icoExpand.setColor("b9babb !important");
        icoExpand.setVisible(false);
        habilitarIcoExand(tf, icoExpand);

        ((Div) icoExpand.getImpl()).setStyle("padding-right: 15px; padding-top: 5px");
        ((Div) icoExpand.getImpl()).setAttribute("clicked", true);
        ((Div) icoExpand.getImpl()).addEventListener(Events.ON_CLICK, (org.zkoss.zk.ui.event.Event event) -> {
            expandMemo(containerPai, icoExpand, infoCnt, heigth, row, tf);
        });
        ((Div) icoExpand.getImpl()).setParent(ic1);

        TFIconClass icoOptions = new TFIconClass();
        icoOptions.setIconClass("ellipsis-h");
        icoOptions.setSize(15);
        icoOptions.setHint("Mais");
        icoOptions.setColor("b9babb !important");
        ((Div) icoOptions.getImpl()).setStyle("padding-right: 15px; padding-top: 6px");
        ((Div) icoOptions.getImpl()).setParent(ic1);
        icoOptions.setVisible(true);

        if (showOptionsPopup) {
            TFPopupMenu ppOptions = new TFPopupMenu();
            ppOptions.setName("ppOptions" + bookmark);
            formMain.addChildren(ppOptions);
            ppOptions.applyProperties();

            ((Div) icoOptions.getImpl()).setContext(((Popup) ppOptions.getImpl()));

            TFMenuItem mmEditar = new TFMenuItem();
            mmEditar.setName("mmEditar" + bookmark);
            mmEditar.setCaption("Editar");
            ppOptions.addChildren(mmEditar);
            mmEditar.applyProperties();

            ((Div) icoOptions.getImpl()).addEventListener(Events.ON_CLICK, (org.zkoss.zk.ui.event.Event event) -> {
                ppOptions.open(icoOptions);
            });

            mmEditar.addEventListener(Events.ON_CLICK, (EventListener<Event<Object>>) (Event<Object> event) -> {
                editarContainer(infoCnt, row, tf, infoCntBotao, icoOptions, icoExpand, containerPai);
            });

            TFMenuItem mmApagar = new TFMenuItem();
            mmApagar.setName("mmApagar" + bookmark);
            mmApagar.setCaption("Apagar");
            ppOptions.addChildren(mmApagar);
            mmApagar.applyProperties();

            mmApagar.addEventListener(Events.ON_CLICK, (EventListener<Event<Object>>) (Event<Object> event) -> {
                int bookmarkMemo = (int) tf.getAttribute(Row.ROW_BOOKMARK);
                callBackEvent.excluir(bookmarkMemo);
                container.removeChildren(row);
                containerPai.invalidate();
            });
        }

        if (clickEditContainter) {
            infoCnt.addEventListener(Events.ON_CLICK, (org.zkoss.zk.ui.event.Event event) -> {
                editarContainer(infoCnt, row, tf, infoCntBotao, icoOptions, icoExpand, containerPai);
                callBackEvent.clickMemo();
            });
            ((Textbox) tf.getImpl()).addEventListener(Events.ON_CLICK, (org.zkoss.zk.ui.event.Event event) -> {
                if (!infoCntBotao.isVisible()) {
                    editarContainer(infoCnt, row, tf, infoCntBotao, icoOptions, icoExpand, containerPai);
                    callBackEvent.clickMemo();
                }
            });
        } else {
            ((Textbox) tf.getImpl()).addEventListener(Events.ON_CLICK, (org.zkoss.zk.ui.event.Event event) -> {
                callBackEvent.clickMemo();
            });
        }

        if (!buttonTop) {
            Hlayout infoCnt5 = new Hlayout();
            infoCnt5.setStyle("padding:5px;");
            infoCnt5.setVflex("min");
            infoCnt5.setHflex("true");
            infoCnt5.setParent(infoCntBotao);
        }

        TFButton btnCancelar = new TFButton();
        btnCancelar.setCaption("Cancelar");

        ((Button) btnCancelar.getImpl()).setStyle("background: #e84646; width: 100px!important");

        btnCancelar.addEventListener(Events.ON_CLICK, (EventListener) event -> {
            int heigthl = getHeigthMemo(tf.getValue().asString());
            disableMemo(containerPai, infoCntBotao, tf, infoCnt, heigthl, row, icoOptions, icoExpand);
            callBackEvent.cancelar(bookmark);
        });

        TFButton btnSalvar = new TFButton();
        btnSalvar.setCaption("Salvar");
        ((Button) btnSalvar.getImpl()).setStyle("background: #4ec970; width: 100px!important");

        if (buttonTop) {
            ((Button) btnSalvar.getImpl()).setParent(infoCntBotao);
            ((Button) btnCancelar.getImpl()).setParent(infoCntBotao);
        } else {
            ((Button) btnCancelar.getImpl()).setParent(infoCntBotao);
            ((Button) btnSalvar.getImpl()).setParent(infoCntBotao);
        }

        btnSalvar.addEventListener(Events.ON_CLICK, (EventListener) event -> {
            int bookmarkMemo = (int) tf.getAttribute(Row.ROW_BOOKMARK);

            String[] lines1 = tf.getValue().asString().split("[\n]");
            if ((lines1.length == 0) || (tf.getValue().asString().trim().equals(""))) {
                Dialog.create()
                        .title("Atenção")
                        .message("Campo observação obrigatório o preenchimento")
                        .showInformation();
                return;
            }

            callBackEvent.salvar(tf.getValue().asString(), bookmarkMemo);

            int heigthl = getHeigthMemo(tf.getValue().asString());

            disableMemo(containerPai, infoCntBotao, tf, infoCnt, heigthl, row, icoOptions, icoExpand);
        });

        if (inEditChange) {
            editarContainer(infoCnt, row, tf, infoCntBotao, icoOptions, icoExpand, containerPai);
        }
    }

    private void editarContainer(Div infoCnt, TFHBox row, TFMemo tf, Hlayout infoCntBotao, TFIconClass icoOptions, TFIconClass icoExpand, TFVBox containerPai) {
        infoCnt.setHeight("200px");
        row.setHeight(200);
        row.invalidate();
        tf.setReadOnly(false);
        tf.setFocus();

        infoCntBotao.setVisible(true);
        ((Textbox) tf.getImpl()).setInplace(false);
        infoCntBotao.invalidate();
        icoOptions.setVisible(false);
        icoExpand.setVisible(false);
        containerPai.invalidate();
    }

}
