package freedom.util;

import freedom.client.controls.ITFForm;
import freedom.client.util.FreedomUtilities;
import org.zkoss.zk.ui.util.Clients;
import org.zkoss.zul.Vlayout;
import org.zkoss.zul.Window;

public class FormClientUtils {

    public static void openFullscreen(){
        org.zkoss.zk.ui.util.Clients.evalJavaScript(
                "var elem = document.documentElement;if (elem.requestFullscreen) {"+ System.lineSeparator()
                +"    elem.requestFullscreen();" +System.lineSeparator()
                +"  } else if (elem.webkitRequestFullscreen) {" +System.lineSeparator()
                +"    elem.webkitRequestFullscreen();" +System.lineSeparator()
                +"  } else if (elem.msRequestFullscreen) {"+System.lineSeparator()
                +"    elem.msRequestFullscreen();"+System.lineSeparator()
                +"  }");
    }

    public static void closedFullscreen(){
        org.zkoss.zk.ui.util.Clients.evalJavaScript(
                "var elem = document.documentElement; if (document.exitFullscreen) {"+ System.lineSeparator()
                +"    document.exitFullscreen();"+ System.lineSeparator()
                +"  } else if (document.webkitExitFullscreen) {"+ System.lineSeparator()
                +"    document.webkitExitFullscreen();"+ System.lineSeparator()
                +"  } else if (document.msExitFullscreen) {"+ System.lineSeparator()
                +"    document.msExitFullscreen();"+ System.lineSeparator()
                +"  }");
    }

    public static void windowClose(){
        org.zkoss.zk.ui.util.Clients.evalJavaScript("window.close();");
    }

    /**
     * Rola a página suavemente até um elemento específico usando JavaScript e jQuery.
     * <p>
     * Esta função injeta um script no lado do cliente que primeiro localiza o contêiner
     * rolável mais próximo do elemento alvo (ou o corpo da página, se nenhum for encontrado).
     * Em seguida, anima a posição de rolagem para que o elemento se torne visível,
     * aplicando um deslocamento vertical.
     * <p>
     * <b>Requer jQuery no frontend.</b>
     *
     * @param id O ID do elemento HTML para o qual rolar (sem o caractere '#').
     * @param durationMs A duração da animação de rolagem em milissegundos.
     * @param offsetPx O deslocamento em pixels a ser aplicado acima do elemento.
     * Um valor positivo deixa um espaço acima do elemento.
     */
    public static void scrollToElement(String id, int durationMs, int offsetPx) {
        String scriptTemplate = String.join("\n",
                "// IIFE para encapsular a lógica e evitar poluir o escopo global.",
                "(($el, duration, offset) => {",
                "    if (!$el || $el.length === 0) {",
                "        console.warn(`Elemento com id '` + id + `' não encontrado para rolagem.`);",
                "        return;",
                "    }",
                "",
                "    // Encontra o primeiro contêiner pai que pode ser rolado.",
                "    const container = (() => {",
                "        let parent = $el.parent();",
                "        while (parent.length > 0) {",
                "            if (['auto', 'scroll'].includes(parent.css('overflow-y'))) {",
                "                return parent;",
                "            }",
                "            parent = parent.parent();",
                "        }",
                "        // Se nenhum contêiner rolável for encontrado, usa o 'html' ou 'body'.",
                "        return $('html, body');",
                "    })();",
                "",
                "    // Calcula a posição de destino da rolagem.",
                "    const targetScrollTop = $el.offset().top - container.offset().top + container.scrollTop() - offset;",
                "",
                "    // Anima a rolagem do contêiner para a posição do elemento.",
                "    container.animate({ scrollTop: targetScrollTop }, duration);",
                "",
                "})($('#%s'), %d, %d);"
        );

        // Formata o script com os parâmetros e o executa no cliente
        String formattedScript = String.format(scriptTemplate, id, durationMs, offsetPx);
        FreedomUtilities.invokeLater(() -> {
                    Clients.evalJavaScript(formattedScript);
                });
    }

    /**
     * Rola a página suavemente até um elemento com uma duração e deslocamento padrão.
     * <p>
     * Versão simplificada que chama {@link #scrollToElement(String, int, int)} com valores padrão
     * (duração de 700ms e deslocamento de 15px).
     *
     * @param id O ID do elemento HTML para o qual rolar (sem o caractere '#').
     * @see #scrollToElement(String, int, int)
     */
    public static void scrollToElement(String id) {
        final int DEFAULT_DURATION_MS = 700;
        final int DEFAULT_OFFSET_PX = 15;
        scrollToElement(id, DEFAULT_DURATION_MS, DEFAULT_OFFSET_PX);
    }


    /**
     * Ajusta a altura do formulário, para encaixar aos componentes dentro dele.
     * @param form O formulário a ser ajustado.
     */
    public static void fixFormHeight(ITFForm form){
        FreedomUtilities.invokeLater(() -> {
            Window window = (Window)((Vlayout) form.getImpl()).getParent();
            window.setHeight("Auto");
        });
    }

}
