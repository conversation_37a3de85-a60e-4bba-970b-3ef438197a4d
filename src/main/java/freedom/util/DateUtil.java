package freedom.util;

import freedom.client.controls.impl.TFDate;
import freedom.client.controls.impl.TFTime;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {

    public static int getHoras(
            Date data
    ) {
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(
                data
        );
        return calendario.get(
                Calendar.HOUR_OF_DAY
        );
    }

    public static int getMinutos(
            Date data
    ) {
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(
                data
        );
        return calendario.get(
                Calendar.MINUTE
        );
    }

    public static int getSegundos(
            Date data
    ) {
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(
                data
        );
        return calendario.get(
                Calendar.SECOND
        );
    }

    public static Date addHoras(
            Date data
            ,int horas
    ) {
        if (data == null) {
            return null;
        }
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(
                data
        );
        calendario.add(
                Calendar.HOUR_OF_DAY
                ,horas
        );
        data = calendario.getTime();
        return data;
    }

    public static Date addMinutos(
            Date data
            ,int minutos
    ) {
        if (data == null) {
            return null;
        }
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(
                data
        );
        calendario.add(
                Calendar.MINUTE
                ,minutos
        );
        data = calendario.getTime();
        return data;
    }

    public static Date addSegundos(
            Date data
            ,int segundos
    ) {
        if (data == null) {
            return null;
        }
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(data);
        calendario.add(
                Calendar.SECOND
                ,segundos
        );
        data = calendario.getTime();
        return data;
    }

    public static Date addMilisegundos(
            Date data
            ,int milisegundos
    ) {
        if (data == null) {
            return null;
        }
        Calendar calendario = Calendar.getInstance();
        calendario.setTime(
                data
        );
        calendario.add(
                Calendar.MILLISECOND
                ,milisegundos
        );
        data = calendario.getTime();
        return data;
    }

    /** Ex.:
     * java.util.Date date = new java.util.Date();
     * String retFuncao = DateUtil.getDataHoraConvertidaEmTexto(date);
     * Resultado: dd/MM/yyyy HH:mm:ss
     *            21/03/2023 01:02:03
     * */
    public static String getDataHoraConvertidaEmTexto(
            Date data
    ) {
        return getDataHoraConvertidaEmTexto(
                data
                ,Constantes.DD_MM_YYYY_HH_MM_SS
        );
    }

    /** Ex.:
     * java.util.Date date = new java.util.Date();
     * String padraoDeFormatacao = "dd/MM/yyyy HH:mm:ss";
     * String retFuncao = DateUtil.getDataHoraConvertidaEmTexto(date,
     *                                                          padraoDeFormatacao);
     * Resultado: dd/MM/yyyy HH:mm:ss
     *            21/03/2023 01:02:03
     * */
    public static String getDataHoraConvertidaEmTexto(
            Date data
            ,String padraoDeFormatacao
    ) {
        DateFormat formatadorDeData = new SimpleDateFormat(
                padraoDeFormatacao
        );
        return formatadorDeData.format(
                data
        );
    }

    /** Ex.:
     * java.util.Date date = new java.util.Date();
     * String retFuncao = DateUtil.getDataConvertidaEmTexto(date);
     * Resultado: dd/MM/yyyy
     *            21/03/2023
     * */
    public static String getDataConvertidaEmTexto(
            Date data
    ) {
        return getDataHoraConvertidaEmTexto(
                data
                ,"dd/MM/yyyy"
        );
    }

    /** Ex.:
     * String texto = "21/03/2023";
     * java.util.Date date = DateUtil.getDataDoTexto(texto);
     * Resultado: dd/MM/yyyy HH:mm:ss
     *            21/03/2023 00:00:00
     * */
    public static Date getDataDoTexto(
            String texto
    ) {
        DateFormat formatadorDeData = new SimpleDateFormat(
                "dd/MM/yyyy"
        );
        return getDataHoraDoTexto(
                texto
                ,formatadorDeData
        );
    }

    /** Ex.:
     * String texto = "21/03/2023";
     * java.util.Date date = DateUtil.getDataHoraDoTexto(texto);
     * Resultado: dd/MM/yyyy HH:mm:ss
     *            21/03/2023 00:00:00
     * */
    public static Date getDataHoraDoTexto(
            String texto
    ) {
        DateFormat formatadorDeDataHora = new SimpleDateFormat(
                Constantes.DD_MM_YYYY_HH_MM_SS
        );
        return getDataHoraDoTexto(
                texto
                ,formatadorDeDataHora
        );
    }

    /** Ex.:
     * String texto = "21/03/2023 12:34:56";
     * DateFormat formatadorDeData = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
     * java.util.Date date = DateUtil.getDataHoraDoTexto(texto,
     *                                                   formatadorDeData);
     * Resultado: dd/MM/yyyy HH:mm:ss
     *            21/03/2023 12:34:56
     * */
    public static Date getDataHoraDoTexto(
            String texto
            ,DateFormat formatadorDeData
    ) {
        Date data;
        try {
            data = formatadorDeData.parse(
                    texto
            );
        } catch (
                ParseException parseException
        ) {
            data = null;
        }
        return data;
    }

    /** Ex.:
     * java.util.Date date = primeiroDiaDoMesAtual = DateUtil.getPrimeiroDiaDoMesAtual();
     * Resultado: dd/MM/yyyy
     *            01/03/2023
     * */
    public static Date getPrimeiroDiaDoMesAtual() {
        Date dataAtual = new Date();
        String dataAtualTexto = getDataConvertidaEmTexto(
                dataAtual
        );
        String primeiroDiaDoMesAtualTexto = "01"
                + dataAtualTexto.substring(
                        2
                ,10
        ); // /MM/yyyy
        return getDataDoTexto(
                primeiroDiaDoMesAtualTexto
        );
    }

    /**
     * boolean retFuncao = DateUtil.primeiraDataMenorIgualSegundaData(primeiraData, segundaData);
     * @param primeiraData primeira data recebida
     * @param segundaData segunda data recebida
     * @return True se a primeira data é menor ou igual a segunda data, do contrário false.
     */
    public static boolean primeiraDataMenorIgualSegundaData(
            Date primeiraData
            ,Date segundaData
    ) {
        if ((primeiraData == null)
                || (segundaData == null)) {
            return false;
        }
        return primeiraData.compareTo(
                segundaData
        ) <= 0;
    }

    /**
     * boolean retFuncao = DateUtil.primeiraDataMenorSegundaData(primeiraData, segundaData);
     * @param primeiraData primeira data recebida
     * @param segundaData segunda data recebida
     * @return True se a primeira data é menor que a segunda data, do contrário false.
     */
    public static boolean primeiraDataMenorSegundaData(
            Date primeiraData
            ,Date segundaData
    ) {
        if ((primeiraData == null)
                || (segundaData == null)) {
            return false;
        }
        return primeiraData.compareTo(
                segundaData
        ) < 0;
    }

    /**
     * boolean retFuncao = DateUtil.primeiraDataIgualSegundaData(primeiraData, segundaData);
     * @param primeiraData primeira data recebida
     * @param segundaData segunda data recebida
     * @return True se a primeira data é igual a segunda data, do contrário false.
     */
    public static boolean primeiraDataIgualSegundaData(
            Date primeiraData
            ,Date segundaData
    ) {
        if ((primeiraData == null)
                || (segundaData == null)) {
            return false;
        }
        return primeiraData.compareTo(
                segundaData
        ) == 0;
    }

    /**
     * boolean retFuncao = DateUtil.primeiraDataMaiorIgualSegundaData(primeiraData, segundaData);
     * @param primeiraData primeira data recebida
     * @param segundaData segunda data recebida
     * @return true se a primeira data é maior que a segunda, do contrário false.
     */
    public static boolean primeiraDataMaiorIgualSegundaData(
            Date primeiraData
            ,Date segundaData
    ) {
        if ((primeiraData == null)
                || (segundaData == null)) {
            return false;
        }
        return primeiraData.compareTo(
                segundaData
        ) >= 0;
    }


    /**
     * boolean retFuncao = DateUtil.primeiraDataMaiorSegundaData(primeiraData, segundaData);
     * @param primeiraData primeira data recebida
     * @param segundaData segunda data recebida
     * @return true se a primeira data é maior que a segunda, do contrário false.
     */
    public static boolean primeiraDataMaiorSegundaData(
            Date primeiraData
            ,Date segundaData
    ) {
        if ((primeiraData == null)
                || (segundaData == null)) {
            return false;
        }
        return (primeiraData.compareTo(
                segundaData
        ) > 0);
    }

    /**
     * Extrai a hora como texto de uma data
     * @param dataString uma string que representa a data e a hora no formato "dd/MM/yyyy HH:mm:ss"
     * @return uma string que representa apenas a hora da data fornecida, no formato "HH:mm:ss"
     */
    public static String getHoraTexto(
            String dataString
    ) {
        try {
            SimpleDateFormat formato = new SimpleDateFormat(
                    Constantes.DD_MM_YYYY_HH_MM_SS
            );
            Date data = formato.parse(
                    dataString
            );
            return getHoraTexto(
                    data
            );
        } catch (
                Exception exception
        ) {
            return "";
        }

    }

    /**
     * Extrai a hora como texto de uma data
     * @param data a data que será extraída a hora
     * @return uma string que representa apenas a hora da data fornecida, no formato "HH:mm:ss"
     */
    public static String getHoraTexto(
            Date data
    ) {
        try {
            SimpleDateFormat formatoHora = new SimpleDateFormat(
                    "HH:mm:ss"
            );
            return formatoHora.format(
                    data
            );

        } catch (
                Exception exception
        ) {
            return "";
        }
    }

    /**
     * Date novaData = Main.adicionarHora(data, hora);
     * @param data data um objeto Date que representa a data original
     * @param hora hora uma string que representa a hora a ser adicionada à data, no formato "HH:mm:ss"
     * @return um objeto Date que representa a data com a hora adicionada
     */
    public static Date adicionarHoraTexto(
            Date data
            ,String hora
    ){
        SimpleDateFormat formatoHora = new SimpleDateFormat(
                "HH:mm:ss"
        );
        Date horaObj;
        try {
            horaObj = formatoHora.parse(hora);
        } catch (
                ParseException parseException
        ) {
            return null;
        }
        Calendar calData = Calendar.getInstance();
        calData.setTime(
                data
        );
        Calendar calHora = Calendar.getInstance();
        calHora.setTime(
                horaObj
        );
        calData.set(
                Calendar.HOUR_OF_DAY
                ,calHora.get(
                        Calendar.HOUR_OF_DAY
                )
        );
        calData.set(
                Calendar.MINUTE
                ,calHora.get(
                        Calendar.MINUTE
                )
        );
        calData.set(
                Calendar.SECOND
                ,calHora.get(
                        Calendar.SECOND
                )
        );
        return calData.getTime();
    }

    /**
     *
     * @param dataRecebeHora - A data que vai receber a hora.
     * @param dataExtraiHora - A data que vai fornecer a hora.
     * @return A data que recebe com a hora da data que fornece.
     */
    public static Date adicionaHoraDeOutraData(
            Date dataRecebeHora
            ,Date dataExtraiHora
    ) {
        try {
            String horaTexto = getHoraTexto(
                    dataExtraiHora
            );
            return DateUtil.adicionarHoraTexto(
                    dataRecebeHora
                    ,horaTexto
            );
        } catch (
                Exception exception
        ) {
            return null;
        }

    }

    /** Ex.:
     * Date dataHora = DateUtil.getDataHoraComponentesTFDateTFTime(
     *         this.dtNomeDoComponente
     *        ,this.timeNomeDoComponente
     * );
     *
     * @param componenteData - O nome do componente que contém a data.
     * @param componenteHora - O nome do componente que contém a hora.
     * @return A data preenchida ou o valor null.
     * */
    public static Date getDataHoraComponentesTFDateTFTime(
            TFDate componenteData
            ,TFTime componenteHora
            ) {
        try {
            Date data = componenteData.getValue().asDate();
            if (data == null) {
                return null;
            }
            Date hora = componenteHora.getValue().asDate();
            if (hora == null) {
                hora = DateUtils.truncDay(
                        new Date()
                );
            }
            int horas = DateUtil.getHoras(
                    hora
            );
            int minutos = DateUtil.getMinutos(
                    hora
            );
            Date dataHora = DateUtil.addHoras(
                    data
                    ,horas
            );
            dataHora = DateUtil.addMinutos(
                    dataHora
                    ,minutos
            );
            return dataHora;
        } catch (
                Exception exception
        ) {
            return null;
        }
    }

}
