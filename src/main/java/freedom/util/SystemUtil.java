/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.client.controls.impl.TFForm;

import static freedom.util.CastUtil.asString;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import javax.naming.InitialContext;

/**
 *
 * <AUTHOR>
 */
public class SystemUtil {

    public static String getApplictionName() throws Exception {
        String applicationName;
        InitialContext ic = new InitialContext();
        applicationName = (String) ic.lookup("java:comp/env/APPLICATION_NAME");
        return applicationName;
    }

    public static String getSistema() throws Exception {
        String sistema;

        sistema = getApplictionName();

        switch (sistema.trim().toUpperCase()) {
            case "CRMPARTS":
                sistema = "K";
                break;
            case "CRMSERVICE":
                sistema = "B";
                break;
        }

        return sistema;
    }

    public static Date getNewDateHour(Date data, String hora) {
        //01/01/2019
        DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        String dataHora = dateFormat.format(data);
        String ano = dataHora.substring(6, 10);
        String mes = dataHora.substring(3, 5);
        String dia = dataHora.substring(0, 2);
        String newDate = ano + "-" + mes + "-" + dia + "T" + hora;
        LocalDateTime localDateTime = LocalDateTime.parse(newDate);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String getDateHoraMesAbrev(Date data) {

        Calendar cal = Calendar.getInstance();
        cal.setTime(data);
        int mes = cal.get(Calendar.MONTH) + 1;
        int ano = cal.get(Calendar.YEAR);
        int dia = cal.get(Calendar.DAY_OF_MONTH);

        String mesExt = "";

        switch (mes) {
            case 1:
                mesExt = "Jan";
                break;
            case 2:
                mesExt = "Fev";
                break;
            case 3:
                mesExt = "Mar";
                break;
            case 4:
                mesExt = "Abr";
                break;
            case 5:
                mesExt = "Mai";
                break;
            case 6:
                mesExt = "Jun";
                break;
            case 7:
                mesExt = "Jul";
                break;
            case 8:
                mesExt = "Ago";
                break;
            case 9:
                mesExt = "Set";
                break;
            case 10:
                mesExt = "Out";
                break;
            case 11:
                mesExt = "Nov";
                break;
            case 12:
                mesExt = "Dez";
                break;
            default:
                break;
        }

        return String.format("%02d", dia) + "/" + mesExt + "/" + ano + " às " + asString("HH:mm", data);
    }

    public static String retiraLetras(String value) {
        return value.replaceAll("[^0-9]", "");
    }

    public static boolean isDebug() {
        return java.lang.management.ManagementFactory.getRuntimeMXBean().getInputArguments().toString().contains("-Xdebug");
    }

    public static boolean isLinux() {
        boolean retorno = false;
        System.out.println("Verificando se o sistema operacional é Linux");
        String sistema = System.getProperty("os.name");
        retorno = sistema.startsWith("Linux");

        if (retorno) {
            System.out.println("Sistema Operacional é Linux = " + sistema);
        } else {
            System.out.println("Sistema Operacional Não é Linux = " + sistema);
        }

        return retorno;
    }

}
