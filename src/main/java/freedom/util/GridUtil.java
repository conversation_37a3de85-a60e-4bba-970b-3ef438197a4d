/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.client.controls.ITFGridColumn;
import freedom.client.controls.ITFGridMDColumn;
import freedom.client.controls.ITFTreeGridColumn;
import freedom.client.controls.impl.*;
import freedom.client.controls.impl.grid.TFGridExporter;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.data.DataException;
import freedom.data.impl.Table;
import java.util.*;

/**
 * <AUTHOR>
 */
public class GridUtil {

    public static ITFGridColumn getColumnGrid(TFGrid grid, String columnName) {
        ITFGridColumn column = null;
        Optional<ITFGridColumn> opt = grid.getColumns().parallelStream()
                .filter(col -> col.getFieldName().equals(columnName))
                .findFirst();
        if (opt.isPresent()) {
            column = opt.get();
        }
        return column;
    }

    public static ITFGridMDColumn getColumnGrid(TFGridMD grid, String columnName) {
        ITFGridMDColumn column = null;
        Optional<ITFGridMDColumn> opt = grid.getColumns().parallelStream()
                .filter(col -> col.getFieldName().equals(columnName))
                .findFirst();
        if (opt.isPresent()) {
            column = opt.get();
        }
        return column;
    }

    public static ITFTreeGridColumn getColumnGrid(TFTreeGrid grid, String columnName) {
        ITFTreeGridColumn column = null;
        Optional<ITFTreeGridColumn> opt = grid.getColumns().parallelStream()
                .filter(col -> col.getFieldName().equals(columnName))
                .findFirst();
        if (opt.isPresent()) {
            column = opt.get();
        }
        return column;
    }

    public static void setEnabledColumn(
            String columnName
            ,TFGrid grid
            ,boolean enabled
    ) throws Exception {
        ITFGridColumn column;
        column = getColumnGrid(grid, columnName);
        if (column == null) {
            throw new Exception(
                    "Erro ao localizar coluna ("
                            + columnName
                            + ") Grid: "
                            + grid.getName()
                            + ". Column não encontrada."
            );
        } else {
            column.setEditorEnabled(
                    enabled
            );
        }
    }

    public void exportarExcel(
            TFTable table
            ,TFGrid grid
    ) throws DataException {
        if (table.count() > 0) {
            TFGridExporter gridExporter = new TFGridExporter();
            gridExporter.exportExcel(
                    grid
            );
        }
    }

    public static void exportarExcel(
            TFGrid grid
    ) throws Exception {
        TFTable tableDaGrade = (TFTable) grid.getTable();
        if ((tableDaGrade != null)
                && (tableDaGrade.count() > 0)) {
            Table tabelaParaExportar = tableDaGrade.getTable();
            Map<String, String> mapeamentoDasColunas = GridUtil.getMapeamentoDasColunas(
                    grid
            );
            byte[] vetorDeBytes = tabelaParaExportar.toXLSX(
                    mapeamentoDasColunas
                    ,true
            );
            TFReport report = new TFReport();
            report.setAttribute(
                    "DataSource"
                    ,tableDaGrade
            );
            report.runToExcel2(
                    vetorDeBytes
            );
        }
    }

    private static Map<String, String> getMapeamentoDasColunas(
            TFGrid grid
    ) {
        Map<String, String> mapeamentoDasColunas = new LinkedHashMap<>();
        List<ITFGridColumn> colunasDaGrade = grid.getColumns();
        for (ITFGridColumn coluna : colunasDaGrade) {
            if (coluna.isVisible()) {
                String fieldName = coluna.getFieldName();
                String titleCaption = coluna.getTitleCaption();
                if ((fieldName != null)
                        && (!fieldName.isEmpty())) {
                    mapeamentoDasColunas.put(
                            fieldName
                            ,titleCaption
                    );
                }
            }
        }
        return mapeamentoDasColunas;
    }

    public static void setFontForAllColumns(
            TFGrid grid
            ,String fontName
            ,String fontColor
            ,String fontStyle
            ,int fontSize
    ) {
        if (Objects.isNull(fontName)) {
            fontName = "Tahoma";
        }
        if (Objects.isNull(fontColor)) {
            fontColor = "clWindowText";
        }
        if (Objects.isNull(fontStyle)) {
            fontStyle = "[]";
        }
        // Crie a expressão de fonte com as configurações desejadas
        TFFontExpression fontExpression = new TFFontExpression();
        fontExpression.setExpression("*");
        fontExpression.setEvalType(Constantes.ET_EXPRESSION);
        fontExpression.setFontName(fontName);
        fontExpression.setFontColor(fontColor);
        fontExpression.setFontSize(fontSize);
        fontExpression.setFontStyle(fontStyle);
        // Itere sobre todas as colunas e aplique a expressão de fonte
        List<ITFGridColumn> columns = grid.getColumns();
        for (ITFGridColumn column : columns) {
            column.getFont().add(fontExpression);
        }
    }

    public static void setFontColumn(
            TFGrid grid
            ,String columnName
            ,boolean clear
            ,String fontName
            ,String fontColor
            ,String fontStyle
            ,int fontSize
    ) {
        if (Objects.isNull(fontName)) {
            fontName = "Tahoma";
        }
        if (Objects.isNull(fontColor)) {
            fontColor = "clWindowText";
        }
        if (Objects.isNull(fontStyle)) {
            fontStyle = "[]";
        }
        // Crie a expressão de fonte com as configurações desejadas
        TFFontExpression fontExpression = new TFFontExpression();
        fontExpression.setExpression("*");
        fontExpression.setEvalType(Constantes.ET_EXPRESSION);
        fontExpression.setFontName(fontName);
        fontExpression.setFontColor(fontColor);
        fontExpression.setFontSize(fontSize);
        fontExpression.setFontStyle(fontStyle);
        if (clear) {
            GridUtil.getColumnGrid(grid, columnName)
                    .getFont()
                    .clear();
        }
        GridUtil.getColumnGrid(grid, columnName)
                .getFont()
                .add(fontExpression);
    }

    public static void setImageExpressionColumn(
            TFGrid grid
            ,String columnName
            ,String expression
            ,int imageId
            ,String hint
            ,EventListener listener
    ) {
        TFImageExpression imageExpression = new TFImageExpression();
        imageExpression.setExpression("*");
        imageExpression.setEvalType(Constantes.ET_EXPRESSION);
        imageExpression.setExpression(expression);
        imageExpression.setImageId(imageId);
        imageExpression.setHint(hint);
        GridUtil.getColumnGrid(grid, columnName)
                .getImages()
                .add(imageExpression);
        if (Objects.nonNull(listener)) {
            imageExpression.addEventListener("onClick", (EventListener<Event<Object>>) (Event<Object> event) -> {
                event.setValue(imageId);
                listener.onEvent(event);
            });
        }
    }

    public static void setColorForAllColumns(
            TFGrid grid
            ,String color
            ,String expression
    ) {
        TFColorExpression colorExpression = new TFColorExpression();
        colorExpression.setExpression(
                expression
        );
        colorExpression.setEvalType(
                Constantes.ET_EXPRESSION
        );
        colorExpression.setColor(
                color
        );
        // Itere sobre todas as colunas e aplique a expressão de cor
        List<ITFGridColumn> columns = grid.getColumns();
        for (ITFGridColumn column : columns) {
            column.getColors().add(colorExpression);
        }
    }

    public static void setColorColumn(
            TFGrid grid
            ,String columnName
            ,String color
            ,String expression
    ) {
        TFColorExpression colorExpression = new TFColorExpression();
        colorExpression.setExpression(
                expression
        );
        colorExpression.setEvalType(
                Constantes.ET_EXPRESSION
        );
        colorExpression.setColor(
                color
        );
        GridUtil.getColumnGrid(grid, columnName)
                .getColors()
                .add(colorExpression);
    }

}
