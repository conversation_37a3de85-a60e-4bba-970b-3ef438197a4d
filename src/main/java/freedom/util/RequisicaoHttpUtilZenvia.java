package freedom.util;

import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.ssl.TrustStrategy;

import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;

public class RequisicaoHttpUtilZenvia {

    private final String _url;
    private final String _tokenAuth;

    public RequisicaoHttpUtilZenvia(String url, String tokenAuth) {
        this._url = url;
        this._tokenAuth = tokenAuth;
    }

    public HttpResponse methodPostZenvia(String request, String token) throws Exception {
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, (TrustStrategy) (final X509Certificate[] chain, final String authType) -> true)
                .build();

        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        int timeout = 10;
        RequestConfig config = RequestConfig.custom()
                .setConnectTimeout(timeout * 1000)
                .setConnectionRequestTimeout(timeout * 1000)
                .setSocketTimeout(timeout * 1000).build();

        CloseableHttpClient httpclient = HttpClientBuilder.create()
                .setDefaultRequestConfig(config)
                .setSSLSocketFactory(sslsf).build();

        HttpPost requisicao = new HttpPost(_url);

        if (!token.isEmpty()) {
            requisicao.addHeader("X-API-TOKEN", token);
        }

        if (!_tokenAuth.isEmpty()) {
            requisicao.addHeader("Authorization", "Bearer " + _tokenAuth);
        }

        requisicao.addHeader("Content-Type", "application/json");
        if (!request.equals("")) {
            StringEntity requestConvertido = new StringEntity(request, ContentType.APPLICATION_JSON);
            requisicao.setEntity(requestConvertido);
        }

        HttpResponse response = httpclient.execute(requisicao);
        return response;
    }

    public HttpResponse methodGetZenvia() {
        try {
            HttpClient client = HttpClientBuilder.create().build();
            HttpGet requisicao = new HttpGet(_url);
            requisicao.addHeader("Content-Type", "application/json");
            if (!_tokenAuth.trim().equals("")) {
                requisicao.addHeader("Authorization", "Bearer " + _tokenAuth);
            }
            return client.execute(requisicao);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            return null;
        }
    }

    public HttpResponse methodDeleteZenvia(String token) throws Exception {
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, (TrustStrategy) (final X509Certificate[] chain, final String authType) -> true)
                .build();

        SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(
                sslContext,
                NoopHostnameVerifier.INSTANCE);

        CloseableHttpClient httpclient = HttpClientBuilder.create().setSSLSocketFactory(sslsf).build();

        HttpDelete requisicao = new HttpDelete(_url);

        if (!token.isEmpty()) {
            requisicao.addHeader("X-API-TOKEN", token);
        }

        if (!_tokenAuth.isEmpty()) {
            requisicao.addHeader("Authorization", "Bearer " + _tokenAuth);
        }

        HttpResponse response = httpclient.execute(requisicao);
        return response;
    }

}
