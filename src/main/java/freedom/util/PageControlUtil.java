package freedom.util;

import freedom.client.controls.impl.TFPageControl;
import freedom.client.controls.impl.TFTabsheet;
import org.zkoss.zk.ui.Component;

public class PageControlUtil {
    
    public static int getIndex(
            TFPageControl pgControl
            ,TFTabsheet tabSheet
    ) {
        int index = -1;
        for (int i = 0; i < pgControl.getChildren().size(); i++) {
            Component component = pgControl.getChildren().get(i);
            if (component.getId().equals(tabSheet.getName())) {
                index = i;
                return index;
            }
        }
        return index;
    }

}
