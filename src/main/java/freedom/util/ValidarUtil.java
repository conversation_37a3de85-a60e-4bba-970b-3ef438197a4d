/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.util;

import freedom.client.controls.IComponent;
import freedom.client.controls.impl.TFCheckBox;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFDecimal;

import static freedom.util.CastUtil.asString;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 *
 * <AUTHOR>
 */
public class ValidarUtil {

    private static final int[] pesoCPF = {11, 10, 9, 8, 7, 6, 5, 4, 3, 2};
    private static final int[] pesoCNPJ = {6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2};

    private static int calcularDigito(String str, int[] peso) {
        int soma = 0;
        for (int indice = str.length() - 1, digito; indice >= 0; indice--) {
            digito = Integer.parseInt(str.substring(indice, indice + 1));
            soma += digito * peso[peso.length - str.length() + indice];
        }
        soma = 11 - soma % 11;
        return soma > 9 ? 0 : soma;
    }

    /**
     * O médodo abaixo irá retornar se um determinado CPF informado é válido ou
     * não.
     *
     * @param cpf Informe o CPF a ser validado, podendo ser ou não informado com
     * mascara. Ex.: 123.456.789-00 ou 12345678900
     * @return Caso a validação do CPF de certo irá retornar como true se não
     * false.
     */
    public static boolean isValidCPF(String cpf) {

        String cpfAux = cpf;

        cpfAux = cpfAux.replace(".", "");
        cpfAux = cpfAux.replace("-", "");

        if (cpfAux.length() != 11) {
            return false;
        }

        switch (cpfAux) {
            case "11111111111":
            case "22222222222":
            case "33333333333":
            case "44444444444":
            case "99999999999":
            case "55555555555":
            case "66666666666":
            case "77777777777":
            case "88888888888":
            case "00000000000":
                return false;
            default:
                break;
        }

        int digito1 = calcularDigito(cpfAux.substring(0, 9), pesoCPF);
        int digito2 = calcularDigito(cpfAux.substring(0, 9) + digito1, pesoCPF);
        return cpfAux.equals(cpfAux.substring(0, 9) + digito1 + digito2);
    }

    /**
     * O médodo abaixo irá retornar se um determinado cnpj informado é válido ou
     * não.
     *
     * @param cnpj Informe o CNPJ a ser validado, podendo ser ou não informado
     * com mascara. Ex.: 12.345.678/0001-99 ou 12345678000199
     * @return Caso a validação do CNPJ de certo irá retornar como true se não
     * false.
     */
    public static boolean isValidCNPJ(String cnpj) {
        String cnpjAux = cnpj;

        cnpjAux = cnpjAux.replace(".", "");
        cnpjAux = cnpjAux.replace("/", "");
        cnpjAux = cnpjAux.replace("-", "");

        if (cnpjAux.length() != 14) {
            return false;
        }

        switch (cnpjAux) {
            case "11111111111111":
            case "44444444444444":
            case "55555555555555":
            case "66666666666666":
            case "77777777777777":
            case "88888888888888":
            case "99999999999999":
            case "00000000000000":
            case "33333333333333":
            case "22222222222222":
                return false;
            default:
                break;
        }

        int digito1 = calcularDigito(cnpjAux.substring(0, 12), pesoCNPJ);
        int digito2 = calcularDigito(cnpjAux.substring(0, 12) + digito1, pesoCNPJ);
        return cnpjAux.equals(cnpjAux.substring(0, 12) + digito1 + digito2);
    }

    public static void setEnableField(IComponent edt, boolean habilitar, boolean zerarValor) {
        if (edt instanceof TFCombo) {
            edt.setEnabled(habilitar);
            if (!habilitar && zerarValor) {
                ((TFCombo) edt).setValue(null);
            }
        } else if (edt instanceof TFDecimal) {
            edt.setEnabled(habilitar);
            if (!habilitar && zerarValor) {
                ((TFDecimal) edt).setValue(0.00);
            }
        } else if (edt instanceof TFCheckBox) {
            edt.setEnabled(habilitar);
            if (!habilitar && zerarValor) {
                ((TFCheckBox) edt).setValue(0.00);
            }
        }
    }

    public static String validarAliquota(Double aliquota, String nomeAliquota) {
        if (aliquota < 0) {
            return "Aliquota " + nomeAliquota + " não pode ser menor que zero.\n"
                    + " Valor Alíquota(" + asString(",##0.00", aliquota) + ")";
        } else if (aliquota > 99) {
            return "Aliquota " + nomeAliquota + " não pode ser maior que 99.\n"
                    + " Valor Alíquota(" + asString(",##0.00", aliquota) + ")";
        } else {
            return "";
        }
    }

    private static int gerarModulo11(String chave) {
        int total = 0;
        int peso = 2;

        for (int i = 0; i < chave.length(); i++) {
            total += (chave.charAt((chave.length() - 1) - i) - '0') * peso;
            peso++;
            if (peso == 10) {
                peso = 2;
            }
        }
        int resto = total % 11;
        return (resto == 0 || resto == 1) ? 0 : (11 - resto);
    }

    public static Boolean validarChaveEletronica(String chave) {
        boolean chaveValida = (chave.length() == 44);
        if (chaveValida) {
            String parteChave = chave.substring(0, chave.length() - 1);
            String modulo11 = Integer.toString(gerarModulo11(parteChave));
            String modulo11Chave = String.valueOf(chave.charAt(43));
            chaveValida = modulo11.equals(modulo11Chave);
        }
        return chaveValida;
    }

    public static boolean isEmailValid(String email) {
        if ((email == null) || (email.trim().isEmpty())) {
            return false;
        }
        email = email.replace(";", ",").trim();
        List<String> emails = Stream.of((email.split("\\s*,\\s*"))).collect(Collectors.toList());
        String emailPattern = "\\b(^[_A-Za-z0-9-]+(\\.[_A-Za-z0-9-]+)*@([A-Za-z0-9-])+(\\.[A-Za-z0-9-]+)*((\\.[A-Za-z0-9]{2,})|(\\.[A-Za-z0-9]{2,}\\.[A-Za-z0-9]{2,}))$)\\b";
        Pattern pattern = Pattern.compile(emailPattern, Pattern.CASE_INSENSITIVE);
        boolean retFunc = true;
        Matcher matcher;
        for (String value : emails) {
            if ((value == null) || (value.trim().isEmpty())) {
                retFunc = false;
            }
            if (retFunc) {
                matcher = pattern.matcher(value);
                retFunc = matcher.matches();
            }
        }
        return retFunc;
    }

    public static boolean isOnlyNumber(String text) {
        return text.matches("\\d*");
    }

    public static boolean validarDataNascimento(
            String dtNascimento
            ,String dataInvalidaBefore
    ){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy");
        try {
            Date dataInvalida = simpleDateFormat.parse(dataInvalidaBefore);
            Date nascimento = simpleDateFormat.parse(dtNascimento);
            String strDataAtual = simpleDateFormat.format(new Date());
            Date dataAtual = simpleDateFormat.parse(strDataAtual);
            if(nascimento.equals(dataAtual)
                    || nascimento.after(dataAtual)){
                return true;
            }
            if(nascimento.compareTo(dataInvalida) <= 0){
                return true;
            }
        } catch (ParseException parseException) {
            return true;
        }
        return false;
    }

    public static boolean isOnlyString(String text){
        return text.matches("[a-zA-Z]+$");
    }

}
