package freedom.bytecode.rn.wizard;
import freedom.commons.lang.IRegraNegocio;
import freedom.connection.ISession;
import freedom.data.impl.DeltaTO;
import java.rmi.Remote;
import java.rmi.RemoteException;
import freedom.data.DataException;
import freedom.bytecode.cursor.*;
import freedom.client.controls.impl.*;
import java.rmi.registry.*;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;
public abstract class SQLMonitorRN implements IRegraNegocio {

    private static final long serialVersionUID = 20130827081850L;
    public SQLMonitorRN() {
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                return (T) Class.forName("freedom.bytecode.rn." + rn.getSimpleName() + "U").newInstance();
            } catch (ClassNotFoundException e) {
                return (T) Class.forName("freedom.bytecode.rn." + rn.getSimpleName() + "A").newInstance();
            }
        } catch (ClassNotFoundException | IllegalAccessException | InstantiationException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

}