package freedom.bytecode.rn.enun;

import java.util.HashMap;
import java.util.Map;

public enum EnTipoAtendimento {
    RECEPTIVO("R"),
    ATIVO("A"),
    PASSIVO("P");

    private final String value;

    EnTipoAtendimento(String value) {
        this.value = value;
    }

    private static Map<String, EnTipoAtendimento> map = new HashMap<String, EnTipoAtendimento>();

    static {
        for (EnTipoAtendimento enTipoAtendimento : EnTipoAtendimento.values()) {
            map.put(enTipoAtendimento.value, enTipoAtendimento);
        }
    }

    public static String toString(EnTipoAtendimento enTipoAtendimento) {
        for (Map.Entry<String, EnTipoAtendimento> pair : map.entrySet()) {

            if (pair.getValue() == enTipoAtendimento) {
                return pair.getKey();
            }

        }
        return null;
    }

}
