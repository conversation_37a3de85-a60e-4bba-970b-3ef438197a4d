package freedom.bytecode.rn.enun;
public enum EnStatusVendaDeposito{
    TRANSFERIR_DEPOSITO_LOJA,
    REENVIAR_NFE_SAIDA_DEPOSITO,
    ENTRADA_TRANSFERENCIA_LOJA,
    CONVERTER_ORCAMENTO,
    CANCELAR_TRANSFERENCIA_DEPOSITO,
    FECHAR_PRE_NOTA,
    EMITIR_SIMPLES_REMESSA,
    DEVOLVER_MERCADORIA_PARA_DEPOSITO,
    REENVIAR_NFE_SAIDA_LOJA,
    ENTRADA_TRANSFERENCIA_DEPOSITO,
    CANCELAR_ORCAMENTO,
    CANCELAR_TRANSFERENCIA_LOJA,
    CANCELAR_ORCAMENTO_GERAL
}
