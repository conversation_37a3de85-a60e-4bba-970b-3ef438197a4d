package freedom.bytecode.form;

import freedom.bytecode.cursor.SERVICOS;
import freedom.bytecode.cursor.SERVICOS_DISP;
import freedom.bytecode.cursor.TEMPOS_PADROES;
import freedom.bytecode.form.wizard.FrmServicosW;
import freedom.client.controls.impl.TFGrid;
import freedom.client.controls.impl.TFSchema;
import freedom.client.controls.impl.TFTable;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.Dialog;
import freedom.client.util.FormUtil;
import freedom.client.util.FreedomUtilities;
import freedom.client.util.IDialog;
import freedom.commons.lang.IWorkList;
import freedom.data.DataException;
import freedom.data.RowState;
import freedom.util.*;
import org.zkoss.zul.Borderlayout;

public class FrmServicosA extends FrmServicosW {

    private static final long serialVersionUID = 20130827081850L;

    private final IWorkList wl = WorkListFactory.getInstance();
    // private int codCliente;
    private String nomeCliente;

    private String nomeModelo;

    private Boolean isValido = true;

    private final GridUtil gridUtil = new GridUtil();

    private final Boolean isReponsive = !wl.get("MOBILE_RESPONSIVE").isNull();

    private String modeModelo = "";

    private final int PAGINACAO_SIZE = 5;

    private boolean isAlterando(){
        return btnSalvar.isEnabled();
    }

    private TEMPOS_PADROES tbTemposPadroesTemp = new TEMPOS_PADROES("tbTemposPadroesTemp");

    public FrmServicosA() {
        try {

            // Emerson - Orione solicitou para esconder a tela de marcas
            FTabMarca.setVisible(false);

            definirValoresFiltroDefault();
            rn.carregaFiltros();
            rn.carregarTabelasCadastroAux();

            componentesEnabled(false);
            setLayoutTela();
            if (!isReponsive) {
                vboxCorChip.setWidth(260);
            }
            /* definir paginação */
            FGridModelos.setPageSize(PAGINACAO_SIZE);
            FGridModelos.setDbPaging(true);
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao abrir filtros de tela.", e);
        }
    }

    private void definirValoresFiltroDefault() {
        cbAtivosFiltro.setValue("S");
    }

    @Override
    public void btnPesquisaServicoClick(final Event event) {
        pesquisarPrincipal();
    }

    @Override
    public void cbGrupoFiltroClearClick(final Event event) {
        try {
            rn.carregaSubGrupo();
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao abrir filtros de SubGrupo.", e);
        }
    }

    @Override
    public void btnNovoClick(final Event event) {
        try {
            rn.carregaDadosServicos("");
            tbServicos.append();
            tbServicos.setATIVO("S");
            // limpaTela();
            pageControlServico.selectTab(tabCadastro);
            componentesEnabled(true);
            btnNovoAd.setEnabled(true);
            btnNovoModelos.setEnabled(true);
            lblDescServicVal.setVisible(false);
            lblDescServicImp.setVisible(false);
            lblDescServAd.setVisible(false);
            lblDescServicMod.setVisible(false);
            lblDescServicMarcas.setVisible(false);
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha a preparar a tela de cadastro.", e);
        }
    }

    public void FCombo1ClearClick(final Event event) {
        try {
            rn.carregaSubGrupoCadastro();
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao abrir filtros de SubGrupo.", e);
        }
    }

    private void limpaTela() {
        try {
            // Valores:
            FCombo3.clear();
            FDecTmoP.clear();
            FDecAgend.clear();
            FDecDuoTec.clear();
            FDecPrecVen.clear();
            FDecPrecCus.clear();
            FCombo2.clear();
            FDecimal1.clear();
            // Impostos:
            ckbRetemIrrfCad.setChecked(false);
            ckbRetemInssCad.setChecked(false);
            cknNRetemPCC.setChecked(false);
            FDecAliqIRRF.clear();
            FDecAliqInss.clear();
            cbClassReinfCad.clear();
            FStrCodPref.clear();
            FDecAliqRed.clear();
            // Adicionais:
            limparAdicionais();
            // Modelos:
            limpaModelos();
            rn.limpaCombos();
            nomeCliente = "";
            FIntCodCliente.clear();
            strPesqCad.clear();
            FIntCodMod.clear();
            FIntCodProduto.clear();
            FStrModeloPesq.clear();
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao abrir filtros de SubGrupo.", e);
        }
    }

    @Override
    public void btnCancelarClick(final Event event) {
        try {
            pageControlServico.selectTab(0);
            rn.carregaDadosServicos(tbConsultaServico.getCOD_SERVICO().asString());
            componentesEnabled(false);
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha não esperada no cancelamento da operação.", ex);
        }
    }

    private void componentesEnabled(Boolean isEnabled) {
        // __::Campos Cadastro::__
        ckbLavagemCad.setEnabled(isEnabled);
        FIntCod.setEnabled(isEnabled);
        FStrFabricante.setEnabled(isEnabled);
        chkOriginal.setEnabled(isEnabled);
        FCbCodigoLC.setEnabled(isEnabled);
        btnPesqCodNBS.setEnabled(isEnabled);
        FStrDesc.setEnabled(isEnabled);
        FMemDetalhe.setEnabled(isEnabled);
        FCoGrupoCad.setEnabled(isEnabled);
        FCoSubGrupoCad.setEnabled(isEnabled);
        FCoClassCad.setEnabled(isEnabled);
        FCoSetorCad.setEnabled(isEnabled);
        ckbTerCad.setEnabled(isEnabled);
        ckbLubCad.setEnabled(isEnabled);
        ckbTrocaOleoCad.setEnabled(isEnabled);
        ckbNForcarCad.setEnabled(isEnabled);
        ckbNApontarCad.setEnabled(isEnabled);
        ckbPassiRemeCad.setEnabled(isEnabled);
        ckbTerRemeCad.setEnabled(isEnabled);
        FStrAviso.setEnabled(isEnabled);
        ckbAtivo.setEnabled(isEnabled);

        // __::Botões ações::__
        gridServicos.setEnabled(!isEnabled);
        btnPesquisaServico.setEnabled(!isEnabled);
        btnNovo.setEnabled(!isEnabled);
        btnAlterar.setEnabled(!isEnabled);
        btnExcluir.setEnabled(!isEnabled);
        btnIndicadores.setEnabled(!isEnabled);
        btnSalvar.setEnabled(isEnabled);
        btnCancelar.setEnabled(isEnabled);


        // __::Valores::__
        FCombo3.setEnabled(isEnabled);
        FDecTmoP.setEnabled(isEnabled);
        FDecAgend.setEnabled(isEnabled);
        FDecDuoTec.setEnabled(isEnabled);
        FDecPrecVen.setEnabled(isEnabled);
        FDecPrecCus.setEnabled(isEnabled);
        FCombo2.setEnabled(isEnabled);
        FDecimal1.setEnabled(isEnabled);
        
        // __::Impostos::__
        ckbRetemIrrfCad.setEnabled(isEnabled);
        ckbRetemInssCad.setEnabled(isEnabled);
        cknNRetemPCC.setEnabled(isEnabled);
        RetemIrrfCadCheck(isEnabled);
        RetemInssCadCheck(isEnabled);
        gridEmpresasImposto.setEnabled(isEnabled);
        btnExcCodPref.setEnabled(isEnabled);
        btnInsCodPref.setEnabled(isEnabled);
        FStrCodPref.setEnabled(isEnabled);
        btnInsRedBase.setEnabled(isEnabled);
        btnExcRedBase.setEnabled(isEnabled);
        FDecAliqRed.setEnabled(isEnabled);
        gridServicosPrefeitura.setEnabled(isEnabled);
        gridServicosRedBcIss.setEnabled(isEnabled);
        
        // __::Adicionais::__
        servicosAdicionaisEnabled(false);
        btnNovoAd.setEnabled(isEnabled);
        btnAlterarrAd.setEnabled(isEnabled);
        
        // __::Modelos::__
        servicosModelosEnabled(false);
        btnNovoModelos.setEnabled(isEnabled);
        btnAlterarModelo.setEnabled(isEnabled);
        //btnFiltrarProdutoModelo.setEnabled(!isEnabled);
        //cbMarcaCad.setEnabled(!isEnabled);
        //cbFamiliaCad.setEnabled(!isEnabled);
        //cbModeloCad.setEnabled(!isEnabled);
        
        // cbMarcaCad.setEnabled(isEnabled);
        // cbFamiliaCad.setEnabled(isEnabled);
        // cbModeloCad.setEnabled(isEnabled);
        // FGrid5.setEnabled(isEnabled);
        // btnNovoModelos.setEnabled(isEnabled);
        // btnSalvarModelos.setEnabled(isEnabled);
        // btnCancelarModelos.setEnabled(isEnabled);
        // FStrModeloPesq.setEnabled(isEnabled);
        // btnPesqModelo.setEnabled(isEnabled);
        // FDecTMOPadraoMod.setEnabled(isEnabled);
        // FDecAgendaModelos.setEnabled(isEnabled);
        // FDecDuoModelos.setEnabled(isEnabled);
        // ckbAtivoModelo.setEnabled(isEnabled);
        // __::Marca::__
        FDualListMarca.setEnabled(isEnabled);
        pesqCadEnabled(isEnabled);
    }

    @Override
    public void btnSalvarClick(final Event event) {
        try {
            if (ckbAtivo.isChecked()) {
                tbServicos.setATIVO(ckbAtivo.getCheckedValue());
            } else {
                tbServicos.setATIVO(ckbAtivo.getUncheckedValue());
            }
            validarCamposDec();
            if (isValido) {

                rn.salvar(FIntCod.getValue().asString());

                /* testando salvar cursor extra */
                CrmServiceUtil util = new CrmServiceUtil();
                TFSchema sc = new TFSchema();
                sc.addTable(tbTemposPadroesTemp);
                util.salvar(sc);
                tbTemposPadroesTemp.commitUpdates();



                if (rn.existsServKit(tbServicos.getCOD_SERVICO().asString())) {
                    Dialog.create().title("Confirma").message("Deseja atualizar o TMO dos kit's relacionados a este serviço?").confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                String ret = rn.atualizarTmoKit(tbServicos.getCOD_SERVICO().asString(), tbServicos.getTEMPO_PADRAO().asDecimal());
                                if (!ret.equals("S")) {
                                    CrmServiceUtil.showWarning(ret);
                                }
                            } catch (DataException ex) {
                                CrmServiceUtil.showError("Falha ao atualizar TMO Kit.", ex);
                            }
                        }
                    });
                }
                componentesEnabled(false);
                pageControlServico.selectTab(0);
            }
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao tentar salvar.", e);
        }
    }

    private void pesqCadEnabled(Boolean isEnabled) {
        if (ckbTerCad.isChecked()) {
            // strPesqCad.setEnabled(isEnabled);
            strPesqCad.setEnabled(false);
            FBtnPesquisar.setEnabled(isEnabled);
        } else {
            strPesqCad.setEnabled(false);
            FBtnPesquisar.setEnabled(false);
            strPesqCad.clear();
            nomeCliente = "";
            FIntCodCliente.clear();
            strPesqCad.clear();
            FIntCodCliente.clear();
        }
    }

    @Override
    public void ckbTerCadCheck(final Event event) {
        pesqCadEnabled(true);
    }

    @Override
    public void FBtnPesquisarClick(final Event event) {
        FrmPesquisarClienteA form = new FrmPesquisarClienteA();
        form.gridEndereco.setVisible(Boolean.FALSE);
        form.FHBox6.setVisible(Boolean.FALSE);
        form.gridFrotaAssociada.setVisible(Boolean.FALSE);
        FormUtil.doModal(form, (EventListener) t -> {
            if (form.isOk()) {
                tbServicos.setCOD_CLIENTE(form.tbPesqClienteCad.getCOD_CLIENTE());
                nomeCliente = form.tbPesqClienteCad.getNOME().asString();
                strPesqCad.setValue(nomeCliente);
            }
        });
    }

    private void setLayoutTela() {
        lblDescServicVal.setFontColor("#7cb5ec");
        lblTempo.setFontColor("#7cb5ec");
        FLabel13.setFontColor("#7cb5ec");
        FLabel16.setFontColor("#7cb5ec");
        lblDescServicImp.setFontColor("#7cb5ec");
        lblDescServAd.setFontColor("#7cb5ec");
        lblDescServicMod.setFontColor("#7cb5ec");
        lblDescServicMarcas.setFontColor("#7cb5ec");
    }

    private void carregaValoresServico() {
        lblDescServicVal.setVisible(true);
        lblDescServicImp.setVisible(true);
        lblDescServAd.setVisible(true);
        lblDescServicMod.setVisible(true);
        lblDescServicMarcas.setVisible(true);
        lblDescServicVal.setCaption(tbConsultaServico.getCOD_SERVICO().asString() + "-" + tbConsultaServico.getSERVICO().asString());
        lblDescServicImp.setCaption(tbConsultaServico.getCOD_SERVICO().asString() + "-" + tbConsultaServico.getSERVICO().asString());
        lblDescServAd.setCaption(tbConsultaServico.getCOD_SERVICO().asString() + "-" + tbConsultaServico.getSERVICO().asString());
        lblDescServicMod.setCaption(tbConsultaServico.getCOD_SERVICO().asString() + "-" + tbConsultaServico.getSERVICO().asString());
        lblDescServicMarcas.setCaption(tbConsultaServico.getCOD_SERVICO().asString() + "-" + tbConsultaServico.getSERVICO().asString());
        if (FIntCodCliente != null) {
            strPesqCad.setValue(tbConsultaServico.getFORNECEDOR_TERCEIRO().asString());
        }
        if (FStrCodNBS != null) {
            FStrCodNbsDesc.setValue(tbConsultaServico.getDESC_COD_NBS().asString());
        }
    }

    @Override
    public void cbMarcaCadChange(final Event event) {
        try {
            rn.carregaProdutosCadastroFiltro(cbMarcaCad.getValue().asInteger());
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao carregar Família.", e);
        }
    }

    @Override
    public void cbMarcaCadClearClick(final Event event) {
        try {
            rn.carregaProdutosCadastroFiltro(0);
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao buscar dados do serviço.", e);
        }
    }

    @Override
    public void cbFamiliaCadChange(final Event event) {
        try {
            rn.carregarProdutosModelosCadastroFiltro(cbFamiliaCad.getValue().asInteger());
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao carregar Modelo.", e);
        }
    }

    @Override
    public void cbFamiliaCadClearClick(final Event event) {
        try {
            rn.carregarProdutosModelosCadastroFiltro(0);
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao carregar Modelo.", e);
        }
    }

    @Override
    public void btnNovoAdClick(final Event event) {
        try {
            tbServicosAdicionais.append();
            servicosAdicionaisEnabled(true);
            FString2.setFocus();
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao abrir Serviços adicionais", e);
        }
    }

    @Override
    public void btnSalvarAdClick(final Event event) {
        try {
            if (FIntCod.getValue().asString() != "") {
                if (FDecTmoAdic.getValue().asString() != "") {
                    tbServicosAdicionais.setCOD_SERVICO(FIntCod.getValue().asString());
                    tbServicosAdicionais.post();
                    servicosAdicionaisEnabled(false);
                    btnNovoAd.setEnabled(true);
                    FGridAdicionais.setEnabled(true);
                } else {
                    Dialog.create().title("CrmService").message("Não permitido TMO padrão vazio.").showInformation(((EventListener) event1 -> {
                        FDecTmoAdic.setFocus();
                    }));
                }
            } else {
                CrmServiceUtil.showMessage("Necessário Inserir Código do Serviço.");
            }
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao adicionar", ex);
        }
    }

    @Override
    public void btnCancelAdClick(final Event event) {
        try {
            tbServicosAdicionais.cancel();
            // tbServicosAdicionais.cancelUpdates();
            servicosAdicionaisEnabled(false);
            btnNovoAd.setEnabled(true);
            FGridAdicionais.setEnabled(true);
            tbServicosAdicionais.first();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao cancelar.", ex);
        }
    }

    private void limparAdicionais() {
        FString2.clear();
        FDecTmoAdic.clear();
        FDecPrecVenAdic.clear();
        FDecPrecoCustoAdic.clear();
        servicosAdicionaisEnabled(false);
    }

    private void servicosAdicionaisEnabled(Boolean isEnabled) {
        FGridAdicionais.setEnabled(isEnabled);
        btnNovoAd.setEnabled(!isEnabled);
        btnAlterarrAd.setEnabled(!isEnabled);
        btnSalvarAd.setEnabled(isEnabled);
        btnCancelAd.setEnabled(isEnabled);
        FString2.setEnabled(isEnabled);
        FDecTmoAdic.setEnabled(isEnabled);
        FDecPrecVenAdic.setEnabled(isEnabled);
        FDecPrecoCustoAdic.setEnabled(isEnabled);
    }

    @Override
    public void btnAlterarClick(final Event event) {
        if (!tbServicos.isEmpty()) {
            componentesEnabled(true);
            FGridAdicionais.setEnabled(true);
            btnNovoAd.setEnabled(true);
            FGridModelos.setEnabled(true);
            btnNovoModelos.setEnabled(true);
            FIntCod.setEnabled(false);
            try {
                tbServicos.edit();
            } catch (DataException ex) {
                CrmServiceUtil.showError("Falha ao entrar em modo edição", ex);
            }
            if (pageControlServico.getSelectedTab().equals(tabSheetListagem)) {
                pageControlServico.selectTab(tabCadastro);
            }
        } else {
            CrmServiceUtil.showMessage("Nenhum Serviço selecionado para edição");
        }
    }

    @Override
    public void btnExcCodPrefClick(final Event event) {
        try {
            tbServicosPrefeitura.delete();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao deletar código de serviço", ex);
        }
    }

    @Override
    public void btnInsCodPrefClick(final Event event) {
        try {
            if (FIntCod.getValue().asString() != "") {
                if (FStrCodPref.getValue().asString() != "") {
                    if (rn.validaChaveServPref(tbEmpresas.getCOD_EMPRESA().asInteger(), FIntCod.getValue().asString())) {
                        tbServicosPrefeitura.append();
                        tbServicosPrefeitura.setCOD_EMPRESA(tbEmpresas.getCOD_EMPRESA().asInteger());
                        tbServicosPrefeitura.setCOD_SERVICO_PREFEITURA(FStrCodPref.getValue().asString());
                        tbServicosPrefeitura.setCOD_SERVICO(FIntCod.getValue().asString());
                        tbServicosPrefeitura.setEMPRESA(tbEmpresas.getCOD_EMPRESA().asString() + "-" + tbEmpresas.getNOME().asString());
                        tbServicosPrefeitura.post();
                        // FStrCodPref.clear();
                    }
                } else {
                    FreedomUtilities.invokeLater(() -> {
                        FStrCodPref.setFocus();
                    });
                    CrmServiceUtil.showMessage("Necessário Inserir Cod. Prefeitura.");
                }
            } else {
                FreedomUtilities.invokeLater(() -> {
                    FIntCod.setFocus();
                });
                CrmServiceUtil.showMessage("Necessário Inserir Código do Serviço.");
            }
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao adicionar Cod. Serviço", ex);
        }
    }

    @Override
    public void pageControlServicoChange(Event<Object> event) {
        boolean isVindoDaAbaListagem = tabSheetListagem.equals(pageControlServico.getTabSheet((int) event.getOldValue()));
        boolean isNaoEstaAlterando = !isAlterando();
        boolean isServicoListagemDiferenteServicoCadastro = !tbConsultaServico.getCOD_SERVICO().asString().equals(tbServicos.getCOD_SERVICO().asString());
        if (isNaoEstaAlterando && isVindoDaAbaListagem && isServicoListagemDiferenteServicoCadastro){
            try {
                rn.carregaDadosServicos(tbConsultaServico.getCOD_SERVICO().asString());
            } catch (DataException e) {
                CrmServiceUtil.showError("Falha ao carregar dados do serviço.", e);
            }
        }
    }

    @Override
    public void btnInsRedBaseClick(final Event event) {
        try {
            if (FIntCod.getValue().asString() != "") {
                if (FDecAliqRed.getValue().asString() != "") {
                    if (FDecAliqRed.getValue().asDecimal() < 0) {
                        // CrmServiceUtil.showMessage("Não permitido Aliq. Redução Base ISS negativo");
                        Dialog.create().title("CrmService").message("Não permitido Aliq. Redução Base ISS negativo").showInformation(((EventListener) event1 -> {
                            pageControlServico.selectTab(3);
                            FDecAliqRed.setFocus();
                        }));
                        isValido = false;
                        return;
                    }
                    if (SystemUtil.retiraLetras(FDecAliqRed.getValue().asString(",##0.00")).length() > 5) {
                        Dialog.create().title("CrmService").message("Tamanho máximo da  Aliq. Redução Máximo 5 números").showInformation(((EventListener) event1 -> {
                            pageControlServico.selectTab(3);
                            FDecAliqRed.setFocus();
                        }));
                        isValido = false;
                        return;
                    }
                    if (rn.validaChaveServRedBcIss(tbEmpresas.getCOD_EMPRESA().asInteger(), FIntCod.getValue().asString())) {
                        tbServicosRedBcIss.append();
                        tbServicosRedBcIss.setCOD_EMPRESA(tbEmpresas.getCOD_EMPRESA().asInteger());
                        tbServicosRedBcIss.setCOD_SERVICO(FIntCod.getValue().asString());
                        tbServicosRedBcIss.setALIQ_RED_BC_ISS(FDecAliqRed.getValue().asInteger());
                        tbServicosRedBcIss.setEMPRESA(tbEmpresas.getCOD_EMPRESA().asString() + "-" + tbEmpresas.getNOME().asString());
                        tbServicosRedBcIss.post();
                        // FDecAliqRed.clear();
                    }
                } else {
                    FreedomUtilities.invokeLater(() -> {
                        FDecAliqRed.setFocus();
                    });
                    CrmServiceUtil.showMessage("Necessário Inserir Aliq. Redução de base.");
                }
            } else {
                FreedomUtilities.invokeLater(() -> {
                    FIntCod.setFocus();
                });
                CrmServiceUtil.showMessage("Necessário Inserir Aliq. Redução de base.");
            }
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao adicionar Redução de base", ex);
        }
    }

    @Override
    public void btnExcRedBaseClick(final Event event) {
        try {
            tbServicosRedBcIss.delete();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao deletar Redução de base", ex);
        }
    }

    private void servicosModelosEnabled(Boolean isEnabled) {
        FGridModelos.setEnabled(isEnabled);
        btnNovoModelos.setEnabled(!isEnabled);
        btnAlterarModelo.setEnabled(!isEnabled);
        btnSalvarModelos.setEnabled(isEnabled);
        btnCancelarModelos.setEnabled(isEnabled);
        FStrModeloPesq.setEnabled(false);
        btnPesqModelo.setEnabled(isEnabled);
        FDecTMOPadraoMod.setEnabled(isEnabled);
        FDecAgendaModelos.setEnabled(isEnabled);
        FDecDuoModelos.setEnabled(isEnabled);
        ckbAtivoModelo.setEnabled(isEnabled);
    }

    @Override
    public void btnNovoModelosClick(final Event event) {
        try {
            tbTemposPadroesValida.copyFrom(tbTemposPadroes, false, true);
            tbTemposPadroes.first();
            tbTemposPadroes.cancel();
            tbTemposPadroes.append();
            servicosModelosEnabled(true);
            ckbAtivoModelo.setChecked(true);
            modeModelo = "APPEND";
            FIntCodMod.clear();
            FIntCodProduto.clear();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha na tentativa de atualizar o Cod. Serviço.", ex);
        }
    }

    @Override
    public void btnSalvarModelosClick(final Event event) {
        try {
            if (validaAddModelo()) {
                CrmServiceUtil.showMessage("CrmService", "Modelo já Cadastrado para este Serviço!");
                return;
            }
            if (FIntCod.getValue().asString() != "") {
                if (FDecTMOPadraoMod.getValue().asString() != "") {
                    if (FDecTMOPadraoMod.getValue().asDecimal() <= FDecAgendaModelos.getValue().asDecimal() || FDecTMOPadraoMod.getValue().asDecimal() <= FDecDuoModelos.getValue().asDecimal()) {
                        Dialog.create().title("CrmService").message("Campo do TMO padrão deve ser maior TMO Agenda e TMO Dua Tec.").showInformation(((EventListener) event1 -> {
                            FDecTMOPadraoMod.setFocus();
                        }));
                    } else {
                        tbTemposPadroes.disableControls();
                        tbTemposPadroes.disableMasterTable();
                        tbTemposPadroes.setCOD_SERVICO(FIntCod.getValue().asString());
                        tbTemposPadroes.setMODELO(FStrModeloPesq.getValue());
                        tbTemposPadroes.setATIVO2(ckbAtivoModelo.getCheckedValue());
                        tbTemposPadroes.post();
                        tbTemposPadroes.enableControls();
                        tbTemposPadroes.enableMasterTable();
                        TableUtil.inserirRegistro(tbTemposPadroesTemp, tbTemposPadroes.toRowType());

                        //tbTemposPadroesTemp.addRowType(tbTemposPadroes.toRowType());
                        servicosModelosEnabled(false);
                        btnNovoModelos.setEnabled(true);
                        FGridModelos.setEnabled(true);
                    }
                } else {
                    Dialog.create().title("CrmService").message("Preenchimento do TMO padrão obrigatório.").showInformation(((EventListener) event1 -> {
                        FDecTMOPadraoMod.setFocus();
                    }));
                }
            } else {
                Dialog.create().title("CrmService").message("Necessário Inserir Código do Serviço.").showInformation(((EventListener) event1 -> {
                    FIntCod.setFocus();
                }));
            }
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao adicionar", ex);
        }
    }

    private void limpaModelos() {
        cbMarcaCad.clear();
        cbFamiliaCad.clear();
        cbModeloCad.clear();
        FStrModeloPesq.clear();
        FDecTMOPadraoMod.clear();
        FDecAgendaModelos.clear();
        FDecDuoModelos.clear();
        ckbAtivoModelo.setChecked(true);
    }

    @Override
    public void btnPesqModeloClick(final Event event) {
        FrmSelecionarVeiculoA form = new FrmSelecionarVeiculoA();
        form.podeSelecionarVeiculo(tbProdutos.getCOD_PRODUTO().asInteger(), tbProdutosModelos.getCOD_MODELO().asInteger());
        FormUtil.doModal(form, (EventListener) t -> {
            if (form.isOk()) {
                tbTemposPadroes.setCOD_MODELO(form.tbProdutosModelos.getCOD_MODELO());
                tbTemposPadroes.setCOD_PRODUTO(form.tbProdutosModelos.getCOD_PRODUTO());
                nomeModelo = form.tbProdutosModelos.getDESCRICAO_MODELO().asString();
                FStrModeloPesq.setValue(nomeModelo);
                FIntCodProduto.setValue(form.tbProdutosModelos.getCOD_PRODUTO());
                FIntCodMod.setValue(form.tbProdutosModelos.getCOD_MODELO());
            }
        });
    }

    @Override
    public void tbTemposPadroesAfterScroll(Event<Object> event) {
        FStrModeloPesq.setValue(tbTemposPadroes.getMODELO().asString());
    }

    @Override
    public void tbServicoMarcaBeforePost(Event<Object> event) {
        try {
            rn.aplicaDescServMarca();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao inserir Marca.", ex);
        }
    }

    @Override
    public void tbConcessionariaTipoBeforePost(Event<Object> event) {
        try {
            tbConcessionariaTipo.edit();
            tbConcessionariaTipo.setDESCRICAO2(tbServicoMarca.getDESC_CONCESSIONARIA_TIPO2().asString());
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao excluir Marca.", ex);
        }
    }

    @Override
    public void FCoGrupoCadClearClick(Event<Object> event) {
        tbServicosSubGrupoCadastro.close();
        tbServicosSubGrupoCadastro.clearFilters();
        tbServicosSubGrupoCadastro.clearParams();
    }

    @Override
    public void btnPesqCodNBSClick(final Event event) {
        FrmTabelaNBSA form = new FrmTabelaNBSA();
        FormUtil.doModal(form, (EventListener) t -> {
            if (form.isOk()) {
                tbServicos.setCOD_NBS(form.tbNbs.getCODIGO());
                FStrCodNbsDesc.setValue(form.tbNbs.getDESCRICAO().asString());
            }
        });
    }

    @Override
    public void btnExcluirClick(final Event event) {
        try {
            if (!tbServicos.isEmpty()) {
                rn.deletarServicoFilhos();
                pesquisarPrincipal();
            } else {
                CrmServiceUtil.showMessage("Nenhum Serviço selecionado para exclusão.");
            }
        } catch (DataException ex) {
            CrmServiceUtil.showError("Erro ao excluir o serviço.", ex);
        }
    }

    private void pesquisarPrincipal() {
        try {
            if (!rn.pesqServicos(cbGrupoFiltro.getValue().asInteger(), cbSbGrupoFiltro.getValue().asInteger(), cbSetorFiltro.getValue().asInteger(),
                    cbClassifFiltro.getValue().asInteger(), cbAtivosFiltro.getValue().asString(), cbOriginaisFiltro.getValue().asString(),
                    cbCobrarFiltro.getValue().asString(), nbNumeroOS.getValue().asInteger(), ckbTerceiros.isChecked(), ckbTrocaOleo.isChecked(),
                    ckbLubrificacao.isChecked(), ckbServicoTZero.isChecked(), ckbLavagem.isChecked(), ckbAdicionais.isChecked(), ckbMarca.isChecked(),
                    ckbTMO.isChecked(), ckbForcar.isChecked(), ckbNPermitir.isChecked(), ckbRemessa.isChecked(), ckbPassRemessa.isChecked(), ckbNaoReter.isChecked(),
                    ckbRetemINSS.isChecked(), ckbRetemIRRF.isChecked(), ckbBmwIspa.isChecked(), ckbAutFabrica.isChecked(), FStrCodServFiltro.getValue().asString(),
                    FStrDescFiltro.getValue().asString(), ckbNRetemPccFiltro.isChecked(), chkLikeDescricao.isChecked())) {
                // FBorderPanel1.setOpen("E", true);
                // Lógica próvisória até atualizar o ZK, depois retornar a linha acima
                ((Borderlayout) FBorderPanel1.getImpl()).getEast().setSlide(true);
                FStrDescFiltro.setFocus();
            }
            carregaValoresServico();
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao pesquisar os Serviços", e);
        }
    }

    private void validarCamposDec() {
        // Cadastro
        if (FIntCod.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório do Cod. Seviço").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(1);
                FIntCod.setFocus();
            }));
            isValido = false;
            return;
        }

        if (RowState.INSERTED.equals(tbServicos.getRowState())) {
            boolean jaExisteServicoComCodigo = false;
            try {
                SERVICOS tbExisteServico = new SERVICOS("ExisteServico");
                tbExisteServico.close();
                tbExisteServico.setFilterCOD_SERVICO(FIntCod.getValue().asString());
                tbExisteServico.open();
                jaExisteServicoComCodigo = tbExisteServico.count() > 0;
            } catch (DataException e) {
                CrmServiceUtil.showError("Falha ao Validar Servico", e);
            }
            if (jaExisteServicoComCodigo) {
                Dialog.create().title("CrmService").message("Já existe um Serviço com esse código.").showInformation(((EventListener) event1 -> {
                    pageControlServico.selectTab(1);
                    FIntCod.setFocus();
                }));
                isValido = false;
                return;
            }
        }

        if (FStrDesc.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório da Descrição").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(1);
                FStrDesc.setFocus();
            }));
            isValido = false;
            return;
        }

        if (FCoGrupoCad.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório do Grupo").showInformation(((EventListener) event1 -> {
                FreedomUtilities.invokeLater(() -> {
                    pageControlServico.selectTab(1);
                    FCoGrupoCad.setFocus();
                    FCoGrupoCad.setOpen(true);
                });
            }));
            isValido = false;
            return;
        }

        if (FCoSubGrupoCad.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório do subGrupo").showInformation(((EventListener) event1 -> {
                FreedomUtilities.invokeLater(() -> {
                    pageControlServico.selectTab(1);
                    FCoSubGrupoCad.setFocus();
                    FCoSubGrupoCad.setOpen(true);
                });
            }));
            isValido = false;
            return;
        }

        if (FCoSetorCad.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório do Setor").showInformation(((EventListener) event1 -> {
                FreedomUtilities.invokeLater(() -> {
                    pageControlServico.selectTab(1);
                    FCoSetorCad.setFocus();
                    FCoSetorCad.setOpen(true);
                });
            }));
            isValido = false;
            return;
        }

        if (FCbCodigoLC.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório do Codigo LC 116/03").showInformation(((EventListener) event1 -> {
                FreedomUtilities.invokeLater(() -> {
                    pageControlServico.selectTab(1);
                    FCbCodigoLC.setFocus();
                    FCbCodigoLC.setOpen(true);
                });
            }));
            isValido = false;
            return;
        }
        // VALOR
        if  (FCombo3.getValue().asString().equals("")) {
            Dialog.create().title("CrmService").message("Preenchimento obrigarório de como cobrar").showInformation(((EventListener) event1 -> {
                FreedomUtilities.invokeLater(() -> {
                    pageControlServico.selectTab(2);
                    FCombo3.setFocus();
                    FCombo3.setOpen(true);
                });
            }));
            isValido = false;
            return;
        }

        if (FDecTmoP.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido TMO Padrão negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecTmoP.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecTmoP.getValue().asDecimal() == 0) {
            Dialog.create().title("CrmService").message("Campo TMO Padrão Obrigatório.").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecTmoP.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecTmoP.getValue().asDecimal() <= FDecAgend.getValue().asDecimal() || FDecTmoP.getValue().asDecimal() <= FDecDuoTec.getValue().asDecimal()) {
            Dialog.create().title("CrmService").message("Campo TMO Padrão deve ser maior que Agenda Premium e Dua Tec.").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecTmoP.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecTmoP.getValue().asString(",##0.00000")).length() > 12) {
            Dialog.create().title("CrmService").message("Tamanho máximo do Tempo Padrão excedido. Máximo 12 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecTmoP.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecAgend.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Agenda Premium negativo").showInformation((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecAgend.setFocus();
            });
            isValido = false;
            return;
        }
        if (FDecAgend.getValue().asDecimal() == 0) {
            Dialog.create().title("CrmService").message("Campo Agenda Premium Obrigatório.").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecAgend.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecAgend.getValue().asString(",##0.00")).length() > 6) {
            Dialog.create().title("CrmService").message("Tamanho máximo do Tempo Agenda excedido. Máximo 6 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecAgend.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecDuoTec.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Dua Tec negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecDuoTec.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecDuoTec.getValue().asString(",##0.00")).length() > 6) {
            Dialog.create().title("CrmService").message("Tamanho máximo Duo Tec excedido. Máximo 6 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecDuoTec.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecPrecVen.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Preço Venda negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecPrecVen.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecPrecVen.getValue().asString(",##0.00")).length() > 12) {
            Dialog.create().title("CrmService").message("Tamanho máximo do Preço Venda excedido. Máximo 12 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecPrecVen.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FCombo3.getValue().asString().equals("P")) {
            if (FDecPrecVen.getValue().asDecimal() == 0) {
                Dialog.create().title("CrmService").message("Campo Preço Venda obrigatório se o Como Pagar for Por Preço Tabelado").showInformation(((EventListener) event1 -> {
                    pageControlServico.selectTab(2);
                    FDecPrecVen.setFocus();
                }));
                isValido = false;
                return;
            } else if (FDecPrecVen.getValue().asDecimal() <= FDecPrecCus.getValue().asDecimal()) {
            }
        }
        if (FDecPrecCus.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Preço Custo negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecPrecCus.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecPrecCus.getValue().asString(",##0.00")).length() > 12) {
            Dialog.create().title("CrmService").message("Tamanho máximo do Preço Custo excedido. Máximo 12 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecPrecCus.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FCombo3.getValue().asString().equals("P")) {
            if (FDecPrecCus.getValue().asDecimal() == 0) {
                Dialog.create().title("CrmService").message("Campo Preço Custo obrigatório se o Como Pagar for Por Preço Tabelado").showInformation(((EventListener) event1 -> {
                    pageControlServico.selectTab(2);
                    FDecPrecCus.setFocus();
                }));
                isValido = false;
                return;
            }
        }
        if (FDecimal1.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Valor Comissão negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecimal1.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecimal1.getValue().asString(",##0.00")).length() > 15) {
            Dialog.create().title("CrmService").message("Tamanho máximo da Comissão excedido. Máximo 15 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(2);
                FDecimal1.setFocus();
            }));
            isValido = false;
            return;
        }
        // Imposto
        if (FDecAliqIRRF.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Aliq. IRRF negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(3);
                FDecAliqIRRF.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecAliqIRRF.getValue().asString(",##0.00")).length() > 5) {
            Dialog.create().title("CrmService").message("Tamanho máximo da Comissão excedido. Máximo 5 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(3);
                FDecAliqIRRF.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecAliqInss.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Aliq. INSS negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(3);
                FDecAliqInss.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecAliqInss.getValue().asString(",##0.00")).length() > 5) {
            Dialog.create().title("CrmService").message("Tamanho máximo da  Aliq. INSS. Máximo 5 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(3);
                FDecAliqInss.setFocus();
            }));
            isValido = false;
            return;
        }
        // Adicional
        if (FDecTmoAdic.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido TMO Padrão Adicional negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(4);
                FDecTmoAdic.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecTmoAdic.getValue().asString(",##0.00")).length() > 6) {
            Dialog.create().title("CrmService").message("Tamanho máximo da  TMO adicional Máximo 6 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(4);
                FDecTmoAdic.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecPrecVenAdic.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Preço Venda Adicional negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(4);
                FDecPrecVenAdic.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecPrecVenAdic.getValue().asString(",##0.00")).length() > 12) {
            Dialog.create().title("CrmService").message("Tamanho máximo da  Preço Venda Adicional Máximo 12 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(4);
                FDecPrecVenAdic.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecPrecoCustoAdic.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido Preço Custo Adicional negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(4);
                FDecPrecoCustoAdic.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecPrecoCustoAdic.getValue().asString(",##0.00")).length() > 12) {
            Dialog.create().title("CrmService").message("Tamanho máximo da  Preço Custo Adicional Máximo 12 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(4);
                FDecPrecoCustoAdic.setFocus();
            }));
            isValido = false;
            return;
        }
        // Modelo
        if (FDecTMOPadraoMod.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido TMO Padrão Modelo negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(5);
                FDecTMOPadraoMod.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecTMOPadraoMod.getValue().asString(",##0.00000")).length() > 12) {
            Dialog.create().title("CrmService").message("Tamanho máximo da TMO Padrão Modelo Máximo 12 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(5);
                FDecTMOPadraoMod.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecAgendaModelos.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido TMO Agenda Modelo negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(5);
                FDecAgendaModelos.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecAgendaModelos.getValue().asString(",##0.00")).length() > 6) {
            Dialog.create().title("CrmService").message("Tamanho máximo da  TMO Agenda Modelo Máximo 6 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(5);
                FDecAgendaModelos.setFocus();
            }));
            isValido = false;
            return;
        }
        if (FDecDuoModelos.getValue().asDecimal() < 0) {
            Dialog.create().title("CrmService").message("Não permitido TMO Dua Tec Modelo negativo").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(5);
                FDecDuoModelos.setFocus();
            }));
            isValido = false;
            return;
        }
        if (SystemUtil.retiraLetras(FDecDuoModelos.getValue().asString(",##0.00")).length() > 6) {
            Dialog.create().title("CrmService").message("Tamanho máximo da  TMO Dua Tec Máximo 6 números").showInformation(((EventListener) event1 -> {
                pageControlServico.selectTab(5);
                FDecDuoModelos.setFocus();
            }));
            isValido = false;
            return;
        }
        isValido = true;
    }

    @Override
    public void BtnLimparFiltrosClick(final Event event) {
        cbGrupoFiltro.clear();
        cbSbGrupoFiltro.clear();
        cbSetorFiltro.clear();
        cbClassifFiltro.clear();
        cbAtivosFiltro.setValue("S");
        cbOriginaisFiltro.clear();
        nbNumeroOS.clear();
        FStrCodServFiltro.clear();
        FStrDescFiltro.clear();
        ckbTerceiros.clear();
        ckbTrocaOleo.clear();
        ckbLubrificacao.clear();
        ckbLavagem.clear();
        ckbServicoTZero.clear();
        ckbMarca.clear();
        ckbAdicionais.clear();
        ckbTMO.clear();
        ckbNPermitir.clear();
        ckbForcar.clear();
        ckbRemessa.clear();
        ckbPassRemessa.clear();
        ckbNaoReter.clear();
        ckbRetemIRRF.clear();
        ckbRetemINSS.clear();
        ckbBmwIspa.clear();
        ckbAutFabrica.clear();
    }

    @Override
    public void ckbRetemIrrfCadCheck(Event<Object> event) {
        RetemIrrfCadCheck(true);
    }

    private void RetemIrrfCadCheck(Boolean isEnabled) {
        if (!isEnabled) {
            FDecAliqIRRF.setEnabled(false);
        } else {
            if (ckbRetemIrrfCad.isChecked()) {
                FDecAliqIRRF.setEnabled(true);
            } else {
                FDecAliqIRRF.clear();
                FDecAliqIRRF.setEnabled(false);
            }
        }
    }

    @Override
    public void ckbRetemInssCadCheck(Event<Object> event) {
        RetemInssCadCheck(true);
    }

    private void RetemInssCadCheck(Boolean isEnabled) {
        if (!isEnabled) {
            FDecAliqInss.setEnabled(false);
            cbClassReinfCad.setEnabled(false);
        } else {
            if (ckbRetemInssCad.isChecked()) {
                FDecAliqInss.setEnabled(true);
                cbClassReinfCad.setEnabled(true);
            } else {
                FDecAliqInss.clear();
                FDecAliqInss.setEnabled(false);
                cbClassReinfCad.clear();
                cbClassReinfCad.setEnabled(false);
            }
        }
    }

    @Override
    public void btnIndicadoresClick(final Event event) {
        FrmCursoresServicoA form = new FrmCursoresServicoA();
        FormUtil.doShow(form, (EventListener) t -> {
        });
    }

    @Override
    public void FStrDescFiltroEnter(Event<Object> event) {
        btnPesquisaServicoClick(null);
        ((Borderlayout) FBorderPanel1.getImpl()).getEast().setSlide(false);
    }

    @Override
    public void FDecAliqRedExit(Event<Object> event) {
        if (FDecAliqRed.getValue().asDecimal() < 0) {
            CrmServiceUtil.showMessage("Aliquota não pode ser menor que 0. Verifique!");
            FDecAliqRed.setFocus();
        } else if (FDecAliqRed.getValue().asDecimal() > 99.99) {
            CrmServiceUtil.showMessage("Aliquota não pode ser maior que 99,99. Verifique!");
            FDecAliqRed.setFocus();
        }
    }

    private void exportExcel(TFTable table, TFGrid grid) {
        try {
            gridUtil.exportarExcel(table, grid);
        } catch (DataException e) {
            CrmServiceUtil.showError("Erro ao exportar excel. Motivo: ", e);
        }
    }

    @Override
    public void miExportarExcelServicosClick(final Event event) {
        exportExcel(tbConsultaServico, gridServicos);
    }

    @Override
    public void tbConsultaServicoAfterScroll(Event<Object> event) {
        /*try {
            if (!tbConsultaServico.getCOD_SERVICO().asString().equals("")) {
                rn.carregaDadosServicos(tbConsultaServico.getCOD_SERVICO().asString());
            }
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao buscar dados do serviço.", e);
        }*/
    }

    @Override
    public void miGridEmpresasImpostoClick(final Event event) {
        exportExcel(tbEmpresas, gridEmpresasImposto);
    }

    @Override
    public void miGridServicosPrefeituraClick(final Event event) {
        exportExcel(tbServicosPrefeitura, gridServicosPrefeitura);
    }

    @Override
    public void miGridServicosRedBcIssClick(final Event event) {
        exportExcel(tbServicosRedBcIss, gridServicosRedBcIss);
    }

    @Override
    public void miGridModelosClick(final Event event) {
        exportExcel(tbTemposPadroes, FGridModelos);
    }

    @Override
    public void btnAlterarAdClick(final Event<Object> event) {
        if (tbServicosAdicionais.getCOD_SERVICO().asString().equals("")) {
            return;
        }
        try {
            servicosAdicionaisEnabled(true);
            tbServicosAdicionais.edit();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha realizar edição do modelo.", ex);
        }
    }

    @Override
    public void btnAlterarModeloClick(final Event<Object> event) {
        try {
            servicosModelosEnabled(true);
            tbTemposPadroes.edit();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha realizar edição do modelo.", ex);
        }
    }

    @Override
    public void btnCancelarModelosClick(final Event<Object> event) {
        try {
            tbTemposPadroes.cancel();
            limpaModelos();
            servicosModelosEnabled(false);
            btnNovoModelos.setEnabled(true);
            tbTemposPadroes.first();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao cancelar.", ex);
        }
    }

    @Override
    public void FGridModelosExcluirModeloClick(Event<Object> event) {
        try {
            tbTemposPadroes.delete();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao excluir Modelo.", ex);
        }
    }

    @Override
    public void FGridAdicionaisExcluirAdClick(Event<Object> event) {
        try {
            tbServicosAdicionais.delete();
        } catch (DataException ex) {
            CrmServiceUtil.showError("Falha ao deletar Serviço.", ex);
        }
    }

    @Override
    public void FHBox20Click(final Event<Object> event) {

    }

    @Override
    public void vBoxTabCadastroClick(final Event<Object> event) {

    }

    @Override
    public void cbCorChipChange(Event<Object> event) {
        if (!tbConsultaServico.isEmpty()) {
            Dialog.create()
                    .title("Confirma")
                    .message("Deseja atualizar a Classificação para TODOS Serviços Selecionado na grid?")
                    .confirmSimNao((String dialogResult) -> {
                        if (CastUtil.asInteger(dialogResult) == IDialog.YES) {
                            try {
                                tbConsultaServico.first();
                                while (!tbConsultaServico.eof()) {
                                    tbServicos.edit();
//                                    tbServicos.setCOD_SERVICO(tbConsultaServico.getCOD_SERVICO());
                                    tbServicos.setCOD_CLASSIFICACAO(cbCorChip.getValue().asInteger());
                                    tbServicos.post();
                                    tbServicos.applyUpdates();

                                    tbConsultaServico.next();
                                }
                                tbServicos.commitUpdates();
                            } catch (DataException ex) {
                                CrmServiceUtil.showError("Falha ao atualizar a Classificação dos Servicos.", ex);
                            }
                        }
                    });
        }
    }

    @Override
    public void cbCorChipClearClick(Event<Object> event) {
        cbCorChip.clear();
    }

    private boolean validaAddModelo() {
        boolean jaExiste = false;
        try {
            String codServ = FIntCod.getValue().asString();
            Integer codProd = FIntCodProduto.getValue().asInteger();
            Integer codMod = FIntCodMod.getValue().asInteger();

            if (modeModelo.equals("APPEND")) {
                tbTemposPadroesValida.first();
                while (!tbTemposPadroesValida.eof()) {
                    if (tbTemposPadroesValida.getCOD_SERVICO().asString().equals(codServ) &&
                            tbTemposPadroesValida.getCOD_PRODUTO().asInteger().equals(codProd) &&
                            tbTemposPadroesValida.getCOD_MODELO().asInteger().equals(codMod)) {
                        jaExiste = true;
                        break;
                    }
                    tbTemposPadroesValida.next();
                }
            }
        } catch (DataException ex) {
            CrmServiceUtil.showMessage("CrmService", "Falha ao Validar Modelo Veiculo!");
            jaExiste = true;
        }
        return jaExiste;
    }

    @Override
    public void btnFiltrarProdutoModeloClick(Event<Object> event) {
        try {
            /*if (rn.countTemposPadroesCruzadas(tbServicos.getCOD_SERVICO().asString()) >= 500 &&  cbFamiliaCad.getValue().asInteger() == 0) {
                Dialog.create().title("CrmService").message("Para iniciar a busca, selecione pelo menos uma família.").showInformation(((EventListener) event1 -> {
                    cbFamiliaCad.setFocus();
                    cbFamiliaCad.setOpen(true);
                }));
                return;
            }*/
            rn.carregaTemposPadroesCruzadas(tbServicos.getCOD_SERVICO().asString(), cbFamiliaCad.getValue().asInteger(), cbModeloCad.getValue().asInteger());
            rn.adicionarGridDadosInseridosEAtualizados(tbTemposPadroesTemp);
            FVBox46.invalidate();
        } catch (Exception e) {
            CrmServiceUtil.showError("Falha ao filtrar tempos padrões.", e);
        }
    }

    

}
