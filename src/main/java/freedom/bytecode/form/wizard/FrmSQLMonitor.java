package freedom.bytecode.form.wizard;

import freedom.client.controls.impl.*;
import freedom.client.event.Event;
import freedom.client.event.EventListener;
import freedom.client.util.ExceptionEngine;
import freedom.data.DataException;
import freedom.workflow.IWorkFlow;
import freedom.workflow.WorkFlowFactory;

public abstract class FrmSQLMonitor extends TFForm {

    private static final long serialVersionUID = 20130827081850L;
    protected freedom.bytecode.rn.SQLMonitorRNA rn = null;

    public FrmSQLMonitor() {
        try {
            rn = (freedom.bytecode.rn.SQLMonitorRNA) getRN(freedom.bytecode.rn.wizard.SQLMonitorRN.class);
        } catch (DataException e) {
            ExceptionEngine.register(e);
        }
        init_FVBox1();
        init_FGroupbox2();
        init_FHBox1();
        init_imgAtivar();
        init_FHBox2();
        init_btnAtualizar();
        init_btnLimpar();
        init_FVBox2();
        init_FLabel1();
        init_cbmTipo();
        init_FVBox3();
        init_FLabel2();
        init_cbDts();
        init_FGroupbox1();
        init_txbSqlStatements();
        init_FrmSQLMonitor();
    }

    protected TFForm FrmSQLMonitor = this;

    private void init_FrmSQLMonitor() {
        FrmSQLMonitor.setName("FrmSQLMonitor");
        FrmSQLMonitor.setCaption("Monitor de SQL");
        FrmSQLMonitor.setClientHeight(458);
        FrmSQLMonitor.setClientWidth(637);
        FrmSQLMonitor.setColor("clBtnFace");
        FrmSQLMonitor.setWKey("150014");
        FrmSQLMonitor.setSpacing(0);
        FrmSQLMonitor.applyProperties();
    }

    public TFVBox FVBox1 = new TFVBox();

    private void init_FVBox1() {
        FVBox1.setName("FVBox1");
        FVBox1.setLeft(3);
        FVBox1.setTop(4);
        FVBox1.setWidth(631);
        FVBox1.setHeight(450);
        FVBox1.setBorderStyle("stNone");
        FVBox1.setPaddingTop(0);
        FVBox1.setPaddingLeft(0);
        FVBox1.setPaddingRight(0);
        FVBox1.setPaddingBottom(0);
        FVBox1.setMarginTop(0);
        FVBox1.setMarginLeft(0);
        FVBox1.setMarginRight(0);
        FVBox1.setMarginBottom(0);
        FVBox1.setSpacing(5);
        FVBox1.setFlexVflex("ftTrue");
        FVBox1.setFlexHflex("ftTrue");
        FVBox1.setScrollable(false);
        FVBox1.setBoxShadowConfigHorizontalLength(10);
        FVBox1.setBoxShadowConfigVerticalLength(10);
        FVBox1.setBoxShadowConfigBlurRadius(5);
        FVBox1.setBoxShadowConfigSpreadRadius(0);
        FVBox1.setBoxShadowConfigShadowColor("clBlack");
        FVBox1.setBoxShadowConfigOpacity(75);
        FrmSQLMonitor.addChildren(FVBox1);
        FVBox1.applyProperties();
    }

    public TFGroupbox FGroupbox2 = new TFGroupbox();

    private void init_FGroupbox2() {
        FGroupbox2.setName("FGroupbox2");
        FGroupbox2.setLeft(0);
        FGroupbox2.setTop(0);
        FGroupbox2.setWidth(628);
        FGroupbox2.setHeight(100);
        FGroupbox2.setCaption("SQL Monitor");
        FGroupbox2.setFontColor("clWindowText");
        FGroupbox2.setFontSize(-11);
        FGroupbox2.setFontName("Tahoma");
        FGroupbox2.setFontStyle("[]");
        FGroupbox2.setFlexVflex("ftFalse");
        FGroupbox2.setFlexHflex("ftTrue");
        FGroupbox2.setScrollable(false);
        FGroupbox2.setClosable(false);
        FGroupbox2.setClosed(false);
        FGroupbox2.setOrient("coHorizontal");
        FGroupbox2.setStyle("grp3D");
        FGroupbox2.setHeaderImageId(0);
        FVBox1.addChildren(FGroupbox2);
        FGroupbox2.applyProperties();
    }

    public TFHBox FHBox1 = new TFHBox();

    private void init_FHBox1() {
        FHBox1.setName("FHBox1");
        FHBox1.setLeft(5);
        FHBox1.setTop(10);
        FHBox1.setWidth(616);
        FHBox1.setHeight(68);
        FHBox1.setBorderStyle("stNone");
        FHBox1.setPaddingTop(0);
        FHBox1.setPaddingLeft(0);
        FHBox1.setPaddingRight(0);
        FHBox1.setPaddingBottom(0);
        FHBox1.setMarginTop(0);
        FHBox1.setMarginLeft(0);
        FHBox1.setMarginRight(0);
        FHBox1.setMarginBottom(0);
        FHBox1.setSpacing(3);
        FHBox1.setFlexVflex("ftTrue");
        FHBox1.setFlexHflex("ftTrue");
        FHBox1.setScrollable(false);
        FHBox1.setBoxShadowConfigHorizontalLength(10);
        FHBox1.setBoxShadowConfigVerticalLength(10);
        FHBox1.setBoxShadowConfigBlurRadius(5);
        FHBox1.setBoxShadowConfigSpreadRadius(0);
        FHBox1.setBoxShadowConfigShadowColor("clBlack");
        FHBox1.setBoxShadowConfigOpacity(75);
        FGroupbox2.addChildren(FHBox1);
        FHBox1.applyProperties();
    }

    public TFImage imgAtivar = new TFImage();

    private void init_imgAtivar() {
        imgAtivar.setName("imgAtivar");
        imgAtivar.setLeft(0);
        imgAtivar.setTop(0);
        imgAtivar.setWidth(108);
        imgAtivar.setHeight(50);
        imgAtivar.setHint("Habilitar/Desabilitar");
        imgAtivar.addEventListener("onClick", (EventListener<Event<Object>>) (Event<Object> event) -> {
            imgAtivarClick(event);
            processarFlow("FrmSQLMonitor", "imgAtivar", "OnClick");
        });
        imgAtivar.setImageSrc("freedom/bytecode/images/switch_off.png");
        imgAtivar.setBoxSize(0);
        imgAtivar.setGrayScaleOnDisable(false);
        FHBox1.addChildren(imgAtivar);
        imgAtivar.applyProperties();
    }

    public TFHBox FHBox2 = new TFHBox();

    private void init_FHBox2() {
        FHBox2.setName("FHBox2");
        FHBox2.setLeft(108);
        FHBox2.setTop(0);
        FHBox2.setWidth(41);
        FHBox2.setHeight(41);
        FHBox2.setBorderStyle("stNone");
        FHBox2.setPaddingTop(0);
        FHBox2.setPaddingLeft(0);
        FHBox2.setPaddingRight(0);
        FHBox2.setPaddingBottom(0);
        FHBox2.setMarginTop(0);
        FHBox2.setMarginLeft(0);
        FHBox2.setMarginRight(0);
        FHBox2.setMarginBottom(0);
        FHBox2.setSpacing(1);
        FHBox2.setFlexVflex("ftFalse");
        FHBox2.setFlexHflex("ftTrue");
        FHBox2.setScrollable(false);
        FHBox2.setBoxShadowConfigHorizontalLength(10);
        FHBox2.setBoxShadowConfigVerticalLength(10);
        FHBox2.setBoxShadowConfigBlurRadius(5);
        FHBox2.setBoxShadowConfigSpreadRadius(0);
        FHBox2.setBoxShadowConfigShadowColor("clBlack");
        FHBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FHBox2);
        FHBox2.applyProperties();
    }

    public TFImage btnAtualizar = new TFImage();

    private void init_btnAtualizar() {
        btnAtualizar.setName("btnAtualizar");
        btnAtualizar.setLeft(149);
        btnAtualizar.setTop(0);
        btnAtualizar.setWidth(50);
        btnAtualizar.setHeight(50);
        btnAtualizar.setHint("Atualizar");
        btnAtualizar.addEventListener("onClick", (EventListener<Event<Object>>) (Event<Object> event) -> {
            btnAtualizarClick(event);
            processarFlow("FrmSQLMonitor", "btnAtualizar", "OnClick");
        });
        btnAtualizar.setImageSrc("freedom/bytecode/images/refresh_monitor.png");
        btnAtualizar.setBoxSize(0);
        btnAtualizar.setGrayScaleOnDisable(false);
        FHBox1.addChildren(btnAtualizar);
        btnAtualizar.applyProperties();
    }

    public TFImage btnLimpar = new TFImage();

    private void init_btnLimpar() {
        btnLimpar.setName("btnLimpar");
        btnLimpar.setLeft(199);
        btnLimpar.setTop(0);
        btnLimpar.setWidth(50);
        btnLimpar.setHeight(50);
        btnLimpar.setHint("Limpar");
        btnLimpar.addEventListener("onClick", (EventListener<Event<Object>>) (Event<Object> event) -> {
            btnLimparClick(event);
            processarFlow("FrmSQLMonitor", "btnLimpar", "OnClick");
        });
        btnLimpar.setImageSrc("freedom/bytecode/images/clear_monitor.png");
        btnLimpar.setBoxSize(0);
        btnLimpar.setGrayScaleOnDisable(false);
        FHBox1.addChildren(btnLimpar);
        btnLimpar.applyProperties();
    }

    public TFVBox FVBox2 = new TFVBox();

    private void init_FVBox2() {
        FVBox2.setName("FVBox2");
        FVBox2.setLeft(249);
        FVBox2.setTop(0);
        FVBox2.setWidth(168);
        FVBox2.setHeight(52);
        FVBox2.setBorderStyle("stRaised");
        FVBox2.setPaddingTop(0);
        FVBox2.setPaddingLeft(0);
        FVBox2.setPaddingRight(0);
        FVBox2.setPaddingBottom(0);
        FVBox2.setMarginTop(0);
        FVBox2.setMarginLeft(0);
        FVBox2.setMarginRight(0);
        FVBox2.setMarginBottom(0);
        FVBox2.setSpacing(3);
        FVBox2.setFlexVflex("ftFalse");
        FVBox2.setFlexHflex("ftFalse");
        FVBox2.setScrollable(false);
        FVBox2.setBoxShadowConfigHorizontalLength(10);
        FVBox2.setBoxShadowConfigVerticalLength(10);
        FVBox2.setBoxShadowConfigBlurRadius(5);
        FVBox2.setBoxShadowConfigSpreadRadius(0);
        FVBox2.setBoxShadowConfigShadowColor("clBlack");
        FVBox2.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox2);
        FVBox2.applyProperties();
    }

    public TFLabel FLabel1 = new TFLabel();

    private void init_FLabel1() {
        FLabel1.setName("FLabel1");
        FLabel1.setLeft(1);
        FLabel1.setTop(1);
        FLabel1.setWidth(91);
        FLabel1.setHeight(13);
        FLabel1.setCaption("Filtro de senten\u00E7as");
        FLabel1.setFontColor("clWindowText");
        FLabel1.setFontSize(-11);
        FLabel1.setFontName("Tahoma");
        FLabel1.setFontStyle("[]");
        FLabel1.setVerticalAlignment("taAlignTop");
        FVBox2.addChildren(FLabel1);
        FLabel1.applyProperties();
    }

    public TFCombo cbmTipo = new TFCombo();

    private void init_cbmTipo() {
        cbmTipo.setName("cbmTipo");
        cbmTipo.setLeft(1);
        cbmTipo.setTop(15);
        cbmTipo.setWidth(165);
        cbmTipo.setHeight(21);
        cbmTipo.setFlex(false);
        cbmTipo.setListOptions("Todos=T;Select=S;Insert=I;Update=U;Delete=D");
        cbmTipo.setRequired(false);
        cbmTipo.setPrompt("Selecione");
        cbmTipo.addEventListener("onChange", (EventListener<Event<Object>>) (Event<Object> event) -> {
            cbmTipoChange(event);
            processarFlow("FrmSQLMonitor", "cbmTipo", "OnChange");
        });
        cbmTipo.setConstraintCheckWhen("cwImmediate");
        cbmTipo.setConstraintCheckType("ctExpression");
        cbmTipo.setConstraintFocusOnError(false);
        cbmTipo.setConstraintEnableUI(true);
        cbmTipo.setConstraintEnabled(false);
        cbmTipo.setConstraintFormCheck(true);
        FVBox2.addChildren(cbmTipo);
        cbmTipo.applyProperties();
        addValidatable(cbmTipo);
    }

    public TFVBox FVBox3 = new TFVBox();

    private void init_FVBox3() {
        FVBox3.setName("FVBox3");
        FVBox3.setLeft(417);
        FVBox3.setTop(0);
        FVBox3.setWidth(168);
        FVBox3.setHeight(52);
        FVBox3.setBorderStyle("stRaised");
        FVBox3.setPaddingTop(0);
        FVBox3.setPaddingLeft(0);
        FVBox3.setPaddingRight(0);
        FVBox3.setPaddingBottom(0);
        FVBox3.setMarginTop(0);
        FVBox3.setMarginLeft(0);
        FVBox3.setMarginRight(0);
        FVBox3.setMarginBottom(0);
        FVBox3.setSpacing(3);
        FVBox3.setFlexVflex("ftFalse");
        FVBox3.setFlexHflex("ftFalse");
        FVBox3.setScrollable(false);
        FVBox3.setBoxShadowConfigHorizontalLength(10);
        FVBox3.setBoxShadowConfigVerticalLength(10);
        FVBox3.setBoxShadowConfigBlurRadius(5);
        FVBox3.setBoxShadowConfigSpreadRadius(0);
        FVBox3.setBoxShadowConfigShadowColor("clBlack");
        FVBox3.setBoxShadowConfigOpacity(75);
        FHBox1.addChildren(FVBox3);
        FVBox3.applyProperties();
    }

    public TFLabel FLabel2 = new TFLabel();

    private void init_FLabel2() {
        FLabel2.setName("FLabel2");
        FLabel2.setLeft(1);
        FLabel2.setTop(1);
        FLabel2.setWidth(55);
        FLabel2.setHeight(13);
        FLabel2.setCaption("Datasource");
        FLabel2.setFontColor("clWindowText");
        FLabel2.setFontSize(-11);
        FLabel2.setFontName("Tahoma");
        FLabel2.setFontStyle("[]");
        FLabel2.setVerticalAlignment("taAlignTop");
        FVBox3.addChildren(FLabel2);
        FLabel2.applyProperties();
    }

    public TFCombo cbDts = new TFCombo();

    private void init_cbDts() {
        cbDts.setName("cbDts");
        cbDts.setLeft(1);
        cbDts.setTop(15);
        cbDts.setWidth(165);
        cbDts.setHeight(21);
        cbDts.setFlex(false);
        cbDts.setListOptions("Dados=D;Dicion\u00E1rio=I");
        cbDts.setRequired(false);
        cbDts.setPrompt("Selecione");
        cbDts.addEventListener("onChange", (EventListener<Event<Object>>) (Event<Object> event) -> {
            cbDtsChange(event);
            processarFlow("FrmSQLMonitor", "cbDts", "OnChange");
        });
        cbDts.setConstraintCheckWhen("cwImmediate");
        cbDts.setConstraintCheckType("ctExpression");
        cbDts.setConstraintFocusOnError(false);
        cbDts.setConstraintEnableUI(true);
        cbDts.setConstraintEnabled(false);
        cbDts.setConstraintFormCheck(true);
        FVBox3.addChildren(cbDts);
        cbDts.applyProperties();
        addValidatable(cbDts);
    }

    public TFGroupbox FGroupbox1 = new TFGroupbox();

    private void init_FGroupbox1() {
        FGroupbox1.setName("FGroupbox1");
        FGroupbox1.setLeft(0);
        FGroupbox1.setTop(101);
        FGroupbox1.setWidth(591);
        FGroupbox1.setHeight(348);
        FGroupbox1.setCaption("Log");
        FGroupbox1.setFontColor("clWindowText");
        FGroupbox1.setFontSize(-11);
        FGroupbox1.setFontName("Tahoma");
        FGroupbox1.setFontStyle("[]");
        FGroupbox1.setFlexVflex("ftTrue");
        FGroupbox1.setFlexHflex("ftTrue");
        FGroupbox1.setScrollable(false);
        FGroupbox1.setClosable(false);
        FGroupbox1.setClosed(false);
        FGroupbox1.setOrient("coHorizontal");
        FGroupbox1.setStyle("grp3D");
        FGroupbox1.setHeaderImageId(0);
        FVBox1.addChildren(FGroupbox1);
        FGroupbox1.applyProperties();
    }

    public TFMemo txbSqlStatements = new TFMemo();

    private void init_txbSqlStatements() {
        txbSqlStatements.setName("txbSqlStatements");
        txbSqlStatements.setLeft(8);
        txbSqlStatements.setTop(17);
        txbSqlStatements.setWidth(575);
        txbSqlStatements.setHeight(324);
        txbSqlStatements.setCharCase("ccNormal");
        txbSqlStatements.setFontColor("clWindowText");
        txbSqlStatements.setFontSize(-13);
        txbSqlStatements.setFontName("Courier New");
        txbSqlStatements.setFontStyle("[]");
        txbSqlStatements.setMaxlength(0);
        txbSqlStatements.setReadOnly(true);
        txbSqlStatements.setFlexVflex("ftTrue");
        txbSqlStatements.setFlexHflex("ftTrue");
        txbSqlStatements.setConstraintCheckWhen("cwImmediate");
        txbSqlStatements.setConstraintCheckType("ctExpression");
        txbSqlStatements.setConstraintFocusOnError(false);
        txbSqlStatements.setConstraintEnableUI(true);
        txbSqlStatements.setConstraintEnabled(false);
        txbSqlStatements.setConstraintFormCheck(true);
        FGroupbox1.addChildren(txbSqlStatements);
        txbSqlStatements.applyProperties();
        addValidatable(txbSqlStatements);
    }

    protected final void processarFlow(String nomeForm, String nomeComp, String action) {
        IWorkFlow flow = WorkFlowFactory.getInstance();
        flow.continueFlow(nomeForm, nomeComp, action);
    }

    @SuppressWarnings("unchecked")
    protected final <T extends freedom.commons.lang.IRegraNegocio> T getRN(Class<T> rn) throws DataException {
        try {
            try {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("U")) {
                    cn += "U";
                }
                return (T) Class.forName(cn).newInstance();
            } catch (ClassNotFoundException e) {
                String cn = "freedom.bytecode.rn." + rn.getSimpleName();
                if (!rn.getSimpleName().endsWith("A")) {
                    cn += "A";
                }
                return (T) Class.forName(cn).newInstance();
            }
        } catch (InstantiationException | IllegalAccessException | ClassNotFoundException e) {
            throw new DataException(e.getMessage(), e);
        }
    }

    public abstract void imgAtivarClick(final Event<Object> event);

    public abstract void btnAtualizarClick(final Event<Object> event);

    public abstract void btnLimparClick(final Event<Object> event);

    public abstract void cbmTipoChange(final Event<Object> event);

    public abstract void cbDtsChange(final Event<Object> event);

}