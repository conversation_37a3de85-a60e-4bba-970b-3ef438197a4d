package freedom.bytecode.form;

import freedom.bytecode.form.wizard.FrmSQLMonitorW;
import freedom.client.event.Event;
import freedom.client.util.ExceptionEngine;
import freedom.commons.lang.IWorkList;
import freedom.util.WorkListFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.InputStream;

public class FrmS<PERSON>M<PERSON>tor<PERSON> extends FrmSQLMonitorW {

    private final IWorkList worklist;
    private boolean isActive = false;

    private static BufferedImage SWITCH_ON;
    private static BufferedImage SWITCH_OFF;
    private static BufferedImage REFRESH;
    private static BufferedImage CLEAR;

    static {
        try {
            try (InputStream resource = FrmSQLMonitorA.class
                    .getResourceAsStream("images/clear_monitor.png")) {
                CLEAR = ImageIO.read(resource);
            }
            try (InputStream resource = FrmSQLMonitorA.class
                    .getResourceAsStream("images/switch_on.png")) {
                SWITCH_ON = ImageIO.read(resource);
            }
            try (InputStream resource = FrmSQLMonitorA.class
                    .getResourceAsStream("images/switch_off.png")) {
                SWITCH_OFF = ImageIO.read(resource);
            }
            try (InputStream resource = FrmSQLMonitorA.class
                    .getResourceAsStream("images/refresh_monitor.png")) {
                REFRESH = ImageIO.read(resource);
            }
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
    }

    public FrmSQLMonitorA() {
        super();
        worklist = WorkListFactory.getInstance();
        btnAtualizar.setEnabled(false);
        btnLimpar.setEnabled(false);

        try {
            btnLimpar.setImageContent(CLEAR);
            btnAtualizar.setImageContent(REFRESH);
            imgAtivar.setImageContent(SWITCH_OFF);
        } catch (Exception e) {
            ExceptionEngine.register(e);
        }
        cbmTipo.setEnabled(false);
        cbmTipo.setValue("T");
        cbDts.setValue("D");
        cbDts.setEnabled(false);
        FrmSQLMonitor.setWKey("1");
    }

    @Override
    public void imgAtivarClick(final Event event) {
        try {
            clear();
            if (isActive) {
                //imgAtivar.setImageSrc("images/switch_off.png");
                imgAtivar.setImageContent(SWITCH_OFF);
                worklist.put("SQL_MONITOR_ATIVO", "N");
                isActive = false;
            } else {
                //imgAtivar.setImageSrc("images/switch_on.png");
                imgAtivar.setImageContent(SWITCH_ON);
                worklist.put("SQL_MONITOR_ATIVO", "S");
                refresh();
                isActive = true;
            }
            btnAtualizar.setEnabled(isActive);
            btnLimpar.setEnabled(isActive);
            cbmTipo.setEnabled(isActive);
            cbDts.setEnabled(isActive);
        } catch (Exception e) {
            ExceptionEngine.register(e);
        }
    }

    private void refresh() {
        txbSqlStatements.setValue("");
        boolean isDic = cbDts.getValue().equals("I");
        String query = "SQL_MONITOR_QUERY" + (isDic ? "DIC" : "");
        String insert = "SQL_MONITOR_INSERT" + (isDic ? "DIC" : "");
        String update = "SQL_MONITOR_UPDATE" + (isDic ? "DIC" : "");
        String delete = "SQL_MONITOR_DELETE" + (isDic ? "DIC" : "");
        String commands = "SQL_MONITOR_COMMANDS" + (isDic ? "DIC" : "");

        switch (cbmTipo.getValue().asString()) {
            case "S":
                txbSqlStatements.setValue(worklist.get(query));
                break;
            case "I":
                txbSqlStatements.setValue(worklist.get(insert));
                break;
            case "D":
                txbSqlStatements.setValue(worklist.get(delete));
                break;
            case "U":
                txbSqlStatements.setValue(worklist.get(update));
                break;
            default:
                txbSqlStatements.setValue(worklist.get(commands));
        }
    }

    @Override
    public void btnAtualizarClick(final Event event) {
        if (btnAtualizar.isEnabled()) {
            refresh();
        }
    }

    @Override
    public void btnLimparClick(final Event event) {
        if (btnLimpar.isEnabled()) {
            clear();
        }
    }

    private void clear() {
        txbSqlStatements.setValue("");

        boolean isDic = cbDts.getValue().equals("I");
        String query = "SQL_MONITOR_QUERY" + (isDic ? "DIC" : "");
        String insert = "SQL_MONITOR_INSERT" + (isDic ? "DIC" : "");
        String update = "SQL_MONITOR_UPDATE" + (isDic ? "DIC" : "");
        String delete = "SQL_MONITOR_DELETE" + (isDic ? "DIC" : "");
        String commands = "SQL_MONITOR_COMMANDS" + (isDic ? "DIC" : "");

        worklist.put(query, "");
        worklist.put(insert, "");
        worklist.put(delete, "");
        worklist.put(update, "");
        worklist.put(commands, "");
    }

    @Override
    public void cbmTipoChange(final Event event) {
        if (cbmTipo.isEnabled()) {
            refresh();
        }
    }

    @Override
    public void cbDtsChange(final Event<Object> event) {
        if (cbDts.isEnabled()) {
            refresh();
        }
    }
}
