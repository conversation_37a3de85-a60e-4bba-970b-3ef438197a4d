package freedom.webservice;

import java.io.IOException;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

/**
 *
 * <AUTHOR>
 */
public class AWebService {

    /**
     * Realiza uma chamada a um webservice
     *
     * @param url Endereco do webservice
     * @param soapAction SOAP Action para o webservice
     * @param envelope XML, em string, do SOAP Envelope que sera utilizado para
     * realizar a chamada do servico
     * @return xml em String com a resposta da execução do webservice
     * @throws IOException Erros em geral durante a chamada: IOException,
     */
    protected String callWebService(String url, String soapAction,
            String envelope) throws IOException {
        return callWebService(url, soapAction, envelope, null, 0, null, null, false);
    }

    /**
     * Realiza uma chamada a um webservice
     *
     * @param url Endereco do webservice
     * @param soapAction SOAP Action para o webservice
     * @param envelope XML, em string, do SOAP Envelope que sera utilizado para
     * realizar a chamada do servico
     * @param useSystemProxy Indica se será usado as configurações de sistema
     * para o proxy
     * @return xml em String com a resposta da execução do webservice
     * @throws IOException Erros em geral durante a chamada: IOException,
     */
    protected String callWebService(String url, String soapAction,
            String envelope, boolean useSystemProxy) throws IOException {
        return callWebService(url, soapAction, envelope, null, 0, null, null,
                useSystemProxy);
    }

    /**
     * Realiza uma chamada a um webservice
     *
     * @param url Endereco do webservice
     * @param soapAction SOAP Action para o webservice
     * @param envelope XML, em string, do SOAP Envelope que sera utilizado para
     * realizar a chamada do servico
     * @param proxyHost Host do servidor proxy
     * @param proxyPort Porta do servidor proxy
     * @param proxyUsername Nome do usuário para autenticação no servidor proxy
     * @param proxyPassword Senha de autenticação no servidor proxy
     * @return xml em String com a resposta da execução do webservice
     * @throws IOException Erros em geral durante a chamada: IOException,
     */
    protected String callWebService(String url, String soapAction,
            String envelope, String proxyHost, int proxyPort, String proxyUsername,
            String proxyPassword) throws IOException {
        return callWebService(url, soapAction, envelope, proxyHost, proxyPort,
                proxyUsername, proxyPassword, false);
    }

    private String callWebService(String url, String soapAction,
            String envelope, String proxyHost, int proxyPort, String proxyUsername,
            String proxyPassword, boolean useSystemProxy) throws IOException {
        final CloseableHttpClient httpClient;
        if (useSystemProxy) {
            httpClient = HttpClients.createSystem();
        } else {
            httpClient = HttpClients.createDefault();
        }
        // POST the envelope
        HttpPost httppost = new HttpPost(url);
        // add headers
        if (soapAction != null) {
            httppost.setHeader("soapaction", soapAction);
        }
        httppost.setHeader("Content-Type", "text/xml; charset=utf-8");

        String responseString;

        // the entity holds the request
        HttpEntity entity = new StringEntity(envelope);
        httppost.setEntity(entity);

        // Response handler
        ResponseHandler<String> rh = (HttpResponse response) -> {
            // get response entity
            HttpEntity entity1 = response.getEntity();
            // read the response as byte array
            StringBuilder out = new StringBuilder();
            byte[] b = EntityUtils.toByteArray(entity1);
            // write the response byte array to a string buffer
            out.append(new String(b, 0, b.length));
            return out.toString();
        }; // invoked when client receives response

        HttpClientContext context = HttpClientContext.create();
        if (!useSystemProxy && proxyHost != null && proxyPort > 0 && proxyUsername != null && proxyPassword != null) {
            HttpHost proxy = new HttpHost(proxyHost, proxyPort);
            RequestConfig config = RequestConfig.custom()
                    .setProxy(proxy)
                    .setExpectContinueEnabled(true)
                    .build();
            httppost.setConfig(config);

            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(proxyUsername, proxyPassword));

            context.setCredentialsProvider(credentialsProvider);
        }

        responseString = httpClient.execute(httppost, rh, context);

        // close the connection
        httpClient.close();
        return responseString;
    }
}
