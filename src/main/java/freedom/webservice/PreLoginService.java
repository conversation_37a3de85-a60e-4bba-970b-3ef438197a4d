package freedom.webservice;

import freedom.commons.lang.IWorkList;
import freedom.util.FRLogger;
import freedom.util.WorkListFactory;
import org.json.JSONObject;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Instant;

public class PreLoginService {

    private final IWorkList wl = WorkListFactory.getInstance();

    public void logsLoginHash(String msg) {
        FRLogger.log("----------inicio log------------",
                this.getClass());
        FRLogger.log(msg,
                this.getClass());
        FRLogger.log("------------fim log------------",
                this.getClass());
    }

    public String loginHash(String urlHash,
                            String senha,
                            String schema) {
        FRLogger.log("Executando rotina de login automatico atraves de hash",
                this.getClass());
        String loginMsgError = "Nao foi possivel Logar automaticamente";
        try {
            String nbsServidorId = null;
            String infraURL;
            try {
                FRLogger.log("Buscando valor da variavel de ambiente NBS_SERVIDOR_ID",
                        this.getClass());
                nbsServidorId = System.getenv("NBS_SERVIDOR_ID");
                FRLogger.log(("Valor para NBS_SERVIDOR_ID: "
                                + nbsServidorId),
                        this.getClass());
            } catch (Exception ignored) {
                FRLogger.log("Nao foi possivel obter o valor da variavel de ambiente NBS_SERVIDOR_ID",
                        this.getClass());
                ignored.printStackTrace();
            }
            if (nbsServidorId == null || nbsServidorId.isEmpty() || nbsServidorId.equals("0")) {
                FRLogger.log(" NBS_SERVIDOR_ID nao localizado, desta forma sera utilizada a variavel URL_INFRA do arquivo fserver.xml",
                        this.getClass());
                String fserverLink = wl.sysget("URL_INFRA").asString();
                if (fserverLink.endsWith("/")) {
                    fserverLink = ((fserverLink.contains("nbs-infra")) ? (fserverLink) : (fserverLink + "nbs-infra"));
                } else {
                    fserverLink = ((fserverLink.contains("/nbs-infra")) ? (fserverLink) : (fserverLink + "/nbs-infra"));
                }
                fserverLink = ((fserverLink.contains("/security/validtoken")) ? (fserverLink) : (fserverLink + "/security/validtoken"));
                infraURL = fserverLink;
            } else {
                infraURL = "http://kong-srv" + nbsServidorId + ":8000/nbs-infra/security/validtoken";
            }
            FRLogger.log(("url_infra a ser utilizada: "
                            + infraURL),
                    this.getClass());
            if (infraURL.toUpperCase().contains(("HTTP").toUpperCase())) {
                HttpResponse response = verificarTokenValido(infraURL,
                        urlHash);
                if (response != null) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("tokenObj", new JSONObject(response.body().toString()));
                    JSONObject userJson = jsonObject.getJSONObject("tokenObj");
                    String usuario = userJson.getString("usuario");
                    boolean valido = userJson.getBoolean("valido");
                    String datasource = userJson.getString("datasource");
                    if (valido) {
                        long tempoExpiracaoMS = 60000;
                        long expiracao = Instant.now().plusMillis(tempoExpiracaoMS).toEpochMilli();
                        return datasource + "|" + schema + "|" + usuario + "|" + expiracao + "|" + senha;
                    }
                } else {
                    throw new Exception(loginMsgError);
                }
            }
        } catch (Exception exception) {
            return "Não foi possível Logar automaticamente";
        }
        return loginMsgError;
    }

    private HttpResponse verificarTokenValido(String url,
                                              String token) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("token", token);
            HttpRequest request = HttpRequest.newBuilder()
                    .POST(HttpRequest.BodyPublishers.ofString(jsonObject.toString()))
                    .uri(URI.create(url))
                    .header("Content-Type", "application/json")
                    .build();
            HttpClient httpClient = HttpClient.newBuilder()
                    .build();
            return httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        } catch (Exception exception) {
            FRLogger.log(("Falha na requisicao para: "
                            + url),
                    this.getClass());
            exception.printStackTrace();
            return null;
        }
    }

}
