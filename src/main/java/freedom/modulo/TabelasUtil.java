/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package freedom.modulo;

import freedom.client.controls.impl.TFCheckBox;
import freedom.client.controls.impl.TFCombo;
import freedom.client.controls.impl.TFDecimal;

public class TabelasUtil {

    public static void setEnableCombo(TFCombo combo, boolean habilitar) {
        combo.setEnabled(habilitar);
        if (habilitar || true) {
            combo.setValue(null);
        }
    }

    public static void setEnableDecimal(TFDecimal campo, boolean habilitar) {
        campo.setEnabled(habilitar);
        if (habilitar || true) {
            campo.setValue(null);
        }
    }

    public static void setEnableCheckbox(TFCheckBox campo, boolean habilitar) {
        campo.setEnabled(habilitar);
        if (habilitar || true) {
            campo.setValue(null);
        }
    }

}
