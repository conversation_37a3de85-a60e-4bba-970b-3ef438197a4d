<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>nbs-shared</groupId>
    <artifactId>nbs-util-zk</artifactId>
    <version>**********.L35586</version>
    <packaging>jar</packaging>
    <description>Projeto Util</description>
    <properties>
        <project.build.sourceEncoding>windows-1252</project.build.sourceEncoding>
        <endorsed.dir>${project.build.directory}/endorsed</endorsed.dir>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <freedom.version>*******</freedom.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>br.com.nbs.freedom</groupId>
            <artifactId>freedom_zk_components</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>br.com.nbs.freedom</groupId>
            <artifactId>nbs</artifactId>
            <version>*******</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>br.com.nbs.freedom</groupId>
            <artifactId>nbsmobile</artifactId>
            <version>*******</version>
            <type>jar</type>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>nbs-shared</groupId>
            <artifactId>cursores-zk</artifactId>
            <version>1.12.0.255.L35586</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>central</id>
            <name>b60e1586178c-releases</name>
            <url>https://jfrog.nbsi.com.br:8091/artifactory/artifactory</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>b60e1586178c-snapshots</name>
            <url>https://jfrog.nbsi.com.br:8091/artifactory/artifactory</url>
        </snapshotRepository>
    </distributionManagement>

</project>
